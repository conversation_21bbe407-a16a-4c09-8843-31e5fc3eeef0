#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : main_打包.py
@Software: PyCharm
@Modify Time      <AUTHOR>    @Description
------------      -------    --------    -----------
2024/12/27        fz         1.0         CSV转Excel打包工具
"""

import os
import shutil
import zipfile
import pandas as pd
from pathlib import Path
import traceback
from datetime import datetime
import tempfile


class CSVToExcelPackager:
    """CSV转Excel打包处理器"""
    
    def __init__(self, base_path):
        """
        初始化打包器
        
        Args:
            base_path (str): 基础路径，main.py输出文件的根目录
        """
        self.base_path = Path(base_path)
        self.date_suffix = "_20250801"
        self.temp_dir = None
        self.processed_count = 0
        self.error_count = 0
        
    def setup_temp_directory(self):
        """创建临时工作目录"""
        try:
            self.temp_dir = Path(tempfile.mkdtemp(prefix="csv_excel_"))
            print(f"创建临时工作目录: {self.temp_dir}")
            return True
        except Exception as e:
            print(f"创建临时目录失败: {e}")
            return False
    
    def csv_to_excel(self, csv_file_path, excel_file_path):
        """
        将CSV文件转换为Excel文件
        
        Args:
            csv_file_path (Path): CSV文件路径
            excel_file_path (Path): 目标Excel文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            # 第一步：使用预处理器修复CSV格式
            from csv_preprocessor import CSVPreprocessor
            
            preprocessor = CSVPreprocessor()
            
            # 创建临时文件来存储修复后的CSV
            with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix='.csv', delete=False) as temp_file:
                temp_path = temp_file.name
            
            # 修复CSV格式
            print(f"  预处理CSV文件...")
            if not preprocessor.fix_csv_file(csv_file_path, temp_path):
                print(f"  CSV预处理失败")
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                return False
            
            # 第二步：读取修复后的CSV
            try:
                df = pd.read_csv(temp_path, encoding='utf-8')
                print(f"  成功读取修复后的CSV，共{len(df)}行数据")
            except Exception as e:
                print(f"  读取CSV失败: {e}")
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                return False
            finally:
                # 删除临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            
            # 确保目标目录存在
            excel_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为Excel并设置格式
            from openpyxl.styles import Alignment
            
            with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='数据')
                
                # 获取工作表并设置单元格格式
                worksheet = writer.sheets['数据']
                
                # 设置所有单元格自动换行和对齐
                for row in worksheet.iter_rows():
                    for cell in row:
                        if cell.value is not None:
                            cell.alignment = Alignment(
                                wrap_text=True,  # 自动换行
                                vertical='top',  # 垂直对齐到顶部
                                horizontal='left'  # 水平左对齐
                            )
                
                # 调整列宽
                for column_cells in worksheet.columns:
                    length = max(len(str(cell.value) if cell.value else '') for cell in column_cells)
                    adjusted_width = min(max(length * 0.1, 15), 100)
                    worksheet.column_dimensions[column_cells[0].column_letter].width = adjusted_width
            
            print(f"  ✓ 转换成功: {csv_file_path.name} -> {excel_file_path.name}")
            self.processed_count += 1
            return True
            
        except Exception as e:
            print(f"  ✗ 转换失败 {csv_file_path.name}: {e}")
            traceback.print_exc()
            self.error_count += 1
            return False
    
    def _is_title_context(self, prev_chars, next_chars):
        """判断是否为标题上下文，决定是否保留换行符"""
        # 更精确的标题识别逻辑
        
        # 合并前后文本进行分析
        full_context = prev_chars + next_chars
        
        # 1. 如果前面有明显的段落标识，保持换行
        paragraph_indicators = ['一、', '二、', '三、', '四、', '五、', '六、', '七、', '八、', '九、', '十、',
                              '（一）', '（二）', '（三）', '（四）', '（五）', '（六）', '（七）', '（八）', '（九）', '（十）',
                              '1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.',
                              '1、', '2、', '3、', '4、', '5、', '6、', '7、', '8、', '9、', '10、']
        
        for indicator in paragraph_indicators:
            if indicator in prev_chars[-10:] or indicator in next_chars[:10]:
                return False
        
        # 2. 如果前面有句号等结束标点，可能是段落分隔，保持换行
        if any(punct in prev_chars[-5:] for punct in ['。', '；', '！', '？']):
            return False
        
        # 3. 检查是否是标题的特征模式
        title_patterns = [
            r'.*年.*半年工作总结',
            r'.*年.*半年工作计划', 
            r'.*年.*半年工作安排',
            r'.*年工作总结',
            r'.*年工作计划',
            r'.*年工作安排',
            r'.*局.*年.*工作',
            r'.*县.*局.*工作',
            r'.*市.*局.*工作'
        ]
        
        import re
        for pattern in title_patterns:
            if re.search(pattern, full_context):
                # 如果匹配标题模式，且文本相对较短，认为是标题
                if len(full_context) < 200:
                    return True
        
        # 4. 特殊情况：如果前面是引号开始或CSV开始，且内容像标题
        if (prev_chars.strip() == '"' or len(prev_chars.strip()) < 5) and \
           any(keyword in full_context for keyword in ['工作总结', '工作计划', '工作安排']) and \
           len(full_context) < 150:
            return True
        
        # 5. 如果前面只有空白字符或引号，后面是标题式内容
        if prev_chars.strip() in ['', '"'] and \
           any(keyword in next_chars[:50] for keyword in ['年', '工作', '总结', '计划', '安排', '局', '县', '市']):
            return True
            
        # 默认保持换行（保守策略）
        return False
    
    def process_folder(self, folder_path):
        """
        处理单个文件夹，将其中的CSV文件转换为Excel
        
        Args:
            folder_path (Path): 文件夹路径
            
        Returns:
            Path: 处理后的文件夹路径，如果失败返回None
        """
        try:
            folder_name = folder_path.name
            new_folder_name = folder_name + self.date_suffix
            new_folder_path = self.temp_dir / new_folder_name
            
            print(f"\n处理文件夹: {folder_name}")
            
            # 创建新文件夹
            new_folder_path.mkdir(parents=True, exist_ok=True)
            
            # 查找所有CSV文件
            csv_files = list(folder_path.glob("*.csv"))
            
            if not csv_files:
                print(f"  文件夹中没有找到CSV文件")
                return None
            
            print(f"  发现 {len(csv_files)} 个CSV文件")
            
            # 转换每个CSV文件
            success_count = 0
            for csv_file in csv_files:
                # 生成Excel文件名
                excel_filename = csv_file.stem + ".xlsx"
                excel_path = new_folder_path / excel_filename
                
                if self.csv_to_excel(csv_file, excel_path):
                    success_count += 1
            
            if success_count > 0:
                print(f"  文件夹处理完成: {success_count}/{len(csv_files)} 个文件转换成功")
                return new_folder_path
            else:
                print(f"  文件夹处理失败: 没有文件转换成功")
                return None
                
        except Exception as e:
            print(f"处理文件夹 {folder_path.name} 时出错: {e}")
            traceback.print_exc()
            return None
    
    def create_zip_package(self, processed_folders, output_zip_path):
        """
        创建ZIP压缩包
        
        Args:
            processed_folders (list): 已处理的文件夹路径列表
            output_zip_path (Path): 输出ZIP文件路径
            
        Returns:
            bool: 是否成功创建ZIP
        """
        try:
            print(f"\n创建ZIP压缩包: {output_zip_path}")
            
            with zipfile.ZipFile(output_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for folder_path in processed_folders:
                    # 添加文件夹中的所有文件到ZIP
                    for file_path in folder_path.rglob("*"):
                        if file_path.is_file():
                            # 计算ZIP中的相对路径
                            arcname = file_path.relative_to(self.temp_dir)
                            zipf.write(file_path, arcname)
                            
            print(f"✓ ZIP创建成功: {output_zip_path}")
            print(f"  压缩包大小: {output_zip_path.stat().st_size / 1024 / 1024:.2f} MB")
            return True
            
        except Exception as e:
            print(f"✗ 创建ZIP失败: {e}")
            traceback.print_exc()
            return False
    
    def cleanup_temp_directory(self):
        """清理临时目录"""
        try:
            if self.temp_dir and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                print(f"清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"清理临时目录失败: {e}")
    
    def run(self, output_dir=None):
        """
        执行完整的转换和打包流程
        
        Args:
            output_dir (str): 输出目录，默认为基础路径
            
        Returns:
            bool: 是否成功完成所有操作
        """
        start_time = datetime.now()
        print(f"=== CSV转Excel打包工具启动 ===")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"基础路径: {self.base_path}")
        
        try:
            # 检查基础路径是否存在
            if not self.base_path.exists():
                print(f"错误: 基础路径不存在: {self.base_path}")
                return False
            
            # 创建临时工作目录
            if not self.setup_temp_directory():
                return False
            
            # 查找所有包含CSV的子文件夹
            folders_to_process = []
            for item in self.base_path.iterdir():
                if item.is_dir():
                    csv_files = list(item.glob("*.csv"))
                    if csv_files:
                        folders_to_process.append(item)
            
            if not folders_to_process:
                print("未找到包含CSV文件的文件夹")
                return False
            
            print(f"发现 {len(folders_to_process)} 个包含CSV文件的文件夹")
            
            # 处理每个文件夹
            processed_folders = []
            for folder in folders_to_process:
                processed_folder = self.process_folder(folder)
                if processed_folder:
                    processed_folders.append(processed_folder)
            
            if not processed_folders:
                print("没有文件夹成功处理")
                return False
            
            # 创建输出ZIP文件
            if output_dir is None:
                output_dir = self.base_path
            else:
                output_dir = Path(output_dir)

            output_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            zip_filename = f"新华秒笔{self.date_suffix}.zip"
            output_zip_path = output_dir / zip_filename

            # 创建ZIP压缩包
            success = self.create_zip_package(processed_folders, output_zip_path)

            # 统计信息
            end_time = datetime.now()
            duration = end_time - start_time

            print(f"\n=== 处理完成 ===")
            print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"总耗时: {duration.total_seconds():.2f} 秒")
            print(f"成功转换文件数: {self.processed_count}")
            print(f"错误文件数: {self.error_count}")
            print(f"处理文件夹数: {len(processed_folders)}")

            if success:
                print(f"输出文件: {output_zip_path}")
                print("✓ 所有操作成功完成！")
            else:
                print("✗ 部分操作失败")
            
            return success
            
        except Exception as e:
            print(f"执行过程中出现错误: {e}")
            traceback.print_exc()
            return False
        
        finally:
            # 清理临时目录
            self.cleanup_temp_directory()


def main():
    """主函数"""
    # 设置基础路径（与main.py中的files_path保持一致）
    base_path = "/Users/<USER>/Downloads/botSmart-日常业务/新华秒笔数据/每周五/"
    
    # 可以指定不同的输出目录，如果不指定则输出到基础路径
    output_dir = None  # 或者指定: "/Users/<USER>/Downloads/输出目录/"
    
    # 创建处理器并执行
    packager = CSVToExcelPackager(base_path)
    
    try:
        success = packager.run(output_dir)
        if success:
            print("\n程序执行成功！")
        else:
            print("\n程序执行失败！")
        packager.cleanup_temp_directory()
    except KeyboardInterrupt:
        print("\n用户中断程序执行")
        packager.cleanup_temp_directory()
    except Exception as e:
        print(f"\n程序执行异常: {e}")
        packager.cleanup_temp_directory()


if __name__ == "__main__":
    main()
