#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版CSV转Excel工具
专门处理每周五文件夹下的CSV文件，转换为Excel格式
使用最简单可靠的方式，避免数据丢失
"""

import os
import pandas as pd
from pathlib import Path
import traceback
from datetime import datetime
from openpyxl.styles import Alignment, Font
import zipfile
import shutil


class SimpleCSVToExcelConverter:
    """简化版CSV转Excel转换器"""
    
    def __init__(self, base_path,date_suffix):
        self.base_path = Path(base_path)
        self.date_suffix = date_suffix
        self.processed_count = 0
        self.error_count = 0
        self.skipped_count = 0
        
    def detect_encoding(self, file_path):
        """检测文件编码"""
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312', 'latin1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read()
                return encoding
            except:
                continue
        return 'utf-8'  # 默认编码
    
    def simple_csv_to_excel(self, csv_file_path, excel_file_path):
        """简单的CSV转Excel，保证数据完整性"""
        try:
            print(f"  处理: {csv_file_path.name}", end='', flush=True)
            
            # 检测编码
            encoding = self.detect_encoding(csv_file_path)
            
            # 读取CSV文件
            try:
                # 使用最宽松的参数读取CSV，避免解析错误
                df = pd.read_csv(
                    csv_file_path, 
                    encoding=encoding,
                    on_bad_lines='skip',  # 跳过有问题的行而不是失败
                    quoting=1,  # QUOTE_ALL
                    dtype=str,  # 所有列都作为字符串处理，避免类型转换问题
                    keep_default_na=False  # 不将空字符串转换为NaN
                )
            except Exception as e:
                # 如果标准方式失败，尝试更宽松的方式
                print(f" [尝试备用方法]", end='', flush=True)
                try:
                    df = pd.read_csv(
                        csv_file_path,
                        encoding=encoding,
                        sep=',',
                        on_bad_lines='skip',
                        dtype=str,
                        keep_default_na=False,
                        engine='python'  # 使用Python引擎，更宽松
                    )
                except Exception as e2:
                    print(f" ✗ 读取失败: {e2}")
                    self.error_count += 1
                    return False
            
            # 检查数据是否为空
            if df.empty:
                print(f" ⚠ 文件为空，跳过")
                self.skipped_count += 1
                return False
            
            # 确保目标目录存在
            excel_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为Excel
            with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='数据')
                
                # 获取工作表并设置格式
                worksheet = writer.sheets['数据']
                
                # 设置标题行格式
                for cell in worksheet[1]:
                    cell.font = Font(bold=True, size=11)
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 设置数据行格式
                for row in worksheet.iter_rows(min_row=2):
                    for cell in row:
                        if cell.value is not None:
                            cell.alignment = Alignment(
                                wrap_text=True,  # 自动换行
                                vertical='top',  # 垂直对齐到顶部
                                horizontal='left'  # 水平左对齐
                            )
                
                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if cell.value:
                                # 计算内容长度，考虑中文字符
                                content = str(cell.value)
                                length = len(content.encode('utf-8')) / 3  # 粗略估算显示宽度
                                max_length = max(max_length, length)
                        except:
                            pass
                    
                    # 设置列宽，限制最大和最小值
                    adjusted_width = min(max(max_length + 2, 10), 80)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f" ✓ ({len(df)}行)")
            self.processed_count += 1
            return True
            
        except Exception as e:
            print(f" ✗ 转换失败: {e}")
            self.error_count += 1
            return False
    
    def process_folder(self, folder_path, output_base_path):
        """处理单个文件夹"""
        try:
            folder_name = folder_path.name
            new_folder_name = folder_name + self.date_suffix
            new_folder_path = output_base_path / new_folder_name
            
            print(f"\n📁 处理文件夹: {folder_name}")
            
            # 创建新文件夹
            new_folder_path.mkdir(parents=True, exist_ok=True)
            
            # 查找所有CSV文件
            csv_files = list(folder_path.glob("*.csv"))
            
            if not csv_files:
                print(f"  ⚠ 文件夹中没有找到CSV文件")
                return None
            
            print(f"  📄 发现 {len(csv_files)} 个CSV文件")
            
            # 转换每个CSV文件
            success_count = 0
            for csv_file in csv_files:
                excel_filename = csv_file.stem + ".xlsx"
                excel_path = new_folder_path / excel_filename
                
                if self.simple_csv_to_excel(csv_file, excel_path):
                    success_count += 1
            
            if success_count > 0:
                print(f"  ✅ 文件夹处理完成: {success_count}/{len(csv_files)} 个文件转换成功")
                return new_folder_path
            else:
                print(f"  ❌ 文件夹处理失败: 没有文件转换成功")
                return None
                
        except Exception as e:
            print(f"❌ 处理文件夹 {folder_path.name} 时出错: {e}")
            traceback.print_exc()
            return None
    
    def create_zip_package(self, processed_folders, output_zip_path):
        """创建ZIP压缩包"""
        try:
            print(f"\n📦 创建ZIP压缩包...")
            
            with zipfile.ZipFile(output_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for folder_path in processed_folders:
                    for file_path in folder_path.rglob("*"):
                        if file_path.is_file():
                            arcname = file_path.relative_to(folder_path.parent)
                            zipf.write(file_path, arcname)
                            
            print(f"✅ ZIP创建成功: {output_zip_path}")
            file_size = output_zip_path.stat().st_size / 1024 / 1024
            print(f"   文件大小: {file_size:.2f} MB")
            return True
            
        except Exception as e:
            print(f"❌ 创建ZIP失败: {e}")
            return False
    
    def run(self):
        """执行完整的转换和打包流程"""
        start_time = datetime.now()
        print(f"🚀 === 简化版CSV转Excel工具 ===")
        print(f"⏰ 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📂 基础路径: {self.base_path}")
        print(f"📋 处理策略: 使用最简单可靠的方式，确保数据完整性")
        
        try:
            if not self.base_path.exists():
                print(f"❌ 错误: 基础路径不存在: {self.base_path}")
                return False
            
            # 创建输出目录
            output_base = self.base_path / f"新华秒笔{self.date_suffix}_简化版"
            output_base.mkdir(exist_ok=True)
            
            # 查找所有包含CSV的子文件夹
            folders_to_process = []
            for item in self.base_path.iterdir():
                if item.is_dir() and not item.name.startswith('新华秒笔'):
                    csv_files = list(item.glob("*.csv"))
                    if csv_files:
                        folders_to_process.append(item)
            
            if not folders_to_process:
                print("❌ 未找到包含CSV文件的文件夹")
                return False
            
            print(f"\n🔍 发现 {len(folders_to_process)} 个包含CSV文件的文件夹:")
            for folder in folders_to_process:
                csv_count = len(list(folder.glob("*.csv")))
                print(f"   📁 {folder.name} ({csv_count} 个CSV文件)")
            
            # 处理每个文件夹
            processed_folders = []
            for folder in folders_to_process:
                processed_folder = self.process_folder(folder, output_base)
                if processed_folder:
                    processed_folders.append(processed_folder)
            
            if not processed_folders:
                print("\n❌ 没有文件夹成功处理")
                return False
            
            # 创建ZIP压缩包
            zip_filename = f"新华秒笔{self.date_suffix}_简化版.zip"
            output_zip_path = self.base_path / zip_filename
            
            success = self.create_zip_package(processed_folders, output_zip_path)
            
            # 统计信息
            end_time = datetime.now()
            duration = end_time - start_time
            
            print(f"\n🎉 === 处理完成 ===")
            print(f"⏰ 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️  总耗时: {duration.total_seconds():.2f} 秒")
            print(f"✅ 成功转换文件数: {self.processed_count}")
            print(f"❌ 错误文件数: {self.error_count}")
            print(f"⚠️  跳过文件数: {self.skipped_count}")
            print(f"📁 处理文件夹数: {len(processed_folders)}")
            
            if success:
                print(f"\n📦 输出文件: {output_zip_path}")
                print("\n✅ 所有操作成功完成！")
                print("\n📝 处理说明：")
                print("1. 使用最宽松的CSV读取参数，避免解析错误")
                print("2. 所有数据都作为字符串处理，避免类型转换丢失")
                print("3. 跳过有问题的行而不是整个文件失败")
                print("4. 自动检测文件编码，支持多种编码格式")
                print("5. 设置合适的Excel格式，包括自动换行和列宽调整")
            
            return success
            
        except Exception as e:
            print(f"\n❌ 执行过程中出现错误: {e}")
            traceback.print_exc()
            return False


def main():
    """主函数"""
    # 设置基础路径
    base_path = "/Users/<USER>/Downloads/botSmart-日常业务/新华秒笔数据/每周五/"
    
    print("🔧 简化版CSV转Excel转换工具")
    print("=" * 50)
    print("这个工具专门解决数据丢失问题，使用最简单可靠的方式处理CSV文件")
    print("=" * 50)
    
    # 创建转换器并执行
    converter = SimpleCSVToExcelConverter(base_path,date_suffix='20250808')
    
    try:
        success = converter.run()
        if success:
            print("\n🎉 程序执行成功！")
            print("📋 请检查生成的Excel文件，确认数据完整性。")
            print("💡 如果还有数据丢失，请检查原始CSV文件是否有格式问题。")
        else:
            print("\n❌ 程序执行失败！")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序执行")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")


if __name__ == "__main__":
    main()
