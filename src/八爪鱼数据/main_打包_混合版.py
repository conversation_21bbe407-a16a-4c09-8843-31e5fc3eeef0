#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合版：针对不同文件采用不同处理策略
- 外网公文库中公文搜网开头的文件：使用增强版CSV修复
- 其他文件：使用原始main_打包.py的逻辑
"""

import os
import shutil
import zipfile
import pandas as pd
from pathlib import Path
import traceback
from datetime import datetime
import csv
import re
import tempfile
from openpyxl.styles import Alignment, Font


class HybridCSVToExcelPackager:
    """混合版打包处理器"""
    
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.date_suffix = "_20250808"
        self.processed_count = 0
        self.error_count = 0
        self.temp_dir = None
        
    def setup_temp_directory(self):
        """创建临时工作目录"""
        try:
            self.temp_dir = Path(tempfile.mkdtemp(prefix="csv_excel_hybrid_"))
            print(f"创建临时工作目录: {self.temp_dir}")
            return True
        except Exception as e:
            print(f"创建临时目录失败: {e}")
            return False
    
    def needs_enhanced_processing(self, csv_file_path, folder_name):
        """判断文件是否需要增强处理"""
        # 只有在外网公文库文件夹中且文件名以公文搜网开头的才使用增强处理
        return (folder_name == "外网公文库" and 
                csv_file_path.name.startswith("公文搜网"))
    
    def enhanced_reverse_fix_csv(self, csv_content):
        """增强版反向还原并修复CSV内容，保留格式"""
        lines = csv_content.split('\n')
        
        if not lines:
            return csv_content
        
        # 第一行是标题
        header_line = lines[0].strip()
        
        # 解析标题
        try:
            reader = csv.reader([header_line])
            headers = next(reader)
        except:
            headers = ['标题', '链接', '文本']
        
        # 收集记录
        records = []
        
        i = 1
        while i < len(lines):
            line = lines[i]  # 注意：不strip，保留原始格式
            
            if not line.strip():
                i += 1
                continue
            
            # 检查是否是新记录的开始
            if (line.strip().startswith('"') and 
                '","http' in line and 
                line.count('","') >= 2):
                
                # 提取记录信息
                try:
                    # 简化的提取方法，避免复杂正则
                    parts = line.strip().split('","')
                    if len(parts) >= 3:
                        title = parts[0].lstrip('"')
                        link = parts[1]
                        text_start = '","'.join(parts[2:])
                        
                        # 收集文本，保留格式
                        text_lines = []
                        if text_start.startswith('"'):
                            text_start = text_start[1:]
                        
                        # 处理第一行文本（可能是空的或只有标题）
                        if text_start.strip():
                            # 如果第一行就是标题的重复，跳过
                            if text_start.strip() == title:
                                # 标题重复了，跳过
                                pass
                            else:
                                text_lines.append(text_start)
                        
                        # 继续读取文本行
                        i += 1
                        first_content_line = True
                        while i < len(lines):
                            next_line = lines[i]  # 保留原始格式
                            
                            # 检查是否是下一条记录
                            if (next_line.strip() and
                                next_line.strip().startswith('"') and 
                                '","http' in next_line and 
                                next_line.count('","') >= 2):
                                break
                            
                            # 处理文本行
                            if next_line.strip():
                                # 如果这是标题的重复，跳过
                                if first_content_line and next_line.strip() == title:
                                    first_content_line = False
                                    i += 1
                                    continue
                                
                                first_content_line = False
                                
                                if next_line.strip().endswith('"'):
                                    cleaned = next_line.rstrip()
                                    if cleaned.endswith('"'):
                                        cleaned = cleaned[:-1]
                                    text_lines.append(cleaned)
                                    i += 1
                                    break
                                else:
                                    text_lines.append(next_line)
                            else:
                                # 空行也要保留，但不要在开头累积空行
                                if text_lines:  # 只有已经有内容时才添加空行
                                    text_lines.append('')
                            
                            i += 1
                        
                        # 合并文本
                        text = '\n'.join(text_lines)
                        
                        # 处理格式
                        text = self.process_text_format(text)
                        
                        records.append([title, link, text])
                    else:
                        i += 1
                except:
                    i += 1
            else:
                i += 1
        
        # 使用标准CSV格式重新构建
        import io
        output = io.StringIO()
        writer = csv.writer(output, quoting=csv.QUOTE_ALL)
        writer.writerow(headers)
        for record in records:
            writer.writerow(record)
        
        return output.getvalue()
    
    def process_text_format(self, text):
        """处理文本格式，添加缩进和修复引号"""
        lines = text.split('\n')
        result = []
        
        for line in lines:
            # 空行保留
            if not line.strip():
                result.append('')
                continue
            
            # 检查是否已有缩进
            if line.startswith('　　') or line.startswith('    '):
                result.append(line)
                continue
            
            # 智能添加缩进
            stripped = line.strip()
            
            # 这些情况需要缩进
            if (stripped.startswith('根据') or 
                stripped.startswith('一、') or 
                stripped.startswith('二、') or
                stripped.startswith('三、') or
                stripped.startswith('四、') or
                stripped.startswith('（一）') or
                stripped.startswith('（二）') or
                stripped.startswith('（三）') or
                stripped.startswith('（四）') or
                stripped.startswith('（五）') or
                stripped.startswith('（六）') or
                re.match(r'^\d+\.', stripped)):  # 数字开头的段落
                result.append('　　' + stripped)
            else:
                # 保持原样
                result.append(line)
        
        # 合并结果
        text = '\n'.join(result)
        
        # 修复引号问题
        text = re.sub(r'开展(\d+)"', r'开展"\1"', text)
        
        return text
    
    def simple_csv_to_excel(self, csv_file_path, excel_file_path):
        """原始的简单CSV转Excel逻辑（用于非公文搜网文件）"""
        try:
            print(f"  处理: {csv_file_path.name}", end='', flush=True)
            
            # 尝试多种编码方式读取CSV
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(csv_file_path, encoding=encoding)
                    break
                except:
                    continue
            
            if df is None:
                print(f"    ✗ 无法读取文件")
                return False
            
            # 确保目标目录存在
            excel_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为Excel并设置格式
            with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='数据')
                
                # 获取工作表并设置单元格格式
                worksheet = writer.sheets['数据']
                
                # 设置所有单元格自动换行和对齐
                for row in worksheet.iter_rows():
                    for cell in row:
                        if cell.value is not None:
                            cell.alignment = Alignment(
                                wrap_text=True,  # 自动换行
                                vertical='top',  # 垂直对齐到顶部
                                horizontal='left'  # 水平左对齐
                            )
                
                # 调整列宽
                for column_cells in worksheet.columns:
                    length = max(len(str(cell.value) if cell.value else '') for cell in column_cells)
                    adjusted_width = min(max(length * 0.1, 15), 100)
                    worksheet.column_dimensions[column_cells[0].column_letter].width = adjusted_width
            
            print(f" ✓ ({len(df)}行)")
            self.processed_count += 1
            return True
            
        except Exception as e:
            print(f"    ✗ 转换失败: {e}")
            self.error_count += 1
            return False
    
    def enhanced_csv_to_excel(self, csv_file_path, excel_file_path):
        """增强版CSV转Excel（用于公文搜网文件）"""
        try:
            print(f"  处理: {csv_file_path.name} [增强模式]", end='', flush=True)
            
            # 读取原始CSV内容
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
            content = None
            encoding_used = None
            
            for encoding in encodings:
                try:
                    with open(csv_file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    encoding_used = encoding
                    break
                except:
                    continue
            
            if content is None:
                print(f"    ✗ 无法读取文件")
                return False
            
            # 增强版反向还原修复CSV
            fixed_content = self.enhanced_reverse_fix_csv(content)
            
            # 在临时目录创建中转CSV文件
            temp_csv_path = self.temp_dir / f"{csv_file_path.stem}_temp.csv"
            with open(temp_csv_path, 'w', encoding=encoding_used, newline='') as f:
                f.write(fixed_content)
            
            try:
                # 读取修复后的CSV
                df = pd.read_csv(temp_csv_path, encoding=encoding_used)
                print(f"    ✓ 成功读取，共{len(df)}行数据")
                
                # 确保目标目录存在
                excel_file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 转换为Excel并设置格式
                with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='数据')
                    
                    # 获取工作表
                    worksheet = writer.sheets['数据']
                    
                    # 设置标题行格式
                    for cell in worksheet[1]:
                        cell.font = Font(bold=True, size=12)
                        cell.alignment = Alignment(horizontal='center', vertical='center')
                    
                    # 设置数据格式
                    for row in worksheet.iter_rows(min_row=2):
                        for cell in row:
                            if cell.value is not None:
                                cell.alignment = Alignment(
                                    wrap_text=True,  # 自动换行
                                    vertical='top',
                                    horizontal='left'
                                )
                    
                    # 调整列宽
                    if len(df.columns) >= 3:
                        worksheet.column_dimensions['A'].width = 60   # 标题
                        worksheet.column_dimensions['B'].width = 40   # 链接
                        worksheet.column_dimensions['C'].width = 120  # 文本
                    
                    # 设置合适的行高
                    for row in range(2, worksheet.max_row + 1):
                        worksheet.row_dimensions[row].height = 30
                
                print(f" ✓ ({len(df)}行)")
                self.processed_count += 1
                return True
                
            finally:
                # 删除临时CSV文件
                if temp_csv_path.exists():
                    temp_csv_path.unlink()
                
        except Exception as e:
            print(f"    ✗ 转换失败: {e}")
            self.error_count += 1
            return False
    
    def csv_to_excel(self, csv_file_path, excel_file_path, folder_name):
        """根据文件类型选择处理方式"""
        if self.needs_enhanced_processing(csv_file_path, folder_name):
            return self.enhanced_csv_to_excel(csv_file_path, excel_file_path)
        else:
            return self.simple_csv_to_excel(csv_file_path, excel_file_path)
    
    def process_folder(self, folder_path, output_base_path):
        """处理单个文件夹"""
        try:
            folder_name = folder_path.name
            new_folder_name = folder_name + self.date_suffix
            new_folder_path = output_base_path / new_folder_name
            
            print(f"\n处理文件夹: {folder_name}")
            
            # 创建新文件夹
            new_folder_path.mkdir(parents=True, exist_ok=True)
            
            # 查找所有CSV文件
            csv_files = list(folder_path.glob("*.csv"))
            
            if not csv_files:
                print(f"  文件夹中没有找到CSV文件")
                return None
            
            print(f"  发现 {len(csv_files)} 个CSV文件")
            
            # 统计增强处理的文件数
            enhanced_count = sum(1 for f in csv_files if self.needs_enhanced_processing(f, folder_name))
            if enhanced_count > 0:
                print(f"  其中 {enhanced_count} 个文件将使用增强处理")
            
            # 转换每个CSV文件
            success_count = 0
            for csv_file in csv_files:
                excel_filename = csv_file.stem + ".xlsx"
                excel_path = new_folder_path / excel_filename
                
                if self.csv_to_excel(csv_file, excel_path, folder_name):
                    success_count += 1
            
            if success_count > 0:
                print(f"  ✓ 文件夹处理完成: {success_count}/{len(csv_files)} 个文件转换成功")
                return new_folder_path
            else:
                print(f"  ✗ 文件夹处理失败: 没有文件转换成功")
                return None
                
        except Exception as e:
            print(f"处理文件夹 {folder_path.name} 时出错: {e}")
            return None
    
    def create_zip_package(self, processed_folders, output_zip_path):
        """创建ZIP压缩包"""
        try:
            print(f"\n创建ZIP压缩包...")
            
            with zipfile.ZipFile(output_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for folder_path in processed_folders:
                    for file_path in folder_path.rglob("*"):
                        if file_path.is_file():
                            arcname = file_path.relative_to(folder_path.parent)
                            zipf.write(file_path, arcname)
                            
            print(f"✓ ZIP创建成功: {output_zip_path}")
            file_size = output_zip_path.stat().st_size / 1024 / 1024
            print(f"  文件大小: {file_size:.2f} MB")
            return True
            
        except Exception as e:
            print(f"✗ 创建ZIP失败: {e}")
            return False
    
    def cleanup_temp_directory(self):
        """清理临时目录"""
        try:
            if self.temp_dir and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                print(f"清理临时目录: {self.temp_dir}")
        except Exception as e:
            print(f"清理临时目录失败: {e}")
    
    def run(self):
        """执行完整的转换和打包流程"""
        start_time = datetime.now()
        print(f"=== 混合版CSV转Excel打包工具 ===")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"基础路径: {self.base_path}")
        print(f"\n处理策略:")
        print(f"  - 外网公文库中公文搜网开头的文件：使用增强处理")
        print(f"  - 其他所有文件：使用标准处理")
        
        try:
            if not self.base_path.exists():
                print(f"错误: 基础路径不存在: {self.base_path}")
                return False
            
            # 设置临时工作目录
            if not self.setup_temp_directory():
                return False
            
            # 创建输出目录（也在临时目录中）
            output_base = self.temp_dir / f"新华秒笔{self.date_suffix}"
            output_base.mkdir(exist_ok=True)
            
            # 查找所有包含CSV的子文件夹
            folders_to_process = []
            for item in self.base_path.iterdir():
                if item.is_dir() and not item.name.startswith('新华秒笔'):
                    csv_files = list(item.glob("*.csv"))
                    if csv_files:
                        folders_to_process.append(item)
            
            if not folders_to_process:
                print("未找到包含CSV文件的文件夹")
                return False
            
            print(f"\n发现 {len(folders_to_process)} 个包含CSV文件的文件夹")
            
            # 处理每个文件夹
            processed_folders = []
            for folder in folders_to_process:
                processed_folder = self.process_folder(folder, output_base)
                if processed_folder:
                    processed_folders.append(processed_folder)
            
            if not processed_folders:
                print("\n没有文件夹成功处理")
                return False
            
            # 创建ZIP压缩包（保存到原始目录）
            zip_filename = f"新华秒笔{self.date_suffix}_混合版.zip"
            output_zip_path = self.base_path / zip_filename
            
            success = self.create_zip_package(processed_folders, output_zip_path)
            
            # 统计信息
            end_time = datetime.now()
            duration = end_time - start_time
            
            print(f"\n=== 处理完成 ===")
            print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"总耗时: {duration.total_seconds():.2f} 秒")
            print(f"成功转换文件数: {self.processed_count}")
            print(f"错误文件数: {self.error_count}")
            print(f"处理文件夹数: {len(processed_folders)}")
            
            if success:
                print(f"\n输出文件: {output_zip_path}")
                print("\n✓ 所有操作成功完成！")
                print("\n处理说明：")
                print("1. 外网公文库中公文搜网开头的文件已使用增强处理")
                print("2. 其他文件使用标准CSV转Excel转换")
                print("3. 所有中转文件都在临时目录处理，不污染原始文件夹")
            
            return success
            
        except Exception as e:
            print(f"\n执行过程中出现错误: {e}")
            traceback.print_exc()
            return False
        
        finally:
            # 清理临时目录
            self.cleanup_temp_directory()


def main():
    """主函数"""
    # 设置基础路径
    base_path = "/Users/<USER>/Downloads/botSmart-日常业务/新华秒笔数据/每周五/"
    
    # 创建处理器并执行
    packager = HybridCSVToExcelPackager(base_path)
    
    try:
        success = packager.run()
        if success:
            print("\n程序执行成功！")
            print("请解压生成的ZIP文件，检查Excel格式是否满意。")
        else:
            print("\n程序执行失败！")
    except KeyboardInterrupt:
        print("\n用户中断程序执行")
        packager.cleanup_temp_directory()
    except Exception as e:
        print(f"\n程序执行异常: {e}")
        packager.cleanup_temp_directory()


if __name__ == "__main__":
    main()