#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据完整性检查工具
分析为什么混合版会漏数据，并提供详细的诊断报告
"""

import os
import pandas as pd
from pathlib import Path
import csv
import traceback
from datetime import datetime


class DataIntegrityChecker:
    """数据完整性检查器"""
    
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.issues_found = []
        
    def detect_encoding(self, file_path):
        """检测文件编码"""
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312', 'latin1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                return encoding, len(content)
            except:
                continue
        return None, 0
    
    def count_csv_lines_raw(self, file_path):
        """原始行数统计（不解析CSV）"""
        try:
            encoding, _ = self.detect_encoding(file_path)
            if not encoding:
                return 0, "编码检测失败"
            
            with open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()
                non_empty_lines = [line for line in lines if line.strip()]
                return len(non_empty_lines), "成功"
        except Exception as e:
            return 0, f"读取失败: {e}"
    
    def count_csv_lines_pandas(self, file_path):
        """使用pandas统计行数"""
        try:
            encoding, _ = self.detect_encoding(file_path)
            if not encoding:
                return 0, "编码检测失败"
            
            # 标准方式
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                return len(df), "标准读取成功"
            except Exception as e1:
                # 宽松方式
                try:
                    df = pd.read_csv(
                        file_path, 
                        encoding=encoding,
                        on_bad_lines='skip',
                        dtype=str,
                        keep_default_na=False
                    )
                    return len(df), f"宽松读取成功（标准方式失败: {e1}）"
                except Exception as e2:
                    return 0, f"读取失败: {e2}"
        except Exception as e:
            return 0, f"处理失败: {e}"
    
    def count_csv_lines_manual(self, file_path):
        """手动解析CSV统计行数"""
        try:
            encoding, _ = self.detect_encoding(file_path)
            if not encoding:
                return 0, "编码检测失败"
            
            with open(file_path, 'r', encoding=encoding) as f:
                reader = csv.reader(f)
                count = 0
                for row in reader:
                    if any(cell.strip() for cell in row):  # 至少有一个非空单元格
                        count += 1
                return count - 1 if count > 0 else 0, "手动解析成功"  # 减去标题行
        except Exception as e:
            return 0, f"手动解析失败: {e}"
    
    def analyze_csv_structure(self, file_path):
        """分析CSV文件结构"""
        try:
            encoding, file_size = self.detect_encoding(file_path)
            if not encoding:
                return {"error": "编码检测失败"}
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            lines = content.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            # 分析引号
            quote_count = content.count('"')
            comma_count = content.count(',')
            
            # 检查是否有不匹配的引号
            quote_balanced = quote_count % 2 == 0
            
            # 分析第一行（标题行）
            first_line = lines[0] if lines else ""
            try:
                reader = csv.reader([first_line])
                headers = next(reader)
                header_count = len(headers)
            except:
                header_count = first_line.count(',') + 1 if first_line else 0
            
            return {
                "file_size": file_size,
                "encoding": encoding,
                "total_lines": len(lines),
                "non_empty_lines": len(non_empty_lines),
                "quote_count": quote_count,
                "quote_balanced": quote_balanced,
                "comma_count": comma_count,
                "header_count": header_count,
                "first_line_preview": first_line[:200] + "..." if len(first_line) > 200 else first_line
            }
        except Exception as e:
            return {"error": f"分析失败: {e}"}
    
    def check_file(self, csv_file_path):
        """检查单个CSV文件"""
        print(f"\n🔍 检查文件: {csv_file_path.name}")
        
        # 基本信息
        file_size = csv_file_path.stat().st_size
        print(f"   📁 文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
        
        # 结构分析
        structure = self.analyze_csv_structure(csv_file_path)
        if "error" in structure:
            print(f"   ❌ 结构分析失败: {structure['error']}")
            self.issues_found.append(f"{csv_file_path.name}: 结构分析失败")
            return
        
        print(f"   📝 编码: {structure['encoding']}")
        print(f"   📄 总行数: {structure['total_lines']}")
        print(f"   📄 非空行数: {structure['non_empty_lines']}")
        print(f"   📊 列数: {structure['header_count']}")
        print(f"   \" 引号数量: {structure['quote_count']} ({'平衡' if structure['quote_balanced'] else '不平衡'})")
        
        if not structure['quote_balanced']:
            print(f"   ⚠️  警告: 引号不平衡，可能导致解析错误")
            self.issues_found.append(f"{csv_file_path.name}: 引号不平衡")
        
        # 行数统计对比
        raw_count, raw_status = self.count_csv_lines_raw(csv_file_path)
        pandas_count, pandas_status = self.count_csv_lines_pandas(csv_file_path)
        manual_count, manual_status = self.count_csv_lines_manual(csv_file_path)
        
        print(f"   📊 行数统计对比:")
        print(f"      原始文本行数: {raw_count} ({raw_status})")
        print(f"      Pandas读取行数: {pandas_count} ({pandas_status})")
        print(f"      手动解析行数: {manual_count} ({manual_status})")
        
        # 检查数据丢失
        max_count = max(raw_count, pandas_count, manual_count)
        min_count = min(pandas_count, manual_count)  # 不包括原始行数
        
        if max_count > min_count:
            loss_rate = (max_count - min_count) / max_count * 100 if max_count > 0 else 0
            print(f"   ⚠️  可能的数据丢失: {max_count - min_count} 行 ({loss_rate:.1f}%)")
            self.issues_found.append(f"{csv_file_path.name}: 可能丢失 {max_count - min_count} 行数据")
        else:
            print(f"   ✅ 数据完整性良好")
        
        # 显示文件预览
        if structure['first_line_preview']:
            print(f"   👀 首行预览: {structure['first_line_preview']}")
    
    def check_folder(self, folder_path):
        """检查文件夹中的所有CSV文件"""
        print(f"\n📁 检查文件夹: {folder_path.name}")
        
        csv_files = list(folder_path.glob("*.csv"))
        if not csv_files:
            print(f"   ⚠️  没有找到CSV文件")
            return
        
        print(f"   📄 发现 {len(csv_files)} 个CSV文件")
        
        total_size = sum(f.stat().st_size for f in csv_files)
        print(f"   📁 总大小: {total_size:,} 字节 ({total_size/1024/1024:.2f} MB)")
        
        for csv_file in csv_files:
            self.check_file(csv_file)
    
    def run(self):
        """执行完整的检查流程"""
        start_time = datetime.now()
        print(f"🔍 === 数据完整性检查工具 ===")
        print(f"⏰ 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📂 基础路径: {self.base_path}")
        
        try:
            if not self.base_path.exists():
                print(f"❌ 错误: 基础路径不存在: {self.base_path}")
                return False
            
            # 查找所有包含CSV的子文件夹
            folders_to_check = []
            for item in self.base_path.iterdir():
                if item.is_dir() and not item.name.startswith('新华秒笔'):
                    csv_files = list(item.glob("*.csv"))
                    if csv_files:
                        folders_to_check.append(item)
            
            if not folders_to_check:
                print("❌ 未找到包含CSV文件的文件夹")
                return False
            
            print(f"\n🔍 发现 {len(folders_to_check)} 个包含CSV文件的文件夹")
            
            # 检查每个文件夹
            for folder in folders_to_check:
                self.check_folder(folder)
            
            # 总结报告
            end_time = datetime.now()
            duration = end_time - start_time
            
            print(f"\n📋 === 检查报告 ===")
            print(f"⏰ 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️  总耗时: {duration.total_seconds():.2f} 秒")
            
            if self.issues_found:
                print(f"\n⚠️  发现 {len(self.issues_found)} 个潜在问题:")
                for i, issue in enumerate(self.issues_found, 1):
                    print(f"   {i}. {issue}")
                
                print(f"\n💡 建议:")
                print(f"   1. 使用简化版工具处理，它使用更宽松的解析参数")
                print(f"   2. 检查原始CSV文件是否有格式问题（如不匹配的引号）")
                print(f"   3. 对于有问题的文件，可以手动修复后再处理")
            else:
                print(f"\n✅ 没有发现明显的数据完整性问题")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 检查过程中出现错误: {e}")
            traceback.print_exc()
            return False


def main():
    """主函数"""
    # 设置基础路径
    base_path = "/Users/<USER>/Downloads/botSmart-日常业务/新华秒笔数据/每周五/"
    
    print("🔍 数据完整性检查工具")
    print("=" * 50)
    print("这个工具会分析CSV文件，找出可能导致数据丢失的原因")
    print("=" * 50)
    
    # 创建检查器并执行
    checker = DataIntegrityChecker(base_path)
    
    try:
        success = checker.run()
        if success:
            print("\n🎉 检查完成！")
        else:
            print("\n❌ 检查失败！")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序执行")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")


if __name__ == "__main__":
    main()
