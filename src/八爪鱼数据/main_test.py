"""
@File    : main.py
@Software: PyCharm
@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2022/2/23 10:52  xing       1.0          Octopus API
"""
import json
import time
import os
import traceback

import requests

from src.utils.BazhuayuUtils import Interface_Call_Credentials, this_task, Cloud_Collection_Related, Data_Collection_Related

def csv_read(path):
    with open(path, 'r', encoding='utf-8') as f:
        return f.read()

def push_web(fileName='', firstTitle='', secondTitle='', thirdTitle='', fourthTitle='', fifthTitle='', link='', time='', source='', content='', remark=''):
    body = {
        "fileName": fileName,
        "firstTitle": firstTitle,
        "secondTitle": secondTitle,
        "thirdTitle": thirdTitle,
        "fourthTitle": fourthTitle,
        "fifthTitle": fifthTitle,
        "link": link,
        "time": time,
        "source": source,
        "content": content,
        "remark": remark
    }
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
    }
    try:
        re = requests.post("http://114.116.242.57:7725/system/data/addData", json=body, headers=headers, timeout=60)
        a = re.text
        data = json.loads(a)
        print(data)
    except Exception as e:
        traceback.print_exc()
        print('提交失败!!!')

def service(files_path, task_group_id, tasks_info, datas):
    data_str = ''
    if not os.path.isdir(files_path + task_group_id):
        os.mkdir(files_path + task_group_id)
    if os.path.isfile(files_path + task_group_id + '/' + tasks_info['taskName'] + '.csv'):
        data_str = csv_read(files_path + task_group_id + '/' + tasks_info['taskName'] + '.csv')
    else:
        data_str = ''
    for data in datas:
        title_index = None
        link_index = None
        time_index = None
        content_index = None
        channel_index = None
        source_index = None
        remark_index = None
        thirdTitle_index = None
        if data_str == '':
            for da in data:
                if data_str == '':
                    data_str = data_str + '\"' + da + '\"'
                else:
                    data_str = data_str + ',\"' + da + '\"'
            data_str = data_str + '\n'
        for da in data:
            if data_str[-1] == '\n':
                data_str = data_str + '\"' + data[da] + '\"'
            else:
                data_str = data_str + ',\"' + data[da] + '\"'
            if da == '标题':
                title_index = data[da]
            elif da == '标题链接' or da == '链接' or da == '页面网址':
                link_index = data[da]
            elif da == '时间' or da == '发布时间' or da == '发布日期':
                time_index = data[da]
            elif da == '文本' or da == '正文':
                content_index = data[da]
            elif da == '频道' or da == '二级分类':
                channel_index = data[da]
            elif da == '信息来源':
                source_index = data[da]
            elif da == '扩展':
                remark_index = data[da]
            elif da == '一级分类':
                thirdTitle_index = data[da]
        data_str = data_str + '\n'
        push_web(fileName=tasks_info['taskName'], fifthTitle=title_index, link=link_index, time=time_index,
                 content=content_index,
                 fourthTitle=channel_index if channel_index else '',
                 source=source_index if source_index else '',
                 remark=remark_index if remark_index else '',
                 thirdTitle=thirdTitle_index if thirdTitle_index else '')


    with open(files_path + task_group_id + '/' + tasks_info['taskName'] + '.csv', mode='w', encoding='utf-8-sig') as f:
        f.write(data_str)
        f.close()
    # Call the  function that Marks the data as exported,the taskId from this_task.get_search_task(),str


if __name__ == '__main__':
    # Instantiate class
    Interface_Call_Credentials = Interface_Call_Credentials()
    this_task = this_task()
    Cloud_Collection_Related = Cloud_Collection_Related()
    Data_Collection_Related = Data_Collection_Related()

    # Please enter an item in an account number password
    Interface_Call_Credentials.user_name = '***********'
    Interface_Call_Credentials.password = '1q2w3e4r'

    # Call the function that gets the new token of the account
    token_info = Interface_Call_Credentials.get_new_token()
    files_path = '/Users/<USER>/Downloads/botSmart-日常业务/新华秒笔数据/每周五/'
    # Call the function that refreshes the token interface
    # token_info = Interface_Call_Credentials.The_refresh_Token(token_info['refresh_token'])

    token_str = token_info['token_type'] + ' ' + token_info['access_token']


    tasks_info = {'taskId': '67b1510d-0218-3f57-c552-05abdee53f55', 'taskName': '二十届三中全会'}
    data_list = Data_Collection_Related.get_task_all_data(token=token_str, taskId=tasks_info['taskId'],offset=10, size=100)
    print(data_list)
    # service(files_path=files_path, task_group_id='', tasks_info=tasks_info, datas=data_list['data'])

