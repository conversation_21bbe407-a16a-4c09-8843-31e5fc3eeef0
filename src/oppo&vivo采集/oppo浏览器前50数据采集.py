import datetime
import json
import os
import pathlib
import re
import time
import traceback
import uuid
from typing import Optional

import requests
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium import webdriver
import uiautomator2 as u2
import xlwings
import pythoncom

from src.utils import DriverUtils

def data_write3(file_path, data):
    pythoncom.CoInitialize()
    app = xlwings.App(visible=False, add_book=False)
    # Excel工作簿显示警告,不显示
    app.display_alerts = False
    # 工作簿屏幕更新,不更新
    app.screen_updating = False
    wb = app.books.add()
    sht = wb.sheets('sheet1')
    # 将数据写入第 i 行，第 j 列
    sht.range('A1').value = data
    wb.save(file_path)  # 保存文件
    wb.close()
    app.kill()
    pythoncom.CoUninitialize()


def data_read2(file_path, table_name):
    pythoncom.CoInitialize()
    app = xlwings.App(visible=False, add_book=False)
    # Excel工作簿显示警告,不显示
    app.display_alerts = False
    # 工作簿屏幕更新,不更新
    app.screen_updating = False
    wb = app.books.open(file_path)
    data = wb.sheets[table_name]
    # 获取已编辑的矩形区域,最底部且最右侧的单元格
    last_cell = data.used_range.last_cell
    # 最大行数
    last_row = last_cell.row
    # 最大列数
    last_col = last_cell.column
    table = data.range((1, 1), (last_row, last_col)).value
    # 关闭工作簿
    wb.close()
    # 退出Excel
    app.quit()
    pythoncom.CoUninitialize()
    return table

def folder(filename):
    filepath = 'C:\\采集\\' + filename
    pathlib.Path(filepath).parent.mkdir(parents=True, exist_ok=True)
    return filepath

if __name__ == '__main__':
    page = DriverUtils.Utils.get_page(port=9092, headless=False, user_data_name='DEHIOFG6WS5DS8AA')
    while True:
        tittle_list = []
        data_len = 0
        if datetime.datetime.now().strftime("%H-%M") in ['08-00', '12-00', '16-00', '20-00']:
            id = "DEHIOFG6WS5DS8AA"
            d = u2.connect(id)
            if os.path.isfile(r'C:\Users\<USER>\Desktop\oppo浏览器数据采集.xlsx'):
                data = data_read2(file_path=r'C:\Users\<USER>\Desktop\oppo浏览器数据采集.xlsx', table_name='Sheet1')
            else:
                data = [['标题', '作者:来源', '来源链接', '封面图本地路径', '正文图本地路径', '发布时间', '采集时间']]
            try:
                d.app_start('com.heytap.browser', stop=True)
                d.sleep(12)
                while data_len <= 70:
                    covername = 'oppo采集图片\\' + ''.join(str(uuid.uuid4()).split('-')) + '.jpg'
                    image = d.screenshot()
                    image_paths = folder(covername)
                    image.save(image_paths)
                    elements = d.xpath('//*[@resource-id="com.heytap.browser:id/recycler_view"]/descendant::android.widget.TextView[@resource-id="com.heytap.browser:id/text0"]').all()
                    for e in elements:
                        if e.text in tittle_list:
                            print(e.text + ':   已存在')
                        else:
                            bt = e.text
                            e.click()
                            d.sleep(1)
                            if len(d.xpath('//*[@resource-id="com.heytap.browser:id/share"]').all()) > 0:
                                d.xpath('//*[@resource-id="com.heytap.browser:id/share"]').click()
                            elif len(d.xpath('//*[@resource-id="com.heytap.browser:id/btn_immersive_more"]').all()) > 0:
                                d.xpath('//*[@resource-id="com.heytap.browser:id/btn_immersive_more"]').click()
                                d.sleep(1)
                                d.xpath('//*[@resource-id="com.heytap.browser:id/small_more_share"]').click()
                            d.sleep(1)
                            d.xpath('//*[@text="复制链接"]').click()
                            d.sleep(1)
                            url = d.clipboard
                            page.get(url)
                            # page.wait.load_start()
                            # page.wait.load_complete()
                            ly_element = page.ele(loc_or_ele='xpath://*[@id="author-name" or @class="only-source-text"]', timeout=3)
                            if ly_element:
                                ly = ly_element.text
                            else:
                                ly = ''
                            release_time_element = page.ele(loc_or_ele='xpath://span[@id="publish-time"]', timeout=3)
                            if release_time_element:
                                release_time = release_time_element.text
                            else:
                                release_time = ''
                            filename = 'oppo采集图片\\' + ''.join(str(uuid.uuid4()).split('-')) + '.jpg'
                            image = d.screenshot()
                            image_paths = folder(filename)
                            image.save(image_paths)
                            d.press("back")
                            d.sleep(1)
                            collect_time = str(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                            tittle_list.append(bt)
                            data.append([bt, ly, url, covername, filename, release_time, collect_time])
                            data_len += 1
                            print(bt)
                            if data_len >= 70:
                                break
                    if data_len >= 70:
                        break
                    gg_elements = d.xpath('//*[@resource-id="com.heytap.browser:id/recycler_view"]/descendant::android.widget.TextView[@resource-id="com.heytap.browser:id/feed_title"]').all()
                    for gg in gg_elements:
                        if gg.text in tittle_list:
                            print(gg.text + ':   已存在')
                        else:
                            bt = gg.text
                            collect_time = str(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                            data.append([bt, '', '', covername, '', '', collect_time])
                            data_len += 1
                            print(bt)
                            tittle_list.append(bt)
                            if data_len >= 70:
                                break
                    if data_len >= 70:
                        break
                    d.swipe(0.4, 0.9, 0.4, 0.15, 0.4)
                    d.sleep(2)
                    if data_len >= 70:
                        break
                    d.sleep(1)
                data_write3(file_path=r'C:\Users\<USER>\Desktop\oppo浏览器数据采集.xlsx', data=data)
                message = {
                    "msgtype": "text",
                    "text": {
                        "content": '数据采集--oppo浏览器数据采集--完成,请查收'
                    }
                }
                message_json = json.dumps(message)
                send_message = requests.post(
                    url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2deca73e-eb06-4f1b-88d4-1b1881cadbd3',
                    data=message_json, headers={"Content-Type": "application/json"})
                print(send_message.text)
                d.app_stop('com.heytap.browser')
            except Exception as e:
                traceback.print_exc()
                data_write3(file_path=r'C:\Users\<USER>\Desktop\oppo浏览器数据采集.xlsx', data=data)
                message = {
                    "msgtype": "text",
                    "text": {
                        "content": '数据采集--oppo浏览器数据采集--意外停止!!!'
                    }
                }
                message_json = json.dumps(message)
                send_message = requests.post(
                    url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2deca73e-eb06-4f1b-88d4-1b1881cadbd3',
                    data=message_json, headers={"Content-Type": "application/json"})
                print(send_message.text)
                d.app_stop('com.heytap.browser')
        else:
            time.sleep(50)