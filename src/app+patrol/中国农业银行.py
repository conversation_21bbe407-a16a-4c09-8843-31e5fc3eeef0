# -*- coding: utf-8 -*-
# coding=utf-8
import os
import re

from src.utils.UploadsUtils import Uploads

from src.utils.MinioUtils import Helper

if __name__ == '__main__':
    files_path = r'C:\Users\<USER>\Desktop\app-patrol-actuator\cj'
    sid = ''
    pr_pi_sid = ''
    pr_cpi_sid = '"52fc70d49e237163ea40a28f39c44643"'
    pr_ai_sid = '"9ac6f711a4637ea3bb948d9ece972ba2"'
    pr_origin = '"1"'
    pr_ti_sid = '"b67110837c742dd7a65a4cc182ff1bcc"'
    pr_ci_sid = '"72d7781f2d2a7c8abba8ecf6ac6b730e"'
    pr_hit_type = '"0"'
    pr_hit_url = ''
    pr_screen_time = ''
    pr_hit_code = ''
    pr_hit_content = ''
    pr_vci_sid = ''
    pr_vli_sid = ''
    pr_event_sid = ''
    pr_sw_sid = ''
    pr_module_name = '"城市专区"'
    pr_describe = ''
    pr_positioning = ''
    pr_content = ''
    pr_status = '"0"'
    pr_repairman_sid = ''
    pr_repair = '"0"'
    pr_repair_time = ''
    pr_operator_sid = '"kGNzHYK6I9srBgf8X9PbKVrGpgKk4jry2XVs"'
    pr_create_time = ''
    pr_reviewer_sid = ''
    pr_reviewer_time = ''
    pr_update_time = ''
    pr_deleted = '"0"'
    pr_audit_sid = ''
    pr_mark_hit_url = ''
    pr_audit_time = ''
    pr_img_url = ''
    with open(r'C:\Users\<USER>\Desktop\中国农业银行.csv', 'a+', encoding='utf-8') as f:
        f.write(
            '"sid","pr_pi_sid","pr_cpi_sid","pr_ai_sid","pr_origin","pr_ti_sid","pr_ci_sid","pr_hit_type","pr_hit_url","pr_screen_time","pr_hit_code","pr_hit_content","pr_vci_sid","pr_vli_sid","pr_event_sid","pr_sw_sid","pr_module_name","pr_describe","pr_positioning","pr_content","pr_status","pr_repairman_sid","pr_repair","pr_repair_time","pr_operator_sid","pr_create_time","pr_reviewer_sid","pr_reviewer_time","pr_update_time","pr_deleted","pr_audit_sid","pr_mark_hit_url","pr_audit_time","pr_img_url"\n')
    files = os.listdir(files_path)
    for file in files:
        tn = re.sub('.jpg', '', re.sub('Screenshot_', '', file)).split('-')
        pr_screen_time = '"'+tn[0]+'-'+tn[1]+'-'+tn[2]+' '+tn[3]+':'+tn[4]+':'+tn[5]+'"'
        pr_create_time = '"'+tn[0]+'-'+tn[1]+'-'+tn[2]+' '+tn[3]+':'+tn[4]+':'+tn[5]+'"'
        pr_update_time = '"'+tn[0]+'-'+tn[1]+'-'+tn[2]+' '+tn[3]+':'+tn[4]+':'+tn[5]+'"'
        file_path = files_path + '\\' + file
        sid = '"cp'+Uploads.encryption(file)+'"'
        with open(file_path, 'rb') as f:
            image_bytes = f.read()
            pr_hit_url = '"'+Helper.save2_image_file_by_base64(save_name='/app_patrol_main_img/app_patrol_' + re.sub('"', '', sid) + '.jpg', file_data=image_bytes)+'"'
            print(pr_hit_url)
        with open(r'C:\Users\<USER>\Desktop\中国农业银行.csv', 'a+', encoding='utf-8') as f:
            f.write(sid+','+pr_pi_sid+','+pr_cpi_sid+','+pr_ai_sid+','+pr_origin+','+pr_ti_sid+','+pr_ci_sid+','+pr_hit_type+','+pr_hit_url+','+pr_screen_time+','+pr_hit_code+','+pr_hit_content+','+pr_vci_sid+','+pr_vli_sid+','+pr_event_sid+','+pr_sw_sid+','+pr_module_name+','+pr_describe+','+pr_positioning+','+pr_content+','+pr_status+','+pr_repairman_sid+','+pr_repair+','+pr_repair_time+','+pr_operator_sid+','+pr_create_time+','+pr_reviewer_sid+','+pr_reviewer_time+','+pr_update_time+','+pr_deleted+','+pr_audit_sid+','+pr_mark_hit_url+','+pr_audit_time+','+pr_img_url+'\n')
