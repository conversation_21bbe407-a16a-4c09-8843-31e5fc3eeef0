import json
import requests
import urllib3

from src.core.signer import sign


class moderation_client(object):

    def __init__(self, ak, sk, endpoint):
        self.accesskey = ak
        self.secretkey = sk
        self.method = 'POST'
        self.hostname = endpoint

    def invoke_http(self, uri, body):
        urllib3.disable_warnings()
        querystring = sign('POST', self.accesskey, self.secretkey, uri)
        params = ''
        for (k, v) in querystring.items():
            params += str(k) + '=' + str(v) + '&'
        params = params[:-1]
        response = requests.post(self.hostname + uri + '?' + params, data=json.dumps(body),
                                 headers={"Content-Type": "application/json"}, timeout=(5, 60), verify=False)
        return response

    # 文本内容审核
    def text_scan(self, body):
        uri = '/api/moderation/v1/text/scan'
        return self.invoke_http(uri, body)

    # 图像内容审核
    def image_scan(self, body):
        uri = '/api/moderation/v1/image/scan'
        return self.invoke_http(uri, body)

    # 图像内容审核(异步批量)
    def image_asyncscans(self, body):
        uri = '/api/moderation/v1/image/asyncscans'
        return self.invoke_http(uri, body)

    # 图像内容审核(结果查询)
    def image_results(self, body):
        uri = '/api/moderation/v1/image/results'
        return self.invoke_http(uri, body)

    # 音频内容审核(异步)
    def audio_asyncscan(self, body):
        uri = '/api/moderation/v1/audio/aysncscan'
        return self.invoke_http(uri, body)

    # 音频内容审核(结果查询)
    def audio_results(self, body):
        uri = '/api/moderation/v1/audio/results'
        return self.invoke_http(uri, body)

    # 视频内容审核(异步)
    def video_asyncscan(self, body):
        uri = '/api/moderation/v1/video/asyncscan'
        return self.invoke_http(uri, body)

    # 视频内容审核(主动查询)
    def video_results(self, body):
        uri = '/api/moderation/v1/video/results'
        return self.invoke_http(uri, body)

    # 文档内容审核
    def doc_asyncscan(self, body):
        uri = '/api/moderation/v1/file/asyncscan'
        return self.invoke_http(uri, body)

    # 文档内容审核(主动查询)
    def doc_results(self, body):
        uri = '/api/moderation/v1/file/results'
        return self.invoke_http(uri, body)
