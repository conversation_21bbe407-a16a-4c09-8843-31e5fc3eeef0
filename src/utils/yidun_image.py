import base64
import hashlib
import json
import random
import time
import uuid
from typing import Optional
import traceback
import requests
from gmssl import sm3, func
import urllib.request as urlrequest
import urllib.parse as urlparse
# from src.core.moderationClient import moderation_client
# from src.sqlite.sql import Sql


class AuditImage:
    audit_code = ['10000', '10001', '10003', '10004', '10005', '10006', '10007', '10008', '10009', '10010', '10200', '10300', '11001', '11002', '11004', '11005',
                  '11006', '11010', '11011', '11014', '11100', '11101', '11102', '11103', '11104', '11105', '11106', '30000', '30001', '30002', '30003', '30004',
                  '30005', '30006', '30007', '300134', '30000297', '40000', '40001', '40002', '40003', '40007', '40008', '40010', '40011', '40012', '40013',
                  '40014', '40015', '40017', '50000', '50001', '50002', '50003', '50004', '50005', '50006', '50007', '80000', '80002', '80005', '80006', '80007',
                  '80008', '80009', '80010', '80011', '90003', '100002', '100003', '100004', '100005', '100006', '100007', '100008', '100147', '100235', '100236',
                  '100376', '300016', '400017', '400021', '400186', '400238', '400239', '400240', '400241', '400242', '400273', '400274', '400275', '400276',
                  '400277', '400446', '400459', '500013', '500014', '500015', '500039', '500040', '500041', '500042', '500043', '500044', '500045', '500070',
                  '500214', '500234', '500245', '500377', '500378', '600018', '600379', '600383', '700019', '700355', '700356', '800001', '900020', '1100101',
                  '1100102', '1100103', '1100104', '1100105', '1100106', '1100107', '1100380', '10000616', '10000617', '10000618', '40000629', '40000630',
                  '40000631', '40000632', '40000633', '40000634', '40000635', '50000635', '50000636', '50000637', '50000638', '50000639', '50000640', '50000641',
                  '50000642', '50000643', '50000644']
    key_word = {'120': '参数错误', '130': '解析错误', '140': '数据类型错误', '160': '视频大小超限（>5G）', '10000': '色情', '10001': '女下体', '10002': '女胸',
                '10003': '男下体', '10004': '性行为', '10005': '臀部', '10006': '口交', '10007': '卡通色情', '10008': '色情人物', '10009': '儿童色情', '10010': '性分泌物',
                '10011': '情趣用品', '10200': '黑屏', '10300': '挂机', '11000': '性感低俗其他', '11001': '亲吻', '11002': '腿部特写', '11003': '非漏点赤膊', '11004': '胸部',
                '11005': '内衣裤', '11006': '女性露沟', '11007': '男性露点赤膊', '11008': '女性露背', '11009': '男性露背', '11010': '臀部低俗', '11011': '裆部', '11012': '吐舌',
                '11013': '足部特写', '11014': '露沟（高召回）', '11015': '床上场景', '11016': '摸胸', '11017': '胸部非裸露特写', '11100': '涉价值观', '11101': '抽烟', '11102': '劣迹艺人',
                '11103': '竖中指', '11104': '喝酒', '11105': '纹身', '11106': '吃播', '20000': '广告', '20001': '广告带文字', '20002': '手写体广告', '20003': '电商广告弹窗',
                '20004': '广告logo', '21000': '二维码', '30000': '暴恐', '30001': '暴恐高级布控', '30002': '暴恐旗帜', '30003': '暴恐人物', '30004': '暴恐标识', '30005': '暴恐场景',
                '30006': '暴恐服饰', '30007': '暴恐血腥', '300134': '暴恐血液', '30000297': '极端主义', '40000': '违禁', '40001': '违禁高级布控', '40002': '违禁品', '40003': '特殊标识', '40005': '公职服饰', '40007': '违禁人物',
                '40008': '违禁场景', '40009': '火焰', '40010': '恐怖灵异', '40011': '货币', '40012': '毒品', '40013': '宗教服饰', '40014': '赌博', '40015': '佛像', '40016': '盾牌',
                '40017': '警用械具', '50000': '涉政', '50001': '涉政高级布控', '50002': '中国地图', '50003': '涉政人物', '50004': '涉政旗帜', '50005': '涉政标识', '50006': '涉政场景',
                '50007': '红头文件', '80000': '恶心图', '80002': '脏器', '80003': '疾病表征', '80004': '密集恐惧', '80005': '腐烂食物', '80006': '排泄物', '80007': '恶心动物',
                '80008': '人体尸体', '80009': '动物尸体', '80010': '血液', '80011': '恶心场景', '90000': '其他', '90002': '自定义用户名单', '90003': '自定义IP名单', '100001': '色情其他',
                '100002': '色情传播', '100003': '色情性器官', '100004': '色情挑逗', '100005': '色情低俗段子', '100006': '色情性行为', '100007': '色情舆情事件', '100008': '色情交友类',
                '100147': '色情交友类-低俗隐晦', '100235': '成人色情', '100236': '儿童色情（海外版）', '100376': '性用品', '110022': '性感其他', '200009': '商业推广', '200010': '广告法',
                '200011': '刷量行为', '200012': '广告其他', '200144': '房产广告', '200268': '拉人广告', '210023': '二维码其他', '260052': '广告法-涉医疗用语（非药品禁止宣传药效）',
                '260053': '广告法-迷信用语', '260054': '广告法-需要凭证（可以写但需要凭证证明）', '260055': '广告法-限时性用语（可以写但必须有具体时间）', '260056': '广告法-涉嫌诱导消费者',
                '260057': '广告法-涉嫌欺诈消费者', '260058': '广告法-法律风险较高', '260059': '广告法-极限词（用语绝对化）', '260279': '广告法-价格夸大营销', '260280': '广告法-效果性承诺或保证',
                '260281': '广告法-网络营销涉嫌欺诈', '260282': '广告法-权威性用语', '300016': '暴恐其他', '400017': '违禁其他', '400021': '违禁网监要求', '400186': '违禁文字-谩骂其他',
                '400238': '暴力血腥（海外版）', '400239': '危害人身安全（海外版）', '400240': '管制物品（海外版）', '400241': '欺诈（海外版）', '400242': '自杀自残（海外版）', '400243': '剥削劳动力（海外版）',
                '400273': '违禁物品', '400274': '赌博', '400275': '违禁工具', '400276': '违禁行为', '400277': '违禁毒品', '400446': '欺诈', '400459': '违禁人物', '500013': '涉政其他',
                '500014': '敏感专项', '500015': '严格涉政', '500039': '时事报道（海外版）', '500040': '核心领导人', '500041': '英雄烈士', '500042': '邪教迷信', '500043': '落马官员',
                '500044': '舆情事件', '500045': '政治综合', '500070': '一号领导人', '500214': '宗教相关', '500234': '仇恨言论（海外版）', '500245': '仇恨宗教（海外版）', '500377': '国外领导人相关',
                '500378': '知名人物', '600018': '谩骂其他', '600379': '轻度谩骂', '600383': '不文明口头禅', '700019': '灌水其他', '700355': '生僻字灌水', '700356': '英文灌水',
                '800001': '性器官特写', '900020': '其他', '1100101': '涉价值观其他', '1100102': '宣扬拜金炫富', '1100103': '宣扬浪费粮食（吃播）', '1100104': '宣扬涉黑文化',
                '1100105': '宣扬腐文化', '1100106': '教唆自杀自残', '1100107': '封建迷信', '1100380': '劣迹人物名', '10000616': 'BDSM', '10000617': '极端色情', '10000618': '恋童癖',
                '40000629': '违禁化学品', '40000630': '违禁药品', '40000631': '违禁作品', '40000632': '野生动植物买卖', '40000633': '个人隐私', '40000634': '儿童邪典', '40000635': '恐怖灵异',
                '50000635': '国内领导人相关', '50000636': '政治违禁作品', '50000637': '反动分裂', '50000638': '政治运动', '50000639': '社会事件', '50000640': '国际局势',
                '50000641': '涉国家机关', '50000642': '涉军警', '50000643': '政治经济关联人物', '50000644': '政治用语表述不规范', '610': '图片下载失败', '620': '图片格式错误', '630': '其它'}
    audit_word = {'100': '色情', '110': '性感低俗', '200': '广告', '210': '二维码', '260': '广告法', '300': '暴恐', '400': '违禁', '500': '涉政', '800': '恶心类', '900': '其他', '1100': '涉价值观'}
    secret_id = "ad18d5ad8e682af5c8b3e978e7329a73"  # 产品密钥ID，产品标识
    secret_key = "04dbf1f5f0d328a64f26d83fec05d119"  # 产品私有密钥，服务端生成签名信息使用，请严格保管，避免泄露
    business_id = "9b7fc3dd36765de0baa1fbe29c60569e"  # 业务ID，易盾根据产品业务特点分配
    API_URL = "http://as.dun.163.com/v5/image/check"
    VERSION = "v5.1"

    @staticmethod
    def gen_signature(params=None):
        """生成签名信息
        Args:
            params (object) 请求参数
        Returns:
            参数签名md5值
        """
        buff = ""
        for k in sorted(params.keys()):
            buff += str(k) + str(params[k])
        buff += AuditImage.secret_key
        if "signatureMethod" in params.keys() and params["signatureMethod"] == "SM3":
            return sm3.sm3_hash(func.bytes_to_list(bytes(buff, encoding='utf8')))
        else:
            return hashlib.md5(buff.encode("utf8")).hexdigest()

    @staticmethod
    def audit_img(obs_url: Optional[str],
                  name:Optional[str]):
        # headers = {
        #     'Content-Type': 'application/json',
        #     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102'
        # }
        # base_data = base64.b64encode(requests.get(obs_url, headers=headers).content).decode()
        # base64_str = base_data.decode()
        flag = True
        for n in range(0, 5):
            try:
                images: list = []
                imageUrl = {
                    "name": name,
                    "type": 2,
                    "data": obs_url
                    # "callbackUrl"  # 主动回调地址url,如果设置了则走主动回调逻辑
                    }
                images.append(imageUrl)
                img_body = {"images": json.dumps(images)}
                img_body["secretId"] = AuditImage.secret_id
                img_body["businessId"] = AuditImage.business_id
                img_body["version"] = AuditImage.VERSION
                img_body["timestamp"] = int(time.time() * 1000)
                img_body["nonce"] = int(random.random() * 100000000)
                img_body["signature"] = AuditImage.gen_signature(img_body)
                params = urlparse.urlencode(img_body).encode("utf8")
                request = urlrequest.Request(AuditImage.API_URL, params)
                img_result = json.loads(urlrequest.urlopen(request, timeout=10).read())
                if img_result['code'] == 200:
                    data = None
                    for result in img_result['result']:
                        if result['antispam']['status'] == 2:
                            if result['antispam']['suggestion'] == 2:
                                img_text = ''
                                img_result_texts = result['antispam']['labels']
                                for img_result_text in img_result_texts:
                                    if 'subLabels' in img_result_text:
                                        labels = img_result_text['subLabels']
                                        for label in labels:
                                            if str(label['subLabel']) in AuditImage.audit_code:
                                                if img_text == '':
                                                    img_text = AuditImage.audit_word[str(img_result_text['label'])]
                                                    # if 'details' in label:
                                                    #     img_text = img_text + '-' + label['details']['hitInfos'][0]['value']
                                                else:
                                                    img_text = img_text + ',' + AuditImage.audit_word[str(img_result_text['label'])]
                                if img_text == '':
                                    img_text = '正常'
                                data = img_text
                            elif result['antispam']['suggestion'] == 0 or result['antispam']['suggestion'] == 1:
                                data = '正常'
                    return data
                else:
                    time.sleep(random.randint(1, 3))
                    continue
            except Exception as e:
                traceback.print_exc()
                print(obs_url)
                time.sleep(random.randint(1, 3))
                continue
        # 审核失败
        print('审核失败!')
        while flag:
            try:
                # Sql.insert_data_no_audit(image_id=image_id, url=url, obs_url=obs_url, error_msg=msg)
                flag = False
            except:
                time.sleep(random.random())
                continue
        return None


