import os
from PIL import Image
import xlwings
import pythoncom

class Utils:
    @classmethod
    def data_write(cls, file_path, data):
        pythoncom.CoInitialize()
        try:
            app = xlwings.App(visible=False, add_book=False)
            # Excel工作簿显示警告,不显示
            app.display_alerts = False
            # 工作簿屏幕更新,不更新
            app.screen_updating = False
            wb = app.books.add()
            sht = wb.sheets('sheet1')
            # 将数据写入第 i 行，第 j 列
            sht.range('A1').value = data
            wb.save(file_path)  # 保存文件
            wb.close()
        except:
            print('表格读取错误!!!')
        app.kill()
        pythoncom.CoUninitialize()

    @classmethod
    def data_read(cls, file_path, table_name):
        # pythoncom.CoInitialize()
        app = xlwings.App(visible=False, add_book=False)
        try:
            # Excel工作簿显示警告,不显示
            app.display_alerts = False
            # 工作簿屏幕更新,不更新
            app.screen_updating = False
            wb = app.books.open(file_path)
            data = wb.sheets[table_name]
            # 获取已编辑的矩形区域,最底部且最右侧的单元格
            last_cell = data.used_range.last_cell
            # 最大行数
            last_row = last_cell.row
            # 最大列数
            last_col = last_cell.column
            table = data.range((1, 1), (last_row, last_col)).value
            # 关闭工作簿
            wb.close()
            # 退出Excel
            app.quit()
        except:
            print('表格读取错误!!!')
        app.kill()
        # pythoncom.CoUninitialize()
        return table

    @staticmethod
    def add_center(sht, target, filePath, match=False, width=None, height=None, column_width=None, row_height=None):
        '''Excel智能居中插入图片
        优先级：match &gt; width &amp; height &gt; column_width &amp; row_height
        建议使用column_width或row_height，定义单元格最大宽或高
        :param sht: 工作表
        :param target: 目标单元格，字符串，如'A1'
        :param filePath: 图片绝对路径
        :param width: 图片宽度
        :param height: 图片高度
        :param column_width: 单元格最大宽度，默认100像素，0 <= column_width <= 1557.285
        :param row_height: 单元格最大高度，默认75像素，0 <= row_height <= 409.5
        :param match: 绝对匹配原图宽高，最大宽度1557.285，最大高度409.5
        '''
        unit_width = 6.107  # Excel默认列宽与像素的比
        rng = sht.range(target)  # 目标单元格
        name = os.path.basename(filePath)  # 文件名
        _width, _height = Image.open(filePath).size  # 原图片宽高
        NOT_SET = True  # 未设置单元格宽高
        # match
        if match:  # 绝对匹配图像
            width, height = _width, _height
        else:  # 不绝对匹配图像
            # width & height
            if width or height:
                if not height:  # 指定了宽，等比计算高
                    height = width / _width * _height
                if not width:  # 指定了高，等比计算宽
                    width = height / _height * _width
            else:
                # column_width &amp; row_height
                if column_width and row_height:  # 同时指定单元格最大宽高
                    width = row_height / _height * _width  # 根据单元格最大高度假设宽
                    height = column_width / _width * _height  # 根据单元格最大宽度假设高
                    area_width = column_width * height  # 假设宽优先的面积
                    area_height = row_height * width  # 假设高优先的面积
                    if area_width > area_height:
                        width = column_width
                    else:
                        height = row_height
                elif not column_width and not row_height:  # 均无指定单元格最大宽高
                    column_width = 100
                    row_height = 75
                    rng.column_width = column_width / unit_width  # 更新当前宽度
                    rng.row_height = row_height  # 更新当前高度
                    NOT_SET = False
                    width = row_height / _height * _width  # 根据单元格最大高度假设宽
                    height = column_width / _width * _height  # 根据单元格最大宽度假设高
                    area_width = column_width * height  # 假设宽优先的面积
                    area_height = row_height * width  # 假设高优先的面积
                    if area_width > area_height:
                        height = row_height
                    else:
                        width = column_width
                else:
                    width = row_height / _height * _width if row_height else column_width  # 仅设了单元格最大宽度
                    height = column_width / _width * _height if column_width else row_height  # 仅设了单元格最大高度
        assert 0 <= width / unit_width <= 255
        assert 0 <= height <= 409.5
        if NOT_SET:
            rng.column_width = width / unit_width  # 更新当前宽度
            rng.row_height = height  # 更新当前高度
        left = rng.left + (rng.width - width) / 2  # 居中
        top = rng.top + (rng.height - height) / 2
        try:
            sht.pictures.add(filePath, left=left, top=top, width=width, height=height, scale=None, name=name)
        except Exception:  # 已有同名图片，采用默认命名
            pass