from src.utils import CommUtils
from DrissionPage import WebPage
from DrissionPage._configs.chromium_options import ChromiumOptions

class Utils:
    @classmethod
    def get_page(cls, port=9101, headless=True, user_data_name=None):
        if user_data_name is None:
            user_data_name = 'user_data_temp'
        browser_path = CommUtils.Utils.get_root_path() + r'\resources\chromium\chrome.exe'
        user_data_path = CommUtils.Utils.get_root_path() + (r'\resources\userdata\%s' % user_data_name)
        co = ChromiumOptions().set_paths(browser_path=browser_path, local_port=port, user_data_path=user_data_path)
        if headless:
            co.set_argument("--headless")
        co.set_argument('--no-sandbox')  # 这里的信息是附加信息，可以不设置，但是推荐设置
        co.set_argument('--disable-gpu')
        co.set_argument("--start-maximized")
        co.set_user_agent(
            'user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102')
        # co.no_imgs(on_off=True)
        page = WebPage(chromium_options=co)
        return page