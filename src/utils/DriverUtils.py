import os
from src.utils import CommUtils
from DrissionPage import WebPage
from DrissionPage._configs.chromium_options import ChromiumOptions

class Utils:
    @classmethod
    def get_page(cls, headless=True, user_data_name=None):
        if user_data_name is None:
            user_data_name = 'user_data_temp'

        # Mac版本的Chrome浏览器路径
        browser_path = '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'

        # 如果标准路径不存在，尝试其他可能的路径
        if not os.path.exists(browser_path):
            # 尝试使用Chromium
            alternative_paths = [
                '/Applications/Chromium.app/Contents/MacOS/Chromium',
                '/usr/bin/google-chrome',
                '/usr/bin/chromium-browser'
            ]
            for alt_path in alternative_paths:
                if os.path.exists(alt_path):
                    browser_path = alt_path
                    break
            else:
                # 如果都找不到，抛出错误
                raise FileNotFoundError(f"未找到Chrome浏览器，请确保Chrome已安装在标准位置: {browser_path}")

        print(f"使用Chrome浏览器路径: {browser_path}")

        # 使用正确的路径分隔符构建用户数据路径
        user_data_path = os.path.join(CommUtils.Utils.get_root_path(), 'resources', 'userdata', user_data_name)

        co = ChromiumOptions()
        co.set_paths(browser_path=browser_path, user_data_path=user_data_path)
        # 不设置端口，让DrissionPage自动分配
        # co.set_local_port(port)
        if headless:
            co.set_argument("--headless")
        co.set_argument('--no-sandbox')  # 这里的信息是附加信息，可以不设置，但是推荐设置
        co.set_argument('--disable-gpu')
        co.set_argument("--start-maximized")
        co.set_argument('--disable-dev-shm-usage')  # Mac上推荐的参数
        co.set_argument('--disable-extensions')  # 禁用扩展以提高稳定性
        co.set_user_agent(
            'user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36')
        # co.no_imgs(on_off=True)
        page = WebPage(chromium_options=co)
        return page