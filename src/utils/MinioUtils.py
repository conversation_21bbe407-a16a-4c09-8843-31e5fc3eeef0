# -*- coding: utf-8 -*- #
import base64
import io
import tempfile
import os
from datetime import timed<PERSON><PERSON>

from minio import Minio
from minio.error import S3Error

# from src.entity import Agent


client = Minio('**************:7671',
               access_key='LTAI4FjKaqTSvAoXXgLwDnMe',
               secret_key='******************************',
               secure=False)

client1 = Minio('minio.miyun.botsmart.cn:9000',
               access_key='YuN2vUABOkg6uMl0',
               secret_key='GUMqhs0lHBwHrXHt84cxyj2kME6HeYyX',
               secure=False)
class Helper:
    bocket_name = 'botsmart'

    @staticmethod
    def check_bucket(bocket=None):
        try:
            if bocket is None:
                bocket = Helper.bocket_name
            found = client.bucket_exists(bocket)
            return found
        except S3Error as e:
            return False
    @staticmethod
    def check_bucket2(bocket=None):
        try:
            if bocket is None:
                bocket = Helper.bocket_name
            found = client1.bucket_exists(bocket)
            return found
        except S3Error as e:
            return False

    @staticmethod
    def save_image_file_by_base64(save_name, file_data):
        if Helper.check_bucket():
            file_data = base64.b64decode(str(file_data, encoding='utf-8'))
            ret = client.put_object("botsmart", save_name, io.BytesIO(file_data), len(file_data),
                                    content_type='image/png')
            return 'http://**************:7671/botsmart' + save_name
        else:
            return False

    @staticmethod
    def save_image_file(save_name, file_data):
        if Helper.check_bucket():
            # file_data = base64.b64decode(str(file_data, encoding='utf-8'))
            ret = client.put_object("botsmart", save_name, io.BytesIO(file_data), len(file_data),
                                    content_type='image/png')
            return 'http://**************:7671/botsmart' + save_name
        else:
            return False

    @staticmethod
    def load_data(file_name):
        data = client.get_object('botsmart', file_name)
        return data.data

    @staticmethod
    def gat_files(files_name):
        datas = client.list_objects(
        'botsmart',
        prefix=files_name + '/',
        recursive=True)
        return datas

    @staticmethod
    def save2_image_file_by_base64(save_name, file_data):
        if Helper.check_bucket2():
            # file_data = base64.b64decode(str(file_data, encoding='utf-8'))
            ret = client1.put_object("botsmart", save_name, io.BytesIO(file_data), len(file_data),
                                    content_type='image/png')
            return 'http://minio.miyun.botsmart.cn:9000/botsmart' + save_name
        else:
            return False

    @staticmethod
    def save_zip_file(save_name, filepath):
        if Helper.check_bucket2():
            ret = client1.fput_object("botsmart", save_name, filepath, content_type='application/zip')
            return 'http://minio.miyun.botsmart.cn:9000/botsmart/' + save_name
        else:
            return False

    @staticmethod
    def save_pdf_file_by_base64(save_name, file_data):
        if Helper.check_bucket():
            if isinstance(file_data, str):
                file_data = base64.b64decode(file_data)
            ret = client.put_object("botsmart", save_name, io.BytesIO(file_data), len(file_data),
                                    content_type='application/pdf')
            return 'http://**************:7671/botsmart' + save_name
        else:
            return False

    @staticmethod
    def save_image_file_by_path(save_name, file_name):
        if Helper.check_bucket():
            client.fput_object("botsmart", save_name, file_name, content_type='image/png')
            return 'http://**************:7671/botsmart' + save_name
        else:
            return False

    @staticmethod
    def save_file_by_path(save_name, file_name):
        if Helper.check_bucket():
            client.fput_object("botsmart", save_name, file_name)
            return 'http://**************:7671/botsmart' + save_name
        else:
            return False

    @staticmethod
    def save1_file_by_path(save_name, file_name):
        if Helper.check_bucket2():
            client1.fput_object("botsmart", save_name, file_name)
            return 'http://minio.miyun.botsmart.cn:9000/botsmart' + save_name
        else:
            return False

    @staticmethod
    def save2_file_by_path(save_name, file_data):
        if Helper.check_bucket2():
            file_data = base64.b64decode(str(file_data, encoding='utf-8'))
            ret = client1.put_object("botsmart", save_name, io.BytesIO(file_data), len(file_data), content_type='video/mp4')
            return 'http://minio.miyun.botsmart.cn:9000/botsmart' + save_name
        else:
            return False


    @staticmethod
    def delete(save_name):
        if Helper.check_bucket():
            client.remove_object("botsmart", save_name)

    @staticmethod
    def delete1(save_name):
        if Helper.check_bucket2():
            client1.remove_object("botsmart", save_name)



