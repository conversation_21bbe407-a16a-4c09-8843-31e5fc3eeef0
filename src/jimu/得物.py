import datetime
import time

import pythoncom
import xlrd
import uiautomator2 as u2
import random

import xlwings
import xlwt
from src.payment import payment
from src.v2.IP import switchover_IP



class dw:
    def __init__(self, d):
        self.d = d

    @staticmethod
    def data_read(file_path, table_name, array):
        data = xlrd.open_workbook(file_path)
        table = data.sheet_by_name(table_name)
        return table.col_values(array)

    @staticmethod
    def data_read2(file_path, table_name):
        pythoncom.CoInitialize()
        app = xlwings.App(visible=False, add_book=False)
        # Excel工作簿显示警告,不显示
        app.display_alerts = False
        # 工作簿屏幕更新,不更新
        app.screen_updating = False
        wb = app.books.open(file_path)
        data = wb.sheets[table_name]
        # 获取已编辑的矩形区域,最底部且最右侧的单元格
        last_cell = data.used_range.last_cell
        # 最大行数
        last_row = last_cell.row
        # 最大列数
        last_col = last_cell.column
        table = data.range((1, 1), (last_row, last_col)).value
        # 关闭工作簿
        wb.close()
        # 退出Excel
        app.quit()
        pythoncom.CoUninitialize()
        return table

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def data_write2(file_path, data):
        pythoncom.CoInitialize()
        app = xlwings.App(visible=False, add_book=False)
        # Excel工作簿显示警告,不显示
        app.display_alerts = False
        # 工作簿屏幕更新,不更新
        app.screen_updating = False
        wb = app.books.add()
        sht = wb.sheets('sheet1')
        # 将数据写入第 i 行，第 j 列
        sht.range('A1').value = data
        wb.save(file_path)  # 保存文件
        wb.close()
        app.kill()
        pythoncom.CoUninitialize()

    @staticmethod
    def jt(d):
        image = d.screenshot()
        image_path = "C:\\得物\\截图_"+time.strftime("%Y%m%d_%H%M%S", time.localtime())+".jpg"
        image.save(image_path)

    @staticmethod
    def collect(d):
        li = dw.data_read2(file_path='C:/Users/<USER>/Desktop/搜索词.xlsx', table_name='搜索词')
        d.xpath('//*[@resource-id="com.shizhuang.duapp:id/iv_tab_trend_dynamic"]').click()
        d.sleep(1)
        d.xpath('//*[@resource-id="com.shizhuang.duapp:id/ivSearch"]').click()
        d.sleep(1)
        # d.xpath('//*[@text="用户"]').click()
        # d.sleep(1)
        for l in range(12045, 80000):
            element = d.xpath('//*[@resource-id="com.shizhuang.duapp:id/et_search"]').all()[0]
            element.click()
            d.sleep(1)
            d.clear_text()
            d.sleep(1)
            d.send_keys(str(li[l]))
            d.sleep(1)
            # d.xpath('//*[@text="搜索"]').click()
            d.send_action("search")
            d.sleep(1)
            d.xpath('//*[@text="用户"]').click()
            d.sleep(5)
            if (len(d.xpath('//*[@resource-id="com.shizhuang.duapp:id/errorText" and @text="暂未找到想要的结果，"]').all()) == 0) and (
                len(d.xpath('//*[@text="没有找到相关用户，你是否想搜索"]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="com.shizhuang.duapp:id/errorText" and @text="抱歉，暂未找到相关内容，"]').all()) == 0
            ):
                dw.jt(d)
                print('搜索:'+str(l))
            d.press("back")
            d.sleep(2)
            # d.xpath('//*[@resource-id="com.yinxiang:id/search"]').click()
            # d.press("back")
                # elements = d.xpath('//*[@resource-id="root"]/android.view.View[2]/android.view.View[2]/android.view.View[1]/android.view.View').all()
                # for e in elements:
                #     e.click()
                #     d.sleep(4)
                #     if len(d.xpath('//*[@text="什么都没有呢～～"]').all()) != 1:
                #         tc58.jt(d)
                #     d.press("back")
                #     d.sleep(2)
            # d.xpath('//*[@text="帖子"]').click()
            # d.sleep(3)
            # if len(d.xpath('//*[@text="换个关键词试试"]').all()) == 0 and len(d.xpath('//*[@text="没有找到相关内容"]').all()) == 0:
            #     tc58.jt(d)
            #     print('帖子:'+str(l))

        return 1


    def run(self):
        d = self.d
        d.app_start('com.wuba.zhuanzhuan', stop=True)
        d.sleep(7)
        dw.collect(d)


if __name__ == '__main__':
    # id = "3a699a47"
    id = "96e2aec0"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="com.shizhuang.duapp:id/ivCoupon"]').when(xpath='//*[@resource-id="com.shizhuang.duapp:id/iv_close"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text="Close"]').click()
    d.watcher.start()
    d.app_start('com.shizhuang.duapp', stop=True)

    d.sleep(7)
    dw.collect(d)