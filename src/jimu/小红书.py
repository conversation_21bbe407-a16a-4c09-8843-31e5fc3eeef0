import base64
import datetime
import json
import os
import time
import uuid

import xlrd
import uiautomator2 as u2
import random
import xlwt
from src.core.moderationClient import moderation_client



class xhs:
    key_word = {'10001103': '核心领导人', '10201110': '邪教', '10201111': '反动分裂', '10301102': '色情辱骂', '10401104': '低俗',
                '10601103': '普通广告', '10601104': '违法广告', '10701105': '毒品禁药', '10701106': '涉嫌违法品',
                '10701107': '违禁封杀作品', '200100': '正常', '10001104': '领导人变体', '10001105': '省部级以上领导人',
                '10001106': '港澳台领导人', '10001107': '领导人家属', '10001108': '国外领导人', '10001109': '知名人物',
                '10001110': '英烈', '10001111': '下马官员', '10001112': '问题艺人', '10001113': '负面人物',
                '10001114': '涉敏感事件（“六四”等）', '10001115': '危害国家安全', '10001116': '攻击党和政体', '10001117': '其它涉政敏感表述',
                '10301103': '疑似女优', '10201112': '恐怖事件', '10002100': '涉政人物', '10002101': '旗帜标识', '10002102': '政治事件',
                '10002113': '中国地图', '10002114': '天安门', '10002115': '落马官员', '10002400': '公务车辆人', '10202100': '血腥人',
                '10202101': '爆炸火灾', '10202102': '管制刀具', '10202103': '枪支', '10202106': '暴恐标识', '10302100': '真人色情',
                '10302101': '动漫色情', '10402101': '男性露点赤膊', '10402102': '女性性感', '10402103': '儿童露点',
                '10402104': '劣迹艺人', '10602101': '图片文字广告', '10602102': '二维码', '10702105': '纹身', '10702101': '赌博',
                '10702103': '吸毒', '10702102': '疑似毒品', '300100': '自定义黑名单', '300200': '自定义白名单', '10702104': '吸烟',
                '10002116': '英烈人物', '10002117': '负面知名人物', '10002300': '人群聚众', '10002500': '宗教', '10202104': '军警服饰',
                '10202105': '大型军事武器', '103103': '声纹鉴黄'}
    ak = 'be5f4254be4867ffbb1eae9b549dedea'
    sk = '92031a6102e43a9276f844c61843fa1d'
    endpoint = 'https://api.botsmart.cn'
    client = moderation_client(ak, sk, endpoint)
    def __init__(self, d):
        self.d = d

    @staticmethod
    def data_read(file_path, table_name, array):
        data = xlrd.open_workbook(file_path)
        table = data.sheet_by_name(table_name)
        return table.col_values(array)


    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def jt(d):
        image = d.screenshot()
        image_path = "C:\\采集\\小红书\\截图_"+time.strftime("%Y%m%d_%H%M%S", time.localtime())+".jpg"
        image.save(image_path)
        return image_path

    @staticmethod
    def collect(d):
        while True:
            if len(d.xpath('//*[@text="首页"]').all()) == 1:
                d.xpath('//*[@text="首页"]').click()
                d.sleep(2)
                img_path = xhs.jt(d)
                d.sleep(1)
                at = xhs.Audit_text(d)
                ai = xhs.Audit_img(img_path)
                if at == '正常' and ai == '正常':
                    os.remove(path=img_path)
                else:
                    aa = img_path.replace('C:\\采集\\', '') + '|' + at + '|' + ai + '\n'
                    with open("C:\\采集\\小红书\\审核结果.txt", "a+") as f:
                        f.write(aa)

    @staticmethod
    def Audit_img(image_path):
        image_base64 = str(base64.b64encode(open(image_path, 'rb').read()), encoding='utf8')
        img_body = {'dataId': ''.join(str(uuid.uuid4()).split('-')),
                    'url': image_base64,
                    'scenesId': 'op7le5ot446i'}
        img_result = xhs.client.image_scan(img_body)
        try:
            img_text = xhs.key_word[str(json.loads(img_result.text)['data']['labels'][0]['label'])]
        except:
            img_text = '审核异常'
        return img_text

    @staticmethod
    def Audit_text(d):
        text_element_list = d.xpath('//*[@text != "" or @content-desc != ""]').all()
        notice_text_list = d.xpath(
            '//*[@resource-id="com.android.systemui:id/status_bar_container"]' + '/descendant-or-self::*[@text != "" or @content-desc != ""]').all()
        text_all = ''
        text = ''
        for text_element in text_element_list:
            next_flag = False
            for notice_element in notice_text_list:
                if notice_element.get_xpath() == text_element.get_xpath():
                    next_flag = True
                    break
            if next_flag:
                continue
            if text_element.info['contentDescription'] is not None and text_element.info[
                'contentDescription'] != '':
                text = text_element.info['contentDescription']
            if text_element.text != '':
                text = text_element.text
            text_all = text_all + text
        if text_all != '' and text_all != ' ':
            nickname_body = {'dataId': ''.join(str(uuid.uuid4()).split('-')),
                             'text': text_all,
                             'scenesId': '7vdxtwsye1ff'}
            nickname_result = xhs.client.text_scan(nickname_body)
            try:
                nickname_text = xhs.key_word[str(json.loads(nickname_result.text)['data']['labels'][0]['label'])]
            except:
                nickname_text = '审核异常'
        else:
            nickname_text = '正常'
        return nickname_text

    def run(self):
        d = self.d
        d.app_start('com.xingin.xhs', stop=True)
        d.sleep(7)
        xhs.collect(d)


if __name__ == '__main__':
    # id = "3a699a47"
    id = "DEHIOFG6WS5DS8AA"
    d = u2.connect(id)
    # d.watcher.when(xpath='//*[@resource-id="com.wuba:id/message" and @text="Wi-Fi环境下视频即将自动播放"]').when(xpath='//*[@resource-id="com.wuba:id/negativeButton"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text="Close"]').click()
    # d.watcher.start()
    d.app_start('com.xingin.xhs', stop=True)

    d.sleep(7)
    xhs.collect(d)