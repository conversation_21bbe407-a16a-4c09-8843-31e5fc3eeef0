import datetime
import time
import xlrd
import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
from src.v2.IP import switchover_IP



class tc58:
    def __init__(self, d):
        self.d = d

    @staticmethod
    def data_read(file_path, table_name, array):
        data = xlrd.open_workbook(file_path)
        table = data.sheet_by_name(table_name)
        return table.col_values(array)


    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def jt(d):
        image = d.screenshot()
        image_path = "D:\\比心\\截图_"+time.strftime("%Y%m%d_%H%M%S", time.localtime())+".jpg"
        image.save(image_path)

    @staticmethod
    def collect(d):
        li = tc58.data_read(file_path='C:/Users/<USER>/Desktop/搜索词全.xls', table_name='Sheet1', array=0)
        d.xpath('//*[@resource-id="com.yitantech.gaigai:id/searchView"]').click()
        d.sleep(2)
        # d.xpath('//*[@resource-id="com.wuba:id/fragment_rn_discovery_container"]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]').click()
        # d.sleep(2)
        # d.xpath('//*[@text="用户"]').click()
        # d.sleep(1)
        for l in range(41, 22763):
            element = d.xpath('//*[@resource-id="com.yitantech.gaigai:id/editText"]').all()[0]
            element.click()
            d.sleep(1)
            element.click()
            d.sleep(1)
            d.clear_text()
            d.sleep(1)
            d.send_keys(str(li[l]))
            d.sleep(1)
            d.xpath('//*[@text="搜索"]').click()
            d.sleep(2)
            if len(d.xpath('//*[@resource-id="com.yitantech.gaigai:id/loading_desc" and @text="空空如也"]').all()) == 1 or len(d.xpath('//*[@resource-id="com.yitantech.gaigai:id/tvResult" and @text="没有找到相关结果"]').all()) == 1:
                continue
            else:
                tc58.jt(d)
                print(str(li[l]))
        return 1


    def run(self):
        d = self.d
        d.app_start('com.yitantech.gaigai', stop=True)
        d.sleep(7)
        tc58.collect(d)


if __name__ == '__main__':
    # id = "3a699a47"
    id = "de1b402"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="com.yitantech.gaigai:id/ivActivityImg"]').press("back")
    # d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text="Close"]').click()
    d.watcher.start()
    d.app_start('com.yitantech.gaigai', stop=True)

    d.sleep(7)
    tc58.collect(d)