# coding: utf-8
#
import datetime
import time
import xlrd
import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
from src.v2.IP import switchover_IP
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class jimu_collect:
    def __init__(self, d):
        self.d = d

    @staticmethod
    def data_read(file_path, table_name, array):
        data = xlrd.open_workbook(file_path)
        table = data.sheet_by_name(table_name)
        return table.col_values(array)


    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def jt(d):
        image = d.screenshot()
        image_path = "D:\\转转\\zz_"+time.strftime("%Y%m%d_%H%M%S", time.localtime())+".jpg"
        image.save(image_path)

    @staticmethod
    def collect(d):
        li = jimu_collect.data_read(file_path='C:/Users/<USER>/Desktop/搜索词全.xls', table_name='Sheet1', array=0)
        d.xpath('//*[@resource-id="com.wuba.zhuanzhuan:id/aa3"]').click()
        d.sleep(1)
        d.xpath('//*[@resource-id="com.wuba.zhuanzhuan:id/csf"]').click()
        d.sleep(1)
        d.xpath('//*[@text="用户"]').click()
        d.sleep(1)
        for l in range(11001, 12001):
            element = d.xpath('//*[@resource-id="com.wuba.zhuanzhuan:id/cra" or @resource-id="com.wuba.zhuanzhuan:id/crr"]').all()[0]
            element.click()
            d.sleep(1)
            d.clear_text()
            d.sleep(1)
            d.send_keys(str(li[l]))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.wuba.zhuanzhuan:id/cr3" and @text="搜索"]').click()
            d.sleep(2)
            if len(d.xpath('//*[@resource-id="com.wuba.zhuanzhuan:id/cs6"]').all()) == 0 and len(d.xpath(
                    '//*[@resource-id="com.wuba.zhuanzhuan:id/ebn" and @text="没有找到您搜索的用户！\n换个关键词试试"]').all()) == 1:
                continue
            elif len(d.xpath('//*[@resource-id="com.wuba.zhuanzhuan:id/cs6"]').all()) > 0:
                jimu_collect.jt(d)
                print(str(li[l]))
        return 1


    def run(self):
        d = self.d
        d.app_start('com.wuba.zhuanzhuan', stop=True)
        d.sleep(7)
        jimu_collect.collect(d)


if __name__ == '__main__':
    # id = "3a699a47"
    id = "62c2612a"
    d = u2.connect(id)
    # d.watcher.when(xpath='//*[@text="系统提示"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text="Close"]').click()
    # d.watcher.start()
    d.app_start('com.wuba.zhuanzhuan', stop=True)

    d.sleep(7)
    jimu_collect.collect(d)