import json
import re
import time

import uiautomator2 as u2
import xlwt as xlwt


class yxbj:
    def __init__(self, d):
        self.d = d

    # @staticmethod
    # def jt(d):
    #     image = d.screenshot()
    #     image_path = "C:\\印象笔记\\" + time.strftime("%Y%m%d_%H%M%S", time.localtime()) + ".jpg"
    #     image.save(image_path)
    #
    # @staticmethod
    # def dy(d):
    #     duy = d.xpath("//*[@resource-id='publish_time']").all()
    #     d.sleep(2)
    #     if len(duy) == 0:
    #         d.sleep(2)
    #         yxbj.jt(d)
    #     else:
    #         d.press("back")

    @staticmethod
    def collect(d):
        # d.click(720, 2132)
        for i in range(100000):
            d.sleep(1)
            d.swipe(20, 2000, 40, 350, 0.4)
            d.sleep(2)
            lis = d.xpath('//*[@resource-id="com.everhub.galaxy:id/from_wechat_clipper_logo"]/parent::android.view.ViewGroup[1]/parent::android.view.ViewGroup[1]/preceding-sibling::android.widget.TextView[@resource-id="com.everhub.galaxy:id/title"]').all()
            d.sleep(1)
            for a in lis:
                tt = a.text
                a.click()
                d.sleep(4)
                duy = d.xpath('//*[@resource-id="activity-name"]').all()
                d.sleep(2)
                duy = d.xpath('//*[@resource-id="activity-name"]').all()
                le = len(duy)
                if le != 0:
                    # image = d.screenshot()
                    # image_path = "C:\\印象笔记\\正常\\" + time.strftime("%Y%m%d_%H%M%S", time.localtime()) + ".jpg"
                    # image.save(image_path)
                    # title = d.xpath('//*[@resource-id="com.yinxiang:id/title"]').text
                    # bb = "正常 | " + title + ' | ' + image_path + '\n'
                    # # fileObject = open(r'zhengchang.json', 'a+', encoding='utf-8')
                    # # fileObject.write(json.dump(title, fp=False) + '\n')
                    # # fileObject.close()
                    # with open("zc.txt", "a+") as f:
                    #     f.write(bb)
                    #     # json.dump(bb, f)
                    d.press("back")
                else:
                    title1 = d.xpath('//*[@resource-id="com.everhub.galaxy:id/title"]').text
                    d.press("back")
                    image = d.screenshot()
                    image_path = "C:\\印象笔记\\非正常\\" + re.sub('丨| |\|', '', tt) + ".jpg"
                    image.save(image_path)
                    aa = "非正常|" + title1.replace('剪藏来源:', '') + '|' + image_path + '|' + tt + '\n'
                    with open("C:\\印象笔记\\zc.txt", "a+") as f:
                        f.write(aa)
                        # json.dump(aa, f)
                    print(tt)
                    # fileObject = open(r'feizhengchang.json', 'a+', encoding='utf-8')
                    # fileObject.write(json.dump(title1, fp=False) + '\n')
                    # fileObject.close()



if __name__ == '__main__':
    ip = '4TS8PRSG9DZPGYD6'
    d = u2.connect(ip)
    d.app_start('com.everhub.galaxy', stop=True)
    d.sleep(5)
    yxbj.collect(d)
