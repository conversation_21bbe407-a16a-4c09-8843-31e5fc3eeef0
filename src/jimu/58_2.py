import datetime
import time
import xlrd
import uiautomator2 as u2
import random

import xlwings
import xlwt
import pythoncom
from src.payment import payment
from src.v2.IP import switchover_IP



class tc58:
    def __init__(self, d):
        self.d = d

    @staticmethod
    def data_read(file_path, table_name, array):
        data = xlrd.open_workbook(file_path)
        table = data.sheet_by_name(table_name)
        return table.col_values(array)

    @staticmethod
    def data_read2(file_path, table_name):
        pythoncom.CoInitialize()
        app = xlwings.App(visible=False, add_book=False)
        # Excel工作簿显示警告,不显示
        app.display_alerts = False
        # 工作簿屏幕更新,不更新
        app.screen_updating = False
        wb = app.books.open(file_path)
        data = wb.sheets[table_name]
        # 获取已编辑的矩形区域,最底部且最右侧的单元格
        last_cell = data.used_range.last_cell
        # 最大行数
        last_row = last_cell.row
        # 最大列数
        last_col = last_cell.column
        table = data.range((1, 1), (last_row, last_col)).value
        # 关闭工作簿
        wb.close()
        # 退出Excel
        app.quit()
        pythoncom.CoUninitialize()
        return table

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def data_write2(file_path, data):
        pythoncom.CoInitialize()
        app = xlwings.App(visible=False, add_book=False)
        # Excel工作簿显示警告,不显示
        app.display_alerts = False
        # 工作簿屏幕更新,不更新
        app.screen_updating = False
        wb = app.books.add()
        sht = wb.sheets('sheet1')
        # 将数据写入第 i 行，第 j 列
        sht.range('A1').value = data
        wb.save(file_path)  # 保存文件
        wb.close()
        app.kill()
        pythoncom.CoUninitialize()
    @staticmethod
    def jt(d):
        image = d.screenshot()
        image_path = "D:\\58\\截图_"+time.strftime("%Y%m%d_%H%M%S", time.localtime())+".jpg"
        image.save(image_path)

    @staticmethod
    def collect(d):
        li = tc58.data_read(file_path='C:/Users/<USER>/Desktop/搜索词全.xls', table_name='Sheet1', array=0)
        d.xpath('//*[@text="发现"]').click()
        d.sleep(2)
        d.xpath('//*[@resource-id="com.wuba:id/fragment_rn_discovery_container"]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]').click()
        d.sleep(2)
        # d.xpath('//*[@text="用户"]').click()
        # d.sleep(1)
        for l in range(10063, 11001):
            element = d.xpath('//*[@resource-id="input"]').all()[0]
            element.click()
            d.sleep(1)
            d.clear_text()
            d.sleep(1)
            d.send_keys(str(li[l]))
            d.sleep(1)
            d.xpath('//*[@text="搜索"]').click()
            d.sleep(2)
            if len(d.xpath('//*[@text="换个关键词试试"]').all()) == 1 and len(d.xpath('//*[@text="没有找到相关内容"]').all()) == 1:
                continue
            else:
                tc58.jt(d)
                print(str(li[l]))
        return 1


    def run(self):
        d = self.d
        d.app_start('com.wuba', stop=True)
        d.sleep(7)
        tc58.collect(d)


if __name__ == '__main__':
    # id = "3a699a47"
    id = "62c2612a"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="com.wuba:id/message" and @text="Wi-Fi环境下视频即将自动播放"]').when(xpath='//*[@resource-id="com.wuba:id/negativeButton"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text="Close"]').click()
    d.watcher.start()
    d.app_start('com.wuba', stop=True)

    d.sleep(7)
    tc58.collect(d)