from src.澳门赌场_收集_c265b801 import amdc_collect
from src.v2.开元棋牌 import kyqp_collect
from src.v2.澳门巴黎人黑 import amblrh_collect
from src.v2.太阳城 import tyc_collect
from src.v3.星际娱乐 import xjyl_collect
from src.v2.IP import switchover_IP
from src.jimu.积木_截图 import jimu_collect
import threading
import uiautomator2 as u2
import time

class test:
    #手机设置, "3a699a47"
    id = ["62c2612a", "96e2aec0", "KBMBGIEYOF9SPN5P"]

    if __name__ == '__main__':

        #app脚本设置  , "xjyl_collect"
        app = "jimu_collect"

        #手机循环
        for i in range(1, len(id) + 1):
            s = 'd%s=u2.connect(id[%s])' % (i, i - 1)
            exec(s)

        #app脚本循环
        for x in range(1,10000):
            for i in range(1, 4):
                #运行脚本
                s = 't%s = %s(d%s)\nthread%s = threading.Thread(target=t%s.run)\nthread%s.start()' % (i,app,i,i,i,i)
                exec(s)

            time.sleep(1800)
            # for i in range(1, len(app)+1):
            #     #运行脚本
            #     s = 'thread%s.stop()' % (i)
            #     exec(s)
            # a = app.pop(0)
            # app.append(a)




