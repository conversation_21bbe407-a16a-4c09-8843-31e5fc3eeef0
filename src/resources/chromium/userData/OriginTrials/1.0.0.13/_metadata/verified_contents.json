[{"description": "treehash per file", "signed_content": {"payload": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "YQ3bA-EV7C3PaG_SnIbfTSwU1AwZtGpsZ6QFPw-_VbUhBWySX2efppu8GX0fliZRHW6KEP7fjynCV_qNtcgrpl8BjSO-1nmB1KrigfT4kHv6uBh8h_SXujgGRjIPAXCWPLYKco-hqE9tTuQPKmzn_-Zc9GgJpl5lEAsu6UTzjrvVmzKkgkbdcesMNSwbrvyDffx2nikl2p_7U3IkHNyd7hLpsCvZV8VqwCHwC6pOuggw5kmNjLwxmRnjA_Emy9mMXEUEofyh7EEOs9BaUNsokg7qXuxkrMz4S0ja5VB6ZVmBO5Wlvexk3EXD-yDCykgMDxk2WZGpW1JtkYnpOMqgGQ"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "W9LRESuiylidkd-XDuFWN18wHXTE2O2h4LMHyRJC87BZ8BdR5KCgp528s54GhpMIrHSNC4JiORY5wQzo84yXCR_345Mqqb16XR2v04dUleCRIQFdeiCcWYXULMEi1FLtISaMsWEBfjRxsAGvRzkBHWqQmc0Jph-PeeKoL9SvAECj_FHAB8RGdkuldpmWQ9jRyJDBbo9s7cuXjiLoyMo3x64IhDFhg3NnP5RflfkJnmVK1zOYFW6ZOZIAJH0whRbvoWPSC4GrCiebmUju55ECte7wkARkODKei0cKr3ODQ2nM0b02CssD_mnDKzif8fAhrr1R2gfOQW2ZAOKDYeubrg"}]}}]