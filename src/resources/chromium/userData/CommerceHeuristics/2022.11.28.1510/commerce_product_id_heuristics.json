{"product_element": {"checkout.iherb.com": "product id: ([\\w|-]+)", "www.electronicexpress.com": "<a href=\"#modal-(\\w+)", "www.honest.com": "data-pid=\"(\\w+)\"", "www.hydroflask.com": "data-cart-item-id=\"(\\w+)\"", "www.otterbox.com": "sku:\\s*</span>\\s*<span class=\"value\">([\\w|-]+)", "www.raneystruckparts.com": "data-product=\"(\\w+)\"", "www.sephora.com": "\\/sku\\/s(\\w+)", "www.wrangler.com": "data-pid=\"([\\w|:]+)\"", "www.zappos.com": "StockId</dt><dd class=\"[\\w|-]+\">(\\w+)"}, "product_image_url": {"iwae.com": "-(\\w+)-01\\.\\d+\\.jpg", "www.chewy.com": "img\\.chewy\\.com\\/is\\/image\\/catalog\\/(\\w+)_", "www.homesquare.com": "(images.cymax.com/Images/\\d+/(\\w+)-)", "www.kiehls.com": "-(\\d{13})-", "www.laroche-posay.us": "-(\\w+)-1\\.jpg", "www.northerntool.com": "\\/(\\w+)_65x65\\.jpg", "www.wish.com": "(\\w+)-\\d+-medium"}, "product_url": {"secure.houseofbeautyworld.com": "houseofbeautyworld\\.com\\/(\\w+)\\.html", "secure.newegg.com": "\\?item=(\\w+)$", "us.sourcebmx.com": "\\?variant=(\\w+)$", "www.acehardware.com": "acehardware\\.com\\/product\\/(\\w+)$", "www.adidas.com": "\\.html\\?forceSelSize=(\\w+)_", "www.ashleyfurniture.com": "\\/(\\w+)\\.html", "www.autozone.com": "\\/(\\w+)_0_0", "www.balsamhill.com": "sku=(\\w+)$", "www.bareminerals.com": "\\/(\\w+)\\.html", "www.bathandbodyworks.com": "-(\\w+)\\.html", "www.bedbathandbeyond.com": "\\?skuid=(\\w+)", "www.belk.com": "\\/(\\w+).html", "www.bhphotovideo.com": "\\/product\\/([\\w|-]+)\\/", "www.biglots.com": "\\?skuId=(\\w+)$", "www.build.com": "\\?uid=(\\w+)$", "www.crutchfield.com": "\\/p_(\\w+)\\/", "www.fossil.com": "\\/(\\w+)\\.html", "www.gnc.com": "pid=(\\w+)$", "www.harborfreight.com": "-(\\w+)\\.html", "www.hibbett.com": "\\/(\\w+).html", "www.homedepot.com": "\\/(\\w+)$", "www.katespade.com": "\\/(\\w+)\\.html", "www.kohls.com": "\\.jsp\\?skuId=(\\w+)$", "www.lowes.com": "\\/(\\w+)$", "www.merrell.com": "\\/(\\w+)\\.html", "www.officedepot.com": "&pr=&customerEnteredSku=(\\w+)&cm_cat=", "www.orientaltrading.com": "-a2-(\\w+)\\.fltr\\?source=shoppingcart", "www.partycity.com": "-(\\w+)\\.html", "www.paulaschoice.com": "-(\\w+)\\.html", "www.petco.com": "-(\\w+)$", "www.rodanandfields.com": "\\/p\\/(\\w+)$", "www.shoecarnival.com": "-(\\w+)\\.html", "www.sideshow.com": "-(\\w+)$", "www.sweetwater.com": "\\/(\\w+)$", "www.target.com": "\\/-\\/A-(\\w+)$", "www.thecompanystore.com": "Cart-AddToWishlist\\?pid=([\\w|-]+)$", "www.tumi.com": "-(\\w+)$", "www.walmart.com": "\\/(\\w+)$", "www.webstaurantstore.com": "\\/(\\w+)\\.html"}}