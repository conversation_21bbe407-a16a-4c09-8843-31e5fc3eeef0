[{"description": "treehash per file", "signed_content": {"payload": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "DO1CJ-7b9hHOizXUvs_buCbfB54UBBHBA-hrFB1v3jJ7g5ckYU7Pjq20k_P3hvsEFVuRk1Sbjevmn_2iQBCleeRnwYMO-Pc6d1oiXyBhok9OypF9lmTGw1WptY00_s7iUYeXDZoNup83pnXZxyPsJiB06q1yO7KfqC4NGPoyPcg"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "VYcu2y59NQlc3msFU-oNXRpqPkba4CAI7EAFwYrxrrgVr4Ez7z363J3fUU4aOn5NBGVZA9BhIyHiLg7PhmU2h9QgJM0r1hzApTRU1pXEogLuzv_Vap5QavcIhesFj7pLfEQq_Ih2HydmmWWbChMigSMXqAq9rvpNoc5Sd7ZPLNwktGOxsedf74VMbNKq40bl45_ERV6XLKYMP3znNTYz1LwnkIeHdWeb2bPumlDNtNg9-VfVZSfbOFrPv5E39-d4E8vCHYT6aC5jEQV-8pQfHWulsW2jGPYzsjmsB64pwjySF1zi_WeFt7D2wkA-j_1zK9fzhPVQL0UOZIolyRTJTw"}]}}]