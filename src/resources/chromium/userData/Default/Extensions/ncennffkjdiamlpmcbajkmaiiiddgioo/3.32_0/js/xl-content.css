.xl-chrome-ext-bar {
  width: 98px;
  height: 24px;
  background: rgba(255, 255, 255, 1);
  opacity: 1;
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px 0 6px;
  box-sizing: border-box;
}

.xl-chrome-ext-bar__logo {
  width: 16px;
  height: 16px;
  overflow: hidden;
  background: url(data:image/png;base64,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) no-repeat;
  background-size: 100% 100%;
}

.xl-chrome-ext-bar__option {
  margin-left: -5px;
  margin-top: -1px;
  width: 48px;
  height: 16px;
  font-size: 12px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  line-height: 16px;
  color: rgba(26, 26, 26, 1) !important;
  opacity: 1;
  text-decoration: none !important;
}

.xl-chrome-ext-bar__close {
  width: 8px;
  height: 8px;
  overflow: hidden;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAABHNCSVQICAgIfAhkiAAAAIhJREFUOI2Vk8sRgCAMBVcrSEmWaAdagh1IB1qKHeAFZjgEeMmJT3YzQwLABTyAoYcBd+FIQA5IrOTmwmLAK0pa+G1zFUkXViX1zoVnklOBe5IjAnuSIbwOJLmzlqq3r6222IWNwJyM+jyVTIfEkYRgT3LUw/qZ1D63kn0pG4AN+ARBlaRAfj9+mPlMwSu4q+0AAAAASUVORK5CYII=) no-repeat;
  background-size: 100% 100%;
}

#ncennffkjdiamlpmcbajkmaiiiddgioo-overlay {
  background-color: transparent !important;
  cursor: default !important;
  -webkit-transition: opacity 0.2s linear !important;
  transition: opacity 0.2s linear !important;
}

.ncennffkjdiamlpmcbajkmaiiiddgioo-loading #ncennffkjdiamlpmcbajkmaiiiddgioo-overlay,
.ncennffkjdiamlpmcbajkmaiiiddgioo-exiting #ncennffkjdiamlpmcbajkmaiiiddgioo-overlay {
  opacity: 0 !important;
}

/*
 * Selcted elements
 */

.ncennffkjdiamlpmcbajkmaiiiddgioo-selected {
  -webkit-border-radius: 4px !important;
  -webkit-box-shadow: #7da9ff 0px 0px 5px, #7da9ff 0px 0px 5px inset !important;
  background-color: rgba(125, 169, 255, 0.6) !important;
  z-index: 10000000001 !important;
}

img.ncennffkjdiamlpmcbajkmaiiiddgioo-selected {
  -webkit-box-shadow: #7da9ff 0px 0px 10px !important;
}

/*
 * Selection rectangle
 */

.ncennffkjdiamlpmcbajkmaiiiddgioo-selection-rectangle {
  position: fixed !important;
  border: 1px dashed #7da9ff !important;
  box-sizing: border-box;
  background-color: rgba(125, 169, 255, 0.6) !important;
  border-radius: 4px !important;
  z-index: 10000000002 !important;
  -webkit-transition: opacity 0.2s linear, background-color 0.2s linear, border-color 0.2s linear !important;
  transition: opacity 0.2s linear, background-color 0.2s linear, border-color 0.2s linear !important;
}

.ncennffkjdiamlpmcbajkmaiiiddgioo-selection-rectangle.ncennffkjdiamlpmcbajkmaiiiddgioo-inverted {
  border: 1px dashed #ff4848 !important;
  border-radius: 4px !important;
  background-color: rgba(255, 72, 72, 0.6) !important;
}

.ncennffkjdiamlpmcbajkmaiiiddgioo-selection-rectangle.ncennffkjdiamlpmcbajkmaiiiddgioo-closing {
  opacity: 0 !important;
}

/*
 * Dark glass
 */

#ncennffkjdiamlpmcbajkmaiiiddgioo-glass {
  position: fixed !important;
  left: 0 !important;
  top: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 10000000000 !important;
  cursor: default !important;
  background-color: rgba(0, 0, 0, 0.4) !important;
}

.ncennffkjdiamlpmcbajkmaiiiddgioo-loading #ncennffkjdiamlpmcbajkmaiiiddgioo-glass,
.ncennffkjdiamlpmcbajkmaiiiddgioo-exiting #ncennffkjdiamlpmcbajkmaiiiddgioo-glass {
  cursor: progress !important;
}

/*
 * Usage instructions z-index:2147483647
 */

#ncennffkjdiamlpmcbajkmaiiiddgioo-help {
  width: auto !important;
  font-family: sans-serif !important;
  font-size: 11pt !important;
  font-weight: normal !important;
  line-height: 1.7em !important;
  text-align: left !important;
  border-radius: 5px !important;
  border: 2px solid white !important;
  color: white !important;
  position: fixed !important;
  bottom: 10px !important;
  right: 10px !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  padding: 5px !important;
  z-index: 10000000004 !important;
  opacity: 1 !important;
  -webkit-transition: opacity 0.2s linear !important;
  transition: opacity 0.2s linear !important;
  cursor: default !important;
}

.ncennffkjdiamlpmcbajkmaiiiddgioo-loading #ncennffkjdiamlpmcbajkmaiiiddgioo-help,
.ncennffkjdiamlpmcbajkmaiiiddgioo-exiting #ncennffkjdiamlpmcbajkmaiiiddgioo-help {
  opacity: 0 !important;
}

#ncennffkjdiamlpmcbajkmaiiiddgioo-help strong {
  font-weight: bolder !important;
}

#ncennffkjdiamlpmcbajkmaiiiddgioo-help.ncennffkjdiamlpmcbajkmaiiiddgioo-invisible {
  opacity: 0 !important;
}

/* misc stuff */

.ncennffkjdiamlpmcbajkmaiiiddgioo-relative {
  position: relative !important;
}


/* scrollbar */
/* BEM */
.xly-dialog-prompt {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 400px;
  padding: 0 18px 18px;
  background: #fff;
  border-radius: 2px;
  box-sizing: border-box;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  transform: translate3d(-50%, -50%, 0);
  z-index: 9999 !important;
  user-select: none;
  font-family: "microsoft yahei", -apple-system, "PingFang SC", "simsun", Arial,
 sans-serif !important;
  font-size: 12px !important; }
  .xly-dialog-prompt .td-button {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30px;
    background: #3f85ff;
    border: 0;
    border-radius: 4px;
    color: #fff;
    font-size: 12px !important;
    outline: 0;
    cursor: pointer;
    transition: background 0.2s; }
    .xly-dialog-prompt .td-button:hover {
      background: #619bff; }
  .xly-dialog-prompt a {
    text-decoration: none;
    font-family: "microsoft yahei", -apple-system, "PingFang SC", "simsun",
 Arial, sans-serif;
    font-size: 12px;
    color: #4d4d4d; }
  .xly-dialog-prompt button {
    cursor: pointer; }
  .xly-dialog-prompt button:focus {
    outline: 0; }
  .xly-dialog-prompt h2 {
    padding: 18px 0 24px;
    margin: 0;
    height: unset;
    font-weight: normal;
    text-align: left;
    font-family: "microsoft yahei", -apple-system, "PingFang SC", "simsun",
 Arial, sans-serif !important;
    font-size: 15px !important;
    color: #4d4d4d !important;
    background: unset !important;
    border: unset !important; }
  .xly-dialog-prompt .td-dialog__footer {
    margin-top: 24px; }
  .xly-dialog-prompt .td-checkbox {
    margin-top: 6px;
    color: var(--color-secondary); }
  .xly-dialog-prompt__text {
    font-size: 13px !important;
    color: #4d4d4d !important;
    text-align: left !important;
    line-height: 20px !important;
    margin: 0 !important; }
  .xly-dialog-prompt__tips {
    margin-top: 6px;
    color: var(--color-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all; }
  .xly-dialog-prompt__footer-link {
    text-decoration: underline !important;
    color: #4d4d4d !important; }
    .xly-dialog-prompt__footer-link:hover {
      color: #3f85ff !important; }
  .xly-dialog-prompt__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24px;
    width: 100%; }
  .xly-dialog-prompt__button {
    display: flex; }
    .xly-dialog-prompt__button .td-button {
      width: 130px;
      margin-left: 12px; }
      .xly-dialog-prompt__button .td-button--other {
        border: solid 1px #ccc;
        color: #4d4d4d;
        background: #fff; }
        .xly-dialog-prompt__button .td-button--other:hover {
          color: #3f85ff; }
  .xly-dialog-prompt .xly-dialog__button .td-button {
    padding: 0 23px;
    width: auto !important; }
  .xly-dialog-prompt .xly-dialog__footer-link--underline {
    color: var(--color-auxiliary);
    text-decoration: underline; }
  .xly-dialog-prompt .xly-dialog-close {
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center; }
    .xly-dialog-prompt .xly-dialog-close:hover {
      opacity: 0.5; }
  .xly-dialog-prompt .icon-close {
    width: 16px;
    height: 16px;
    background: url(data:image/png;base64,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) no-repeat;
    background-size: 100% auto; }
