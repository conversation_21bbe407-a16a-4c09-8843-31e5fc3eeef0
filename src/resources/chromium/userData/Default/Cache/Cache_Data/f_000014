define("@baidu/aging-tools-pc/dist/index",["san","tslib"],function(n,t){function e(n){if(o[n])return o[n].exports;var t=o[n]={i:n,l:!1,exports:{}};return i[n].call(t.exports,t,t.exports,e),t.l=!0,t.exports}return i=[function(t){t.exports=n},function(n){n.exports=t},function(n,t,e){t=function(){"use strict";function t(n){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n
})(n)}function i(n){if(null==n)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(n),e=1;e<arguments.length;e++){var i=arguments[e];if(null!=i)for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(t[o]=i[o])}return t}var o=e(0).defineComponent;n.exports=function(n,e,a){for(var s=function(n){var t=[n];return"function"==typeof n&&(t.push(n.prototype),n.prototype.constructor&&t.push(n.prototype.constructor.prototype)),t}(n),r=0;r<s.length;r++)e&&("string"==typeof e?s[r].template=e:e instanceof Array?s[r].aPack=e:s[r].aNode=e),a.length&&function(n,t){for(var e={},o=0;o<t.length;o++)i(e,t[o]);
var a=n.initData;n.initData=a?function(){return i({},a.call(this),{$style:e})}:function(){return e}}(s[r],a);return"object"===t(n)?o(n):n}}.apply(t,[]),void 0===t||(n.exports=t)},function(n,t){t=function(){"use strict";n.exports=function(n){var t=[];return t.toString=function(){return this.map(function(t){var e=function(n,t){var e=n[1]||"",i=n[3];return i?t&&"function"==typeof btoa?(n=function(n){return n=btoa(unescape(encodeURIComponent(JSON.stringify(n)))),n="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(n),"/*# ".concat(n," */")
}(i),t=i.sources.map(function(n){return"/*# sourceURL=".concat(i.sourceRoot||"").concat(n," */")}),[e].concat(t).concat([n]).join("\n")):[e].join("\n"):e}(t,n);return t[2]?"@media ".concat(t[2]," {").concat(e,"}"):e}).join("")},t.i=function(n,e,i){"string"==typeof n&&(n=[[null,n,""]]);var o={};if(i)for(var a=0;a<this.length;a++){var s=this[a][0];null!=s&&(o[s]=!0)}for(var r=0;r<n.length;r++){var l=[].concat(n[r]);i&&o[l[0]]||(e&&(l[2]=l[2]?"".concat(e," and ").concat(l[2]):e),t.push(l))}},t}}.apply(t,[]),void 0===t||(n.exports=t)
},function(n,t,e){"use strict";function i(n,t){for(var e=[],i={},o=0;o<t.length;o++){var a=t[o],s=a[0],a={id:n+":"+o,css:a[1],media:a[2],sourceMap:a[3]};i[s]?i[s].parts.push(a):e.push(i[s]={id:s,parts:[a]})}return e}function o(){}function a(n,t,e,o){f=!!o.runAsProduction||e,y=o||{};var a=i(n,t);return s(a),function(t){for(var e=[],o=0;o<a.length;o++){var r=a[o];(l=d[r.id]).refs--,e.push(l)}t?s(a=i(n,t)):a=[];for(var l,o=0;o<e.length;o++)if(0===(l=e[o]).refs){for(var u=0;u<l.parts.length;u++)l.parts[u]();
delete d[l.id]}}}function s(n){for(var t=0;t<n.length;t++){var e=n[t],i=d[e.id];if(i){i.refs++;for(var o=0;o<i.parts.length;o++)i.parts[o](e.parts[o]);for(;o<e.parts.length;o++)i.parts.push(l(e.parts[o]));i.parts.length>e.parts.length&&(i.parts.length=e.parts.length)}else{for(var a=[],o=0;o<e.parts.length;o++)a.push(l(e.parts[o]));d[e.id]={id:e.id,refs:1,parts:a}}}}function r(){var n,t=document.createElement("style"),e=y.attributes||{};for(n in t.type="text/css",e)e.hasOwnProperty(n)&&t.setAttribute(n,e[n]);
return c.appendChild(t),t}function l(n){var t,e,i,a=document.querySelector("style["+m+'~="'+n.id+'"]');if(a){if(f)return o;a.parentNode.removeChild(a)}return i=x?(t=p++,a=g=g||r(),e=u.bind(null,a,t,!1),u.bind(null,a,t,!0)):(a=r(),e=function(n,t){var e=t.css,i=t.media,o=t.sourceMap;if(i&&n.setAttribute("media",i),y.ssrId&&n.setAttribute(m,t.id),o&&(e+="\n/*# sourceURL="+o.sources[0]+" */",e+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),n.styleSheet)n.styleSheet.cssText=e;
else{for(;n.firstChild;)n.removeChild(n.firstChild);n.appendChild(document.createTextNode(e))}}.bind(null,a),function(){a.parentNode.removeChild(a)}),e(n),function(t){t?t.css===n.css&&t.media===n.media&&t.sourceMap===n.sourceMap||e(n=t):i()}}function u(n,t,e,i){e=e?"":i.css,n.styleSheet?n.styleSheet.cssText=b(t,e):(i=document.createTextNode(e),(e=n.childNodes)[t]&&n.removeChild(e[t]),e.length?n.insertBefore(i,e[t]):n.appendChild(i))}if(e.r(t),e.d(t,"default",function(){return a}),t="undefined"!=typeof document,"undefined"!=typeof DEBUG&&DEBUG&&!t)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");
var h,d={},c=t&&(document.head||document.getElementsByTagName("head")[0]),g=null,p=0,f=!1,y=null,m="data-vue-ssr-id",x="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase()),b=(h=[],function(n,t){return h[n]=t,h.filter(Boolean).join("\n")})},function(n,t,e){var i=e(2),o=[e(28)],a=e(30),s=e(10).default;n.exports=e(10),n.exports.default=i(s,a,o)},function(n,t){"use strict";t.__esModule=!0,t.default=function(n,t,e){"reset"===t?n[1]=["","","","","",""]:(t=n[0].indexOf(t),n[1][t]=e);
try{localStorage.setItem("agingTools",n[1].join("_"))}catch(n){}return n}},function(n,t){"use strict";t.__esModule=!0;var e={};t.default={on:function(n,t){e[n]||(e[n]=[]),e[n].push(t)},fire:function(n,t){e[n]&&e[n].forEach(function(n){n&&n(t)})},off:function(n,t){!e[n]||0<=(t=function(n,t){for(var e=0;e<n.length&&n[e].toString()!==t.toString();)e++;return e>=n.length?-1:e}(e[n],t))&&e[n].splice(t,1)}}},function(n,t,e){"use strict";function i(){var n=null!==o&&o.apply(this,arguments)||this;return n.trimWhitespace="all",n
}t.__esModule=!0;var o,a=e(1),s=e(0),r=a.__importDefault(e(24)),l=a.__importDefault(e(7)),u=e(52),h=a.__importDefault(e(53)),d=e(15),h=(o=s.Component,a.__extends(i,o),i.prototype.initData=function(){return{show:!1,rootDom:"",entry:null,quickType:"",statusList:[["color","scale","cursor","cross","voice","bigText"],["","","","","",""]],totalWidth:"100%",from:"result",behavior:[],scaleDom:[],newType:"1",showArea:!1,timer:null,readText:"",audioSrc:"",mode:"point",bigTextDom:null,disableVoice:"",logService:null,event:null,toastTimer:null,closeFn:null,initReader:null,focusReader:null}
},i.prototype.attached=function(){var n=this,t=this,e=t.data.get("entry");e&&e.length&&e.off().on("click",function(){t.toggleTools("quit"),t.logSend("点击工具条入口")}),t.init();var i=this.getKeyType.bind(this),e=this.handleScale.bind(this);document.addEventListener("keydown",i),document.addEventListener("keyup",i),window.addEventListener("resize",e),i=this.data.get("event"),e=function(){(n.data.get("show")||n.data.get("showArea"))&&(n.showToast(),n.hideTools(),n.clearCompletely("quit"))},this.data.set("closeFn",e),i&&i.on("aging-tools.close",e),this.addPageDescrib()
},i.prototype.setTimeout=function(n,t,e){clearTimeout(this.data.get(n)),this.data.set(n,setTimeout(t,e))},i.prototype.clearTimeout=function(){clearTimeout(this.data.get("initReader")),clearTimeout(this.data.get("focusReader"))},i.prototype.showToast=function(){var n=$("body"),t=this.data.get("toastTimer");0===$(".aging-toast").length&&n.append('<div class="aging-toast">已为您关闭无障碍导读</div>'),clearTimeout(t),setTimeout(function(){$(".aging-toast").remove()},2e3)},i.prototype.logSend=function(n){var t=this.data.get("logService");
t&&"function"==typeof t&&t(n)},i.prototype.handleScale=function(){var n=$(this.data.get("rootDom"));this.data.set("totalWidth",n[0].getBoundingClientRect().width)},i.prototype.init=function(){var n="";try{n=localStorage.getItem("agingTools")}catch(n){}null!==n?(this.showTools(),n&&(n=n.split("_"),this.data.set("statusList[1]",n))):this.hideTools()},i.prototype.getKeyType=function(n){var t,e=this.data.get("behavior");"keydown"===n.type?(e.push(n.keyCode),"changeFocus"===(t=u.changeFocus(n.keyCode))&&(d.interveneFocus(n),this.readFocusElement())):"keyup"===n.type&&this.data.set("behavior",[]),e[0]&&"keyup"===n.type&&("start"===(t=u.press(e))?this.toggleTools():"quit"===t?this.toggleTools("quit"):"enter"===t?((e=document.activeElement)&&e.click(),this.readFocusElement()):"readScreen"===t?this.handleReadScreen():t&&(this.data.set("quickType",t),l.default.fire("type",null)))
},i.prototype.readFocusElement=function(){this.setTimeout("focusReader",function(){l.default.fire("read",{type:"single",target:document.activeElement})},300)},i.prototype.handleKeup=function(){this.data.set("quickType","")},i.prototype.toggleTools=function(n){this.data.get("show")?(this.hideTools(),this.clearCompletely(n)):this.showTools()},i.prototype.clearCompletely=function(n){if(n&&"quit"===n){n=this.data.get("scaleDom"),n.forEach(function(n){$(n).removeClass("aging-scale").css({transform:"","transform-origin":""})
}),$("#aging-cursor").remove();try{localStorage.removeItem("agingTools")}catch(n){}}},i.prototype.showTools=function(){$("body").addClass("aging-tools-gap"),this.data.set("show",!0);var n=this.data.get("event");n&&n.fire("tts.close"),this.setTimeout("initReader",function(){return l.default.fire("read",{type:"single",target:"#aging-total-page"})},500),this.logSend("工具条展现");try{localStorage.getItem("agingTools")||localStorage.setItem("agingTools","")}catch(n){}},i.prototype.hideTools=function(){var n=this.data.get("from"),t=this.data.get("bigTextDom");
$(this.data.get("rootDom")).removeClass("darkmode dark blue"),$("body").removeClass("aging-tools-gap darkmode dark blue aging-scale"),this.data.set("show",!1),this.data.set("showArea",!1),t&&t.css("padding-bottom",""),"index"===n&&($("#bottom_layer").css("bottom",""),$("#s_side_wrapper").css("bottom",""))},i.prototype.handleReadScreen=function(){var n=this.data.get("showArea"),t=this.data.get("bigTextDom");n&&($("#bottom_layer").css("bottom",""),t&&t.css("padding-bottom","")),this.data.set("showArea",!n)
},i.prototype.addPageDescrib=function(){var n;document.querySelector("#aging-total-page")||((n=document.createElement("a")).setAttribute("id","aging-total-page"),n.setAttribute("role","pagedescription"),n.setAttribute("aria-label","欢迎进入 百度一下，你就知道，盲人用户进入读屏幕模式请按快捷键Ctrl加Alt加R；阅读详细操作说明请按快捷键Ctrl加Alt加问号键。"),n.setAttribute("tabindex","0"),n.setAttribute("href","javascript:void(0)"),document.body.insertBefore(n,document.body.firstChild))},i.prototype.detached=function(){this.hideTools(),this.clearTimeout();
var n=this.data.get("closeFn"),t=this.data.get("event");n&&t&&t.off("aging-tools.close",n)},i.components={"init-list":r.default,"read-screen":h.default},i);t.default=h},function(n,t,e){"use strict";function i(){var n=null!==o&&o.apply(this,arguments)||this;return n.trimWhitespace="all",n}t.__esModule=!0;var o,a=e(1),s=e(0),r=a.__importDefault(e(5)),l=a.__importDefault(e(31)),u=a.__importDefault(e(35)),h=a.__importDefault(e(6)),d=a.__importDefault(e(13)),e=a.__importDefault(e(43)),e=(o=s.Component,a.__extends(i,o),i.prototype.initData=function(){return{colorProps:{type:""},rootDom:"",scale:1,cursor:"",showCross:!1,quickType:"",explainUrl:"https://www.baidu.com/search/aging-tools.html",crossEvent:"",statusList:[],cursorChecked:!1,enlargeChecked:!1,narrowChecked:!1,scaleDom:[],readText:"",from:"",showStatus:!0}
},i.prototype.attached=function(){var n=this;n.initStatus(),n.bindShortcutKey(),n.watch("quickType",function(t){n.bindShortcutKey(t),n.fireQuickKeyUp()});var t=this.data.get("from");t&&"result"===t&&$("html").addClass("aging-tools-result")},i.prototype.detached=function(){$("html").removeClass("aging-tools-result")},i.prototype.fireQuickKeyUp=function(){this.fire("quickKeyUp",{})},i.prototype.logSend=function(n){this.fire("logSend",n)},i.prototype.handleReading=function(n){this.data.set("readText",n.val)
},i.prototype.initStatus=function(){var n=this.data.get("statusList")[1];n[0]&&this.setColor(n[0]),n[1]&&1!=+n[1]&&(this.data.set("enlargeChecked",!0),this.setScale(+n[1])),n[2]&&"false"!==n[2]&&(this.data.set("cursor",!1),this.handleCursor()),n[3]&&"false"!==n[3]&&this.data.set("crossEvent","click")},i.prototype.initCrossEvent=function(){this.data.set("crossEvent","")},i.prototype.initBigEvent=function(){this.data.set("bigTextEvent","")},i.prototype.bindShortcutKey=function(n){n=n||this.data.get("quickType"),"cursor"===n?this.handleCursor():"color"===n?this.handleColor():"enlarge"===n||"narrow"===n?this.handleScale("enlarge"===n?n:""):"explain"===n?window.location.href=this.data.get("explainUrl"):"reset"===n?this.handleReset():"cross"===n&&this.data.set("crossEvent","click")
},i.prototype.handleReset=function(n){this.handleColor("reset"),this.handleScale("reset"),this.handleCursor("reset"),this.data.set("crossEvent","reset"),this.data.set("bigTextEvent","reset");var t=h.default(this.data.get("statusList"),"reset","");this.data.set("statusList",t);var e=this.ref("voice"),t=this.ref("text");e&&e.resetAll&&e.resetAll(),t&&t.resetAll&&t.resetAll(),n||this.logSend("重置")},i.prototype.handleColor=function(n){var t=this.data.get("colorProps.type"),e="";"reset"!==n&&(t?"dark"===t&&(e="blue"):e="dark",this.logSend("配色_"+e)),t=h.default(this.data.get("statusList"),"color",e),this.data.set("statusList",t),this.setColor(e)
},i.prototype.setColor=function(n){var t=this.data.get("rootDom"),e=$("body");this.data.set("colorProps.type",n),t&&((t=$(t)).removeClass("darkmode dark blue"),e.removeClass("darkmode dark blue"),n&&(t.addClass("darkmode "+n),e.addClass("darkmode "+n)))},i.prototype.handleScale=function(n){var t=this.data.get("scale"),e=[1,1.15,1.3],t=e.indexOf(t);"reset"===n?t=0:n?(2>t&&(t+=1),this.data.set("enlargeChecked",!0),this.logSend("放大")):(t>0&&--t,this.logSend("缩小")),n=e[t],e=h.default(this.data.get("statusList"),"scale",n),this.data.set("statusList",e),t||this.data.set("enlargeChecked",!1),this.setScale(n)
},i.prototype.setScale=function(n){var t,e=this,i=this.data.get("scaleDom");this.data.set("scale",n),0<i.length&&(t="",i.forEach(function(i){t=$(i),1===n?t.css({transform:"","transform-origin":""}).removeClass("aging-scale"):t.css({transform:"scale("+n+")","transform-origin":"0px 0px"}).addClass("aging-scale"),e.fire("scale",{index:n})}))},i.prototype.handleCursor=function(n){var t=this.data.get("cursor"),e=!1;t||"reset"===n?$("#aging-cursor").remove():t||($("body").append('<style id="aging-cursor">* {cursor: url("https://dss0.bdstatic.com/5aV1bjqh_Q23odCf/static/superman/img/w_cur-d41911290d.cur"),auto !important}</style>'),e=!0),this.data.set("cursorChecked",e),this.data.set("cursor",e),t=h.default(this.data.get("statusList"),"cursor",""+e),this.data.set("statusList",t),"reset"!==n&&this.logSend(e?"鼠标样式_添加":"鼠标样式_移除")
},i.prototype.handleQuit=function(){this.handleReset(!0),this.logSend("退出服务"),this.fire("quit",{})},i.prototype.handleChange=function(n){var t=h.default(this.data.get("statusList"),"cross",""+n.show);this.data.set("statusList",t),this.logSend(n.show?"十字线_开":"十字线_关")},i.prototype.handleAreaClick=function(){this.fire("readScreen",{}),this.logSend("读屏幕")},i.computed={colorText:function(){var n=this.data.get("colorProps");return"dark"===n.type?"黑黄":"blue"===n.type?"蓝黄":"默认"},scaleText:function(){var n=this.data.get("scale");
return 1===n?"默认":1.15===n?"1.15倍":1.3===n?"1.3倍":void 0}},i.components={"a-item":r.default,"a-cross":l.default,"a-more":u.default,"a-voice-item":d.default,"a-big-text":e.default},i);t.default=e},function(n,t,e){"use strict";function i(){var n=null!==o&&o.apply(this,arguments)||this;return n.trimWhitespace="all",n}t.__esModule=!0;var o,a=e(1),e=e(0),a=(o=e.Component,a.__extends(i,o),i.prototype.initData=function(){return{wide:!1,text:"",type:"reset",colorProps:{type:""},nogap:!1,checked:!1,key:"",tabIndex:0}
},i.prototype.itemClk=function(n){this.fire("click",n)},i.components={},i);t.default=a},function(n,t,e){"use strict";function i(){var n=null!==o&&o.apply(this,arguments)||this;return n.trimWhitespace="all",n}t.__esModule=!0;var o,a=e(1),s=e(0),e=a.__importDefault(e(5)),e=(o=s.Component,a.__extends(i,o),i.prototype.initData=function(){return{horizontalStyle:"",verticalStyle:"",showCross:!1,eventStatus:"",crossChecked:!1,newType:!1}},i.prototype.attached=function(){var n=this,t=this;this.watch("eventStatus",function(e){"reset"===e&&(t.data.set("showCross",!1),n.data.set("crossChecked",!1),t.detachFunc(),n.fire("reset",{})),"click"===e&&n.el&&n.el.children&&n.el.children[0]&&(n.el.children[0].click(),n.fire("click",{}))
})},i.prototype.getCrossPos=function(n){var t=this.ref("horizontal"),e=this.ref("vertical"),i=t?t.offsetWidth:576,t=e?e.offsetHeight:576,e=n.clientX-20,n=n.clientY-20,t=n-t/2;this.data.set("horizontalStyle","left: "+(e-i/2)+"px;top: "+n+"px;"),this.data.set("verticalStyle","left: "+e+"px;top: "+t+"px;")},i.prototype.detached=function(){this.detachFunc()},i.prototype.handleCross=function(n){var t=this.data.get("showCross");t?this.detachFunc():(this.getCrossPos(n),n=this.getCrossPos.bind(this),this.data.set("posFunc",n),document.addEventListener("mousemove",n)),this.data.set("crossChecked",!t),this.data.set("showCross",!t),this.fire("change",{show:!t})
},i.prototype.detachFunc=function(){var n=this.data.get("posFunc");document.removeEventListener("mousemove",n)},i.components={"a-item":e.default},i);t.default=e},function(n,t,e){"use strict";function i(){var n=null!==o&&o.apply(this,arguments)||this;return n.trimWhitespace="all",n}t.__esModule=!0;var o,a=e(1),s=e(0),e=a.__importDefault(e(5)),e=(o=s.Component,a.__extends(i,o),i.prototype.initData=function(){return{newType:!1,inTime:0,timer:null}},i.prototype.enter=function(){var n=this;this.data.set("inTime",(new Date).getTime());
var t=setTimeout(function(){n.fire("logSend","more_service"),clearTimeout(n.data.get("timer")),n.data.set("timer",null)},1e3);this.data.set("timer",t)},i.prototype.leave=function(){var n=this.data.get("inTime");(new Date).getTime()-n<1e3&&(clearTimeout(this.data.get("timer")),this.data.set("timer",null))},i.components={"a-item":e.default},i);t.default=e},function(n,t,e){var i=e(2),o=[e(39)],a=e(41),s=e(14).default;n.exports=e(14),n.exports.default=i(s,a,o)},function(n,t,e){"use strict";function i(){var n=null!==o&&o.apply(this,arguments)||this;
return n.trimWhitespace="all",n}t.__esModule=!0;var o,a=e(1),s=e(0),r=a.__importDefault(e(5)),l=a.__importDefault(e(42)),u=a.__importDefault(e(6)),h=a.__importDefault(e(7)),d=e(15),r=(o=s.Component,a.__extends(i,o),i.prototype.initData=function(){return{useVoice:!0,type:{onoff:"voiceOn",speed:"speed",mode:"pointread"},onlyOnoff:!1,voiceApi:"https://www.baidu.com/pctts/report/report_audio",readText:"",myAudio:null,isReadScreen:!1,screenText:"",isReading:!1,readTarget:null,statusList:[],toastTimer:null,disableVoice:!1,quickType:"",lastReadTimeStamp:null,lastReadText:""}
},i.prototype.attached=function(){var n=this,t="",e=this.data.get("disableVoice");try{var i=localStorage.getItem("agingTools");i&&(t=i.split("_")[4])}catch(e){}i=this.data.get("isReadScreen"),e||this.createAudio(),this.bindHandlerFun(),this.bindShortcutKey(),this.watch("quickType",function(t){n.bindShortcutKey(t),n.fire("quickKeyUp",{})}),this.reading(),i&&this.watch("screenText",function(t){t&&n.getReading(t)}),"off"===t&&this.data.set("type.onoff","voiceOff"),this.watch("showStatus",function(e){if(e)try{var i,o=localStorage.getItem("agingTools");
o&&(i=o.split("_"),"off"===(t=i[4])?n.data.set("type.onoff","voiceOff"):n.data.set("type.onoff","voiceOn"))}catch(e){}}),h.default.on("read",this.outterRead.bind(this))},i.prototype.outterRead=function(n){"single"===n.type&&n.target?this.handlePoint(n):"multi"===n.type&&n.target&&this.handleSeriesread(n)},i.prototype.bindShortcutKey=function(n){n=n||this.data.get("quickType"),"voiceSwitch"===n?this.voiceOnOff():"voiceSpeed"===n?this.handleSpeed():"readMode"===n&&this.handleReadmode()},i.prototype.logSend=function(n){this.fire("logSend",n)
},i.prototype.createAudio=function(){var n=this,t=null;document.querySelector(".aging-audio")?t=document.querySelector(".aging-audio"):((t=new Audio).className="aging-audio",document.body.appendChild(t)),this.data.set("myAudio",t),$(document.body).on("click.audio",function(){t.src="",n.data.set("myAudio",t)})},i.prototype.detached=function(){this.clearHandler();var n=document.querySelector(".aging-audio");n&&n.parentNode.removeChild(n),h.default.off("read",this.outterRead.bind(this)),$(document.body).off("click.audio")
},i.prototype.resetAll=function(){this.setNewType("onoff","voiceOn"),u.default(this.data.get("statusList"),"voice",""),this.setNewType("speed","speed"),this.setNewType("mode","pointread"),this.reading()},i.prototype.clearHandler=function(){var n=this.data.get("pointFunc"),t=this.data.get("seriesFunc");document.removeEventListener("mousemove",n,!1),document.removeEventListener("mousemove",t,!1)},i.prototype.bindHandlerFun=function(){this.data.set("pointFunc",this.handlePoint.bind(this)),this.data.set("seriesFunc",this.handleSeriesread.bind(this))
},i.prototype.reading=function(){this.clearHandler();var n=this.data.get("type.mode"),t=this.data.get("pointFunc"),e=this.data.get("seriesFunc");"pointread"===n?document.addEventListener("mousemove",t,!1):document.addEventListener("mousemove",e,!1)},i.prototype.handlePoint=function(n){var t=this,e=t.data.get("timer"),i=t.data.get("from"),o=this.data.get("disableVoice");clearTimeout(e),e=setTimeout(function(){var e=$(n.target),e=l.default(e,i);e.val&&(t.fire("getReadingText",{val:e.val}),o||t.getReading(e.val))
},50),this.data.set("timer",e)},i.prototype.handleSeriesread=function(n){var t=this,e=$(n.target),i=t.data.get("from"),o=t.data.get("timer"),a=this.data.get("isReading");clearTimeout(o),o=setTimeout(function(){var o=$(n.target),a=l.default(e,i),s=l.default(o,i);e.attr("class")!==o.attr("class")&&a.val!==s.val||s.val&&(t.fire("getReadingText",{val:s.val}),t.data.set("readTarget",s.target),t.getReading(s.val))},a?3e3:50),this.data.set("timer",o)},i.prototype.showToast=function(){var n=$("body"),t=this.data.get("toastTimer");
0===$(".aging-toast").length&&n.append('<div class="aging-toast">读取失败，请重新放置鼠标</div>'),clearTimeout(t),setTimeout(function(){$(".aging-toast").remove()},1e3)},i.prototype.seriesRead=function(n){for(var t=null,e=this.data.get("from"),i=d.getNextReadNode(n),t=l.default(i,e);!t.target&&!t.val&&i[0];)i=d.getNextReadNode(i),t=l.default(i,e);i[0]&&(this.data.set("readTarget",i),t.val?(this.fire("getReadingText",{val:t.val}),this.getReading(t.val)):(i.hasClass("dustbin")&&(i=i.parents(".s-news-item").next()),this.fireReading(i)))
},i.prototype.fireReading=function(n){var t=this.data.get("from"),t=l.default(n,t);t.val&&(this.fire("getReadingText",{val:t.val}),this.data.set("readTarget",n),this.getReading(t.val))},i.prototype.getReading=function(n){var t=this,e=this,i=e.data.get("voiceApi"),o=this.data.get("myAudio"),a=this.data.get("type.onoff"),s=this.data.get("lastReadText"),r=this.data.get("lastReadTimeStamp"),l=(new Date).getTime();"voiceOff"===a||r&&1e3>l-r&&s===n||(this.data.set("lastReadText",n),this.data.set("lastReadTimeStamp",l),$.ajax({type:"POST",dataType:"json",url:i,data:{text:encodeURIComponent(n)},xhrFields:{withCredentials:!0},scriptCharset:"utf-8",success:function(n){var i=(n.data||{}).audio_list;
i&&0!==i.length?(o.src=i.shift(),n=e.data.get("type.speed"),o.playbackRate="speed2"===n?1.25:1,i=e.test.bind(e,[o,i]),$(o).off("ended"),$(o).on("ended",i),t.audioPlay(o),t.data.set("isReading",!0),t.data.set("myAudio",o)):e.showToast()},error:function(){e.showToast()}}))},i.prototype.audioPlay=function(n){n=n.play(),n&&n.catch(function(){})},i.prototype.test=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var e=n[0][0],i=n[0][1],o=this.data.get("type.mode");i&&i.length?(i=i.shift(),e.src=i,this.audioPlay(e)):($(e).off("ended"),this.data.set("isReading",!1),"seriesread"!==o||(o=this.data.get("readTarget"))&&this.seriesRead(o))
},i.prototype.voiceOnOff=function(){var n;this.updateType("onoff","voiceOn","voiceOff"),"voiceOff"===this.data.get("type.onoff")?(u.default(this.data.get("statusList"),"voice","off"),(n=this.data.get("myAudio")).src="",this.data.set("myAudio",n)):u.default(this.data.get("statusList"),"voice",""),this.logSend("voiceOn"===this.data.get("type").onoff?"声音开":"声音关")},i.prototype.judgeVoice=function(){this.data.get("type.onoff")},i.prototype.updateType=function(n,t,e){var i="",o=this.data.get("type."+n);
return this.data.set("type."+n,i=o===t?e:t),i},i.prototype.setNewType=function(n,t){this.data.get("type."+n)!==t&&this.data.set("type."+n,t)},i.prototype.handleSpeed=function(){this.updateType("speed","speed","speed2"),this.logSend("speed2"===this.data.get("type").speed?"语速快":"语速")},i.prototype.handleReadmode=function(){var n="",n="pointread"===this.updateType("mode","pointread","seriesread")?"开启点读模式，请将鼠标移至需要阅读的位置":"开启连读模式，请将鼠标移至需要阅读的位置";this.getReading(n),this.reading(),this.logSend("pointread"===this.data.get("type").mode?"点读模式":"连读模式")
},i.components={"a-item":r.default},i);t.default=r},function(n,t,e){"use strict";function i(n){setTimeout(function(){n&&n.focus()},50)}function o(n){function t(n){n&&(Array.from(n.children).reverse().forEach(function(n){return"none"!==$(n).css("display")&&t(n)}),s(n)&&!e&&(e=n))}var e;return t(n),e}function a(n){function t(n){n&&(s(n)&&!e&&(e=n),Array.from(n.children).forEach(function(n){return"none"!==$(n).css("display")&&t(n)}))}var e;return t(n),e}function s(n){return"a"===n.tagName||"button"===n.tagName||0<=n.tabIndex
}function r(n){var t=[];return n.each(function(n,e){return t.push(e)}),t.sort(function(n,t){return parseInt($(t).attr("tabindex")||0,10)-parseInt($(n).attr("tabindex")||0,10)}),t}function l(n){var t=r(n.parent().children()),e=0;return t.forEach(function(t,i){t===n[0]&&(e=i+1)}),$(t[e])}t.__esModule=!0,t.interveneFocus=t.addFilter=t.getNextReadNode=void 0;var u=e(1),h=[],d=[],c=[];d.push(function(){var n=document.activeElement;/s-tab-item/.test(n.className)&&/s-tab-more/.test(n.className)&&/更多/.test(n.innerText)&&i(document.querySelector("#content_left"))
}),d.push(function(){var n=document.activeElement,t=document.querySelector("#rs_new table tbody");t&&o(t)===n&&i(document.querySelector("#content_right"))}),d.push(function(){var n=document.activeElement,t=document.querySelector("#content_right");t&&o(t)===n&&i(document.querySelector("#page a")||document.querySelector("#help a"))}),c.push(function(){var n=document.activeElement,t=document.querySelector("#page a");t&&t===n&&i(o(document.querySelector("#content_right")))}),c.push(function(){var n=document.activeElement,t=a(document.querySelector("#content_right"));
t&&t===n&&i(o(document.querySelector("#rs_new table tbody")))}),c.push(function(){var n=document.activeElement,t=a(document.querySelector("#content_left"));t&&t===n&&i(document.querySelector("a.s-tab-item.s-tab-more"))}),h.push(function(n){return!(-1<["EM","SCRIPT","STYLE"].indexOf(n.prop("tagName")))}),h.push(function(n){var t="";n.children().each(function(n,e){return t+=e.innerText});var e=n.text().trim()&&n.text().trim()!==n.children().text().trim(),n=n[0]&&n[0].innerText.trim()&&n[0].innerText.trim()!==t;
return e&&n}),t.addFilter=function(n){h=u.__spreadArray(u.__spreadArray([],h),n)},t.getNextReadNode=function g(n){var t,e,i=null,i=n.children()[0]&&"none"!==n.css("display")?(t=r((t=n).children()),$(t[0])):l(n)[0]?l(n):(n=function(n){for(;n.parent()[0]&&!l(n.parent())[0];)n=n.parent();return n}(n),l(n.parent()));return e=i,h.every(function(n){return n(e)})||i.attr("aria-label")||!i[0]?i:g(i)},t.interveneFocus=function(n){(n.shiftKey?c:d).forEach(function(t){return t(n)})}},function(n,t,e){"use strict";
function i(){var n=null!==o&&o.apply(this,arguments)||this;return n.trimWhitespace="all",n}t.__esModule=!0;var o,a=e(1),s=e(0),r=a.__importDefault(e(5)),l=a.__importDefault(e(17)),u=a.__importDefault(e(6)),l=(o=s.Component,a.__extends(i,o),i.prototype.initData=function(){return{showPopup:!1,readText:"",from:"",eventStatus:"",bigTextDom:null,statusList:[],quickType:""}},i.prototype.attached=function(){var n=this,t=this.data.get("from");this.bindShortcutKey(),this.watch("quickType",function(t){n.bindShortcutKey(t),n.fire("quickKeyUp",{})
});try{var e=localStorage.getItem("agingTools"),i=this.data.get("bigTextDom");e&&e.split("_")[5]&&(this.data.set("showPopup",!0),i&&i.css("padding-bottom","160px"),"index"===t&&($("#bottom_layer").css("bottom","160px"),$("#s_side_wrapper").css("bottom","216px")))}catch(t){}},i.prototype.bindShortcutKey=function(n){"bigTextSwitch"===(n||this.data.get("quickType"))&&this.handleBigText()},i.prototype.setBoard=function(n,t){var e=this.data.get("from"),i=this.data.get("statusList");return n?(t&&t.css("padding-bottom","160px"),"index"===e&&($("#bottom_layer").css("bottom","160px"),$("#s_side_wrapper").css("bottom","216px")),u.default(i,"bigText","1")):(t&&t.css("padding-bottom",""),"index"===e&&($("#bottom_layer").css("bottom",""),$("#s_side_wrapper").css("bottom","")),u.default(i,"bigText",""))
},i.prototype.resetAll=function(){var n=this.data.get("bigTextDom"),n=this.setBoard(!1,n);this.data.set("statusList",n),this.data.set("showPopup",!1)},i.prototype.handleBigText=function(){var n=this.data.get("showPopup"),t=this.data.get("bigTextDom"),t=this.setBoard(!n,t);this.data.set("statusList",t),this.data.set("showPopup",!n),this.logSend(n?"大字幕_关":"大字幕_开")},i.prototype.logSend=function(n){this.fire("logSend",n)},i.prototype.handleClose=function(){var n=this.data.get("statusList");this.data.set("showPopup",!1),this.destroyItem(),n=u.default(n,"bigText",""),this.data.set("statusList",n)
},i.prototype.destroyItem=function(){var n=this.data.get("from"),t=this.data.get("bigTextDom");$("#s_side_wrapper").css("bottom",""),t&&t.css("padding-bottom",""),"index"===n&&($("#bottom_layer").css("bottom",""),$("#s_side_wrapper").css("bottom",""))},i.prototype.detached=function(){this.destroyItem()},i.components={"a-item":r.default,"a-bottom-popup":l.default},i);t.default=l},function(n,t,e){var i=e(2),o=[e(47)],a=e(49),s=e(18).default;n.exports=e(18),n.exports.default=i(s,a,o)},function(n,t,e){"use strict";
function i(){var n=null!==o&&o.apply(this,arguments)||this;return n.trimWhitespace="all",n}t.__esModule=!0;var o,a=e(1),s=e(0),r=e(50),l=e(51),a=(o=s.Component,a.__extends(i,o),i.prototype.initData=function(){return{pinyinChecked:!1,showType:"",readText:"测试: 网址之家是好用、简单的中文上网导航,汇集优秀的网站及资源。提供搜索、天气预报、实用工具等服务,及时收录影视、音乐、小说、游戏、购物等分类的网址和内容,让您的网络生活更简单精彩。测试: 网址之家是好用、简单的中文上网导航,汇集优秀的网站及资源。提供搜索、天气预报、实用工具等服务,及时收录影视、音乐、小说、游戏、购物等分类的网址和内容,让您的网络生活更简单精彩。测试: 网址之家是好用、简单的中文上网导航,汇集优秀的网站及资源。提供搜索、天气预报、实用工具等服务,及时收录影视、音乐、小说、游戏、购物等分类的网址和内容,让您的网络生活更简单精彩。",textPinyinStr:"",displayFantiText:"",barTop:0,fantiType:"繁体",showBar:!1}
},i.prototype.attached=function(){var n=this,t=this,e=this.ref("scrollArea"),i=this.ref("scrollInner");this.data.set("displayFantiText",this.data.get("readText")),this.nextTick(function(){120<$(i).height()&&n.data.set("showBar",!0)}),e.addEventListener("scroll",function(n){n.stopPropagation(),n.preventDefault(),n=$(e).scrollTop()/($(i).height()-124),n=Math.floor(80*n),t.data.set("barTop",n)}),this.watch("readText",function(t){var e=n.data.get("showType");"pinyin"===e?n.usePinyin():"fanti"===e?n.useFanti("fanti"):n.data.set("displayFantiText",t),n.nextTick(function(){120<$(i).height()?n.data.set("showBar",!0):n.data.set("showBar",!1)
})})},i.prototype.logSend=function(n){this.fire("logSend",n)},i.prototype.usePinyin=function(){this.data.set("displayFantiText","");for(var n=this.data.get("readText"),t=[],e=0;e<n.length;e++){var i=n.charAt(e),o=r.pinyin[i]||i;t.push({text:i,"char":o})}this.data.set("displayPinyinText",t),this.data.set("showType","pinyin"),this.logSend("使用拼音")},i.prototype.useFanti=function(n){var t=this.data.get("readText"),e=this.data.get("fantiType");if(this.logSend("使用"+(e="fanti"===n?"繁体":e)),"繁体"===e){for(var e="简体",i="",o=0;o<t.length;o++){var a=t.charAt(o);
i+=l.fanti[a]||a}this.data.set("displayFantiText",i),this.data.set("showType","fanti")}else e="繁体",this.data.set("showType",""),this.data.set("displayFantiText",t);this.data.set("fantiType",e)},i.prototype.closePopup=function(){this.logSend("关闭字幕弹窗"),this.fire("close",{})},i);t.default=a},function(n,t,e){"use strict";function i(){var n=null!==o&&o.apply(this,arguments)||this;return n.trimWhitespace="all",n}t.__esModule=!0;var o,a=e(1),s=e(0),r=a.__importDefault(e(5)),l=a.__importDefault(e(13)),u=a.__importDefault(e(17)),h=a.__importDefault(e(7)),u=(o=s.Component,a.__extends(i,o),i.prototype.initData=function(){return{areaQuick:["Ctrl+1","Ctrl+2","Ctrl+3","Ctrl+4"],explainUrl:"https://www.baidu.com/search/aging-tools.html",useNav:!1,curView:0,curView2:0,curView1:0,curView3:0,from:"",screenText:"",num:[0,0,0,0],showPopup:!0,statusList:[],bigTextDom:null}
},i.prototype.attached=function(){var n=[],t="本页面有";"index"===this.data.get("from")?t+=(n=[3,2,1,1])[0]+"个导航区，":n=[0,4,3,2],this.data.set("num",n);var e=0;n.forEach(function(n){e+=n}),t+=n[1]+"个视窗区，"+n[2]+"个交互区，"+n[3]+"个服务区，共计"+e+"个区域组成，操作帮助请按Ctrl 加 Alt 加 问号键",this.data.set("screenText",t),this.data.set("readText",t),this.handleBottomPop(),h.default.on("exitRead",this.handleAreaClick.bind(this))},i.prototype.detached=function(){h.default.off("exitRead",this.handleAreaClick.bind(this))},i.prototype.handleQuit=function(){this.logSend("退出服务"),this.fire("quit",{})
},i.prototype.logSend=function(n){this.fire("logSend",n)},i.prototype.handleNav=function(n){var t,e=$(window),i=["#s-top-left",".s-menu-item[data-id=1]",".s-code-blocks.s-block-nav"],o=this.data.get("curView3"),a="您已进入导航区，本区域有3个网站导航区，共计3个区域组成，操作帮助按下tab键浏览信息",s=0;(s=o<i.length-1?o+1:0)&&$([".s-menu-item[data-id=2]",".s-menu-item[data-id=1]",".s-menu-item[data-id=100]"][s]).trigger("click"),t=$(i[s]).offset().top,e.scrollTop(t-=70),this.data.set("curView",s),this.data.set("screenText",a),this.data.set("readText",a),this.logSend("导航区"+n)
},i.prototype.handleView=function(n){var t,e=this.data.get("from"),i=$(window),o=[],a=0,s=this.data.get("curView"),r="您已进入视窗区，本区域有";"index"===e?(o=["#s-hotsearch-wrapper",".s-menu-item[data-id=2]"],r+="1个推荐视窗区，共计1个区域组成，操作帮助按下tab键浏览信息"):(o=["#content_left","#con-ar",".result-op[tpl=right_toplist1]","#rs"],r+="1个结果视窗区，3个推荐视窗区，共计4个区域组成，操作帮助按下tab键浏览信息"),a=s<o.length-1?s+1:0,$(o[a]).length&&(t=$(o[a]).offset().top,i.scrollTop(t-=70),this.data.set("curView",a)),this.data.set("screenText",r),this.data.set("readText",r),this.logSend("视窗区"+n)
},i.prototype.handleOperation=function(n){var t=$(window),e=[],i="您已进入交互区，本区域有";"index"===this.data.get("from")?(e=["#head"],i+="1个搜索交互区，共计1个区域组成，操作帮助按下tab键浏览信息"):(e=["#head","#s_tab","#page"],i+="2个搜索交互区，1个换页交互区，共计3个区域组成，操作帮助按下tab键浏览信息");var o=this.data.get("curView1"),a=0,a=o<e.length-1?o+1:0,s=$(e[a]).offset().top;t.scrollTop(s-=70),this.data.set("curView1",a),this.data.set("screenText",i),this.data.set("readText",i),this.logSend("交互区"+n)},i.prototype.handleService=function(n){var t=this.data.get("from"),e=$(window),i="您已进入服务区，本区域有",o=[];
"index"===t?(o=["#u1"],i+="1个版本服务区，共计1个区域组成，操作帮助按下tab键浏览信息"):(o=["#u","#foot"],i+="1个版本服务区，1个帮助服务区，共计2个区域组成，操作帮助按下tab键浏览信息");var a=this.data.get("curView2"),t=0,t=a<o.length-1?a+1:0,s=$(o[t]).offset().top;e.scrollTop(s-=70),this.data.set("curView2",t),this.data.set("screenText",i),this.data.set("readText",i),this.logSend("服务区"+n)},i.prototype.handleAreaClick=function(){this.fire("readScreen",{}),this.logSend("退出引导")},i.prototype.handleBottomPop=function(){var n=this.data.get("bigTextDom"),t=this.data.get("from");
n&&n.css("padding-bottom","160px"),"index"===t&&($("#bottom_layer").css("bottom","160px"),$("#s_side_wrapper").css("bottom","216px"))},i.prototype.handleClose=function(){var n=this.data.get("from"),t=this.data.get("bigTextDom");this.data.set("showPopup",!1),$("#s_side_wrapper").css("bottom",""),t&&t.css("padding-bottom",""),"index"===n&&($("#bottom_layer").css("bottom",""),$("#s_side_wrapper").css("bottom",""))},i.components={"a-item":r.default,"a-voice-item":l.default,"a-bottom-popup":u.default},i);
t.default=u},function(n,t,e){var i=e(2),o=[e(21)],a=e(23),s=e(8).default;n.exports=e(8),n.exports.default=i(s,a,o)},function(n,t,e){var i=e(22);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("0a9649d4",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})},function(n,t,e){(t=e(3)(!1)).push([n.i,".tools-wrap_TM9u8,\n.tools-wrap-new_6HSYA {\n  position: absolute;\n  top: 0;\n  background: #eee;\n  height: 136px;\n  width: 100%;\n  z-index: 333;\n  box-sizing: border-box;\n  text-align: left;\n  padding: 16px 0 0 86px;\n}\n.tools-wrap-new_6HSYA {\n  min-width: 1500px;\n}\n.aging-tools-gap {\n  padding-top: 136px;\n}\n.hide_3y2AL {\n  display: none;\n}\n",""]),t.locals={"tools-wrap":"tools-wrap_TM9u8",toolsWrap:"tools-wrap_TM9u8","tools-wrap-new":"tools-wrap-new_6HSYA",toolsWrapNew:"tools-wrap-new_6HSYA",hide:"hide_3y2AL"},n.exports=t
},function(n){n.exports=' <div id="aging-tools-pc"> <init-list s-if="show && newType === \'1\'" class="{{$style[\'tools-wrap-new\']}} {{showArea ? $style.hide : \'\'}}" disableVoice="{{ disableVoice }}" rootDom="{{ rootDom }}" scaleDom="{{ scaleDom }}" quickType="{{ quickType }}" from="{{ from }}" readText="{{ readText }}" bigTextDom="{{ bigTextDom }}" ref="initList" statusList="{{statusList}}" on-quit="toggleTools(\'quit\')" showStatus="{{ !showArea }}" style="width: {{ totalWidth }}px;" on-scale="handleScale" on-quickKeyUp="handleKeup" on-readScreen="handleReadScreen" on-logSend="logSend"> </init-list> <read-screen s-if="showArea" useNav="{{from === \'index\' ? true : false}}" from="{{ from }}" bigTextDom="{{ bigTextDom }}" class="{{$style[\'tools-wrap-new\']}}" statusList="{{ statusList }}" on-readScreen="handleReadScreen" on-logSend="logSend" on-quit="toggleTools(\'quit\')"> </read-screen> </div> '
},function(n,t,e){var i=e(2),o=[e(25)],a=e(27),s=e(9).default;n.exports=e(9),n.exports.default=i(s,a,o)},function(n,t,e){var i=e(26);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("1d768b29",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})},function(n,t,e){(t=e(3)(!1)).push([n.i,"@media screen and (min-width: 1116px) {\n  .aging-tools-result {\n    overflow-x: auto;\n  }\n}\n.aging-tools-result body {\n  overflow: initial;\n  min-width: 1500px;\n}\n.darkmode.dark {\n  background-color: #1F1F25;\n}\n.darkmode.blue {\n  background-color: #141E42;\n}\n.darkmode a {\n  color: #FFD862;\n}\n.darkmode a:hover {\n  color: #FFF762;\n}\n.darkmode a:visited {\n  color: #EA80FF;\n}\n.explain-link_3L7n4 {\n  display: inline-block;\n  margin-right: 10px;\n}\n",""]),t.locals={"explain-link":"explain-link_3L7n4",explainLink:"explain-link_3L7n4"},n.exports=t
},function(n){n.exports=' <div> <a-item text="重置" class="{{$style.item}}" type="reset" on-click="handleReset" key="重置 Ctrl 加 Alt 加 C"> </a-item> <a-item text="退出服务" wide class="{{$style.item}}" type="quit" on-click="handleQuit" key="关闭服务 Ctrl 加 Alt 加 E"> </a-item> <a-voice-item s-ref="voice" readText="{{ readText }}" from="{{ from }}" disableVoice="{{ disableVoice }}" statusList="{{ statusList }}" showStatus="{{ showStatus }}" quickType="{{quickType}}" on-quickKeyUp="fireQuickKeyUp" on-getReadingText="handleReading" on-logSend="logSend"></a-voice-item> <a-item text="配色" class="{{$style.item}}" on-click="handleColor" type="color" key="当前配色为{{colorText}}，切换 Ctrl 加 Alt 加 T" colorProps="{{ colorProps }}"> </a-item> <a-item text="放大" class="{{$style.item}}" checked="{{ enlargeChecked }}" key="当前放缩倍数为{{scaleText}}，{{scale === 1.3 ? \'已放至最大，\' : \'\'}}放大 Ctrl 加 Alt 加 加号" type="enlarge" on-click="handleScale(\'enlarge\')"> </a-item> <a-item text="缩小" wide class="{{$style.item}}" key="当前放缩倍速为{{scaleText}}，{{scale === 1 ? \'已缩至最小，\' : \'\'}}缩小 Ctrl 加 Alt 加 减号" type="narrow" on-click="handleScale(\'\')"> </a-item> <a-item text="鼠标样式" class="{{$style.item}}" checked="{{ cursorChecked }}" type="cursor" key="鼠标样式{{cursorChecked ? \'关闭\' : \'开启\'}} Ctrl 加 Alt 加 M" on-click="handleCursor"> </a-item> <a-cross eventStatus="{{ crossEvent }}" on-click="initCrossEvent" on-reset="initCrossEvent" on-change="handleChange" newType="{{ true }}"> </a-cross> <a-big-text s-ref="text" readText="{{ readText }}" statusList="{{ statusList }}" quickType="{{quickType}}" on-quickKeyUp="fireQuickKeyUp" on-click="initBigEvent" on-reset="initBigEvent" on-logSend="logSend" bigTextDom="{{ bigTextDom }}" from="{{ from }}" eventStatus="{{ bigTextEvent }}"></a-big-text> <a href="{{ explainUrl }}" class="{{$style[\'explain-link\']}}" target="_blank" on-click="logSend(\'说明\')"> <a-item text="说明" class="{{$style.item}}" nogap type="explain" key="说明 Ctrl 加 Alt 加 问号键" tabIndex="-1"> </a-item> </a> <a-more newType="{{ true }}" on-logSend="logSend"></a-more> <a-item s-if="!disableVoice" text="读屏幕" wide class="{{$style.item}}" type="area" key="读屏幕 Ctrl 加 Alt 加 R" on-click="handleAreaClick"> </a-item> </div> '
},function(n,t,e){var i=e(29);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("b4391324",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})},function(n,t,e){(t=e(3)(!1)).push([n.i,".item-wrap_fLQ-9 {\n  display: inline-block;\n  cursor: pointer;\n  vertical-align: top;\n}\n.item-wrap_fLQ-9.gap_3KngU {\n  margin-right: 10px;\n}\n.item-wrap_fLQ-9.wide_1YcTX {\n  margin-right: 28px;\n}\n.item-wrap_fLQ-9.checked_3usXg .icon-wrap_2yOX4:not(.choosed_2-EFw),\n.item-wrap_fLQ-9:hover .icon-wrap_2yOX4:not(.choosed_2-EFw) {\n  background-color: #6D87F2;\n}\n.item-wrap_fLQ-9.checked_3usXg .reset_26MfB,\n.item-wrap_fLQ-9:hover .reset_26MfB,\n.item-wrap_fLQ-9.checked_3usXg .color_s_MM5,\n.item-wrap_fLQ-9:hover .color_s_MM5 {\n  background-position-y: -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .voiceOn_1V98d,\n.item-wrap_fLQ-9:hover .voiceOn_1V98d {\n  background-position: -86px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .voiceOff_3hlPl,\n.item-wrap_fLQ-9:hover .voiceOff_3hlPl {\n  background-position: -172px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .speed_2ZdLK,\n.item-wrap_fLQ-9:hover .speed_2ZdLK {\n  background-position: -258px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .speed2_2a970,\n.item-wrap_fLQ-9:hover .speed2_2a970 {\n  background-position: -344px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .pointread_jzOlt,\n.item-wrap_fLQ-9:hover .pointread_jzOlt {\n  background-position: -430px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .seriesread_3ukmn,\n.item-wrap_fLQ-9:hover .seriesread_3ukmn {\n  background-position: -516px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .enlarge_2xX4f,\n.item-wrap_fLQ-9:hover .enlarge_2xX4f {\n  background-position: -860px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .narrow_15s9V,\n.item-wrap_fLQ-9:hover .narrow_15s9V {\n  background-position: -946px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .cursor_lqZ75,\n.item-wrap_fLQ-9:hover .cursor_lqZ75 {\n  background-position: -1032px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .cross_3CfAa,\n.item-wrap_fLQ-9:hover .cross_3CfAa {\n  background-position: -1118px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .bigText_3eYOa,\n.item-wrap_fLQ-9:hover .bigText_3eYOa {\n  background-position: -1204px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .explain_2WH2s,\n.item-wrap_fLQ-9:hover .explain_2WH2s {\n  background-position: -1290px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .more_2YK94,\n.item-wrap_fLQ-9:hover .more_2YK94 {\n  background-position: -1376px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .area_1wflQ,\n.item-wrap_fLQ-9:hover .area_1wflQ {\n  background-position: -1462px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .areaQuit_3y_PU,\n.item-wrap_fLQ-9:hover .areaQuit_3y_PU {\n  background-position: -1548px -86px;\n}\n.item-wrap_fLQ-9.checked_3usXg .quit_2XYvr,\n.item-wrap_fLQ-9:hover .quit_2XYvr {\n  background-position: -1634px -86px;\n}\n.icon-wrap_2yOX4 {\n  background-color: #fff;\n  box-shadow: 0 0 5px 0 rgba(98, 102, 117, 0.1);\n  border-radius: 22px;\n  width: 76px;\n  height: 76px;\n  padding-top: 20px;\n  box-sizing: border-box;\n}\n.icon-wrap_2yOX4.dark_2bW7C {\n  background-color: #1D1E1F;\n}\n.icon-wrap_2yOX4.dark_2bW7C .icon_C7gQt {\n  background-position: -860px 0;\n}\n.icon-wrap_2yOX4.blue_2tWp7 {\n  background-color: #243476;\n}\n.icon-wrap_2yOX4.blue_2tWp7 .icon_C7gQt {\n  background-position: -860px 0;\n}\n.item-text_3UFwO {\n  font-size: 18px;\n  color: #333;\n  text-align: center;\n  line-height: 38px;\n}\n.icon_C7gQt {\n  width: 36px;\n  height: 36px;\n  margin: 0 auto;\n  background: url(https://dss2.bdstatic.com/5bVYsj_p_tVS5dKfpU_Y_D3/res/r/image/2021-7-29/tubiaoqietu.png) no-repeat;\n  background-size: 1756px;\n}\n.voiceOn_1V98d {\n  background-position: -86px 0;\n}\n.voiceOff_3hlPl {\n  background-position: -172px 0;\n}\n.speed_2ZdLK {\n  background-position: -258px 0;\n}\n.speed2_2a970 {\n  background-position: -344px 0;\n}\n.pointread_jzOlt {\n  background-position: -430px 0;\n}\n.seriesread_3ukmn {\n  background-position: -516px 0;\n}\n.color_s_MM5 {\n  background-position: -774px 0;\n}\n.enlarge_2xX4f {\n  background-position: -946px 0;\n}\n.narrow_15s9V {\n  background-position: -1032px 0;\n}\n.cursor_lqZ75 {\n  background-position: -1118px 0;\n}\n.cross_3CfAa {\n  background-position: -1204px 0;\n}\n.bigText_3eYOa {\n  background-position: -1290px 0;\n}\n.explain_2WH2s {\n  background-position: -1376px 0;\n}\n.more_2YK94 {\n  background-position: -1462px 0;\n}\n.area_1wflQ {\n  background-position: -1548px 0;\n}\n.areaQuit_3y_PU {\n  background-position: -1634px 0;\n}\n.quit_2XYvr {\n  background-position: -1720px 0;\n}\n",""]),t.locals={"item-wrap":"item-wrap_fLQ-9",itemWrap:"item-wrap_fLQ-9",gap:"gap_3KngU",wide:"wide_1YcTX",checked:"checked_3usXg","icon-wrap":"icon-wrap_2yOX4",iconWrap:"icon-wrap_2yOX4",choosed:"choosed_2-EFw",reset:"reset_26MfB",color:"color_s_MM5",voiceOn:"voiceOn_1V98d",voiceOff:"voiceOff_3hlPl",speed:"speed_2ZdLK",speed2:"speed2_2a970",pointread:"pointread_jzOlt",seriesread:"seriesread_3ukmn",enlarge:"enlarge_2xX4f",narrow:"narrow_15s9V",cursor:"cursor_lqZ75",cross:"cross_3CfAa",bigText:"bigText_3eYOa",explain:"explain_2WH2s",more:"more_2YK94",area:"area_1wflQ",areaQuit:"areaQuit_3y_PU",quit:"quit_2XYvr",dark:"dark_2bW7C",icon:"icon_C7gQt",blue:"blue_2tWp7","item-text":"item-text_3UFwO",itemText:"item-text_3UFwO"},n.exports=t
},function(n){n.exports=' <div class="{{$style[\'item-wrap\']}} {{nogap ? \'\' : (wide ? $style.wide : $style.gap)}} {{checked ? $style.checked : \'\'}}" aria-label="{{ key || text }}" tabindex="{{tabIndex}}" on-click="itemClk"> <div class="{{$style[\'icon-wrap\']}} {{colorProps.type ? [$style[colorProps.type], $style.choosed] : \'\'}}"> <div class="{{$style.icon}} {{$style[type]}}"></div> </div> <div class="{{$style[\'item-text\']}}" aria-hidden="true">{{ text }}</div> </div> '},function(n,t,e){var i=e(2),o=[e(32)],a=e(34),s=e(11).default;
n.exports=e(11),n.exports.default=i(s,a,o)},function(n,t,e){var i=e(33);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("27d1fb2e",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})},function(n,t,e){(t=e(3)(!1)).push([n.i,".line_biMr9 {\n  position: fixed;\n  z-index: 999;\n  border: 4px solid #FF455D;\n  border-radius: 4px;\n}\n.horizontal_2maiJ {\n  width: 576px;\n  height: 0;\n}\n.vertical_2RsfS {\n  height: 576px;\n  width: 0;\n}\n.cross-wrap_nhMWw {\n  margin-right: 10px;\n  display: inline-block;\n}\n.wide-gap__4GZe {\n  margin-right: 28px;\n}\n",""]),t.locals={line:"line_biMr9",horizontal:"horizontal_2maiJ",vertical:"vertical_2RsfS","cross-wrap":"cross-wrap_nhMWw",crossWrap:"cross-wrap_nhMWw","wide-gap":"wide-gap__4GZe",wideGap:"wide-gap__4GZe"},n.exports=t
},function(n){n.exports=' <div class="{{$style[\'cross-wrap\']}} {{!newType ? $style[\'wide-gap\'] : \'\'}}"> <a-item text="十字线" nogap class="{{$style.item}}" type="cross" checked="{{ crossChecked }}" key="十字线{{showCross ? \'关闭\' : \'开启\'}} Ctrl 加 Alt 加 N" ref="crossDom" on-click="handleCross"> </a-item> <div s-if="showCross"> <div class="{{$style.line}} {{$style.horizontal}}" style="{{ horizontalStyle }}" s-ref="horizontal"></div> <div class="{{$style.line}} {{$style.vertical}}" style="{{ verticalStyle }}" s-ref="vertical"></div> </div> </div> '
},function(n,t,e){var i=e(2),o=[e(36)],a=e(38),s=e(12).default;n.exports=e(12),n.exports.default=i(s,a,o)},function(n,t,e){var i=e(37);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("52a745b0",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})},function(n,t,e){(t=e(3)(!1)).push([n.i,".float-wrap_giJYT {\n  background: #fff;\n  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);\n  border-radius: 12px;\n  width: 279px;\n  height: 102px;\n  position: absolute;\n  right: 0;\n  display: none;\n}\n.float-wrap_giJYT .text_WQd7Q {\n  text-align: left;\n  margin-top: 24px;\n  margin-left: 16px;\n  display: inline-block;\n}\n.float-wrap_giJYT .text_WQd7Q .title_C_Aaw {\n  color: #000;\n  font: 20px/30px Arial, sans-serif;\n}\n.float-wrap_giJYT .text_WQd7Q .subtitle_10NDD {\n  color: #9195a3;\n  font: 13px/23px Arial, sans-serif;\n}\n.float-wrap_giJYT .qrcode_3mmyy {\n  width: 70px;\n  height: 70px;\n  display: inline-block;\n  float: right;\n  margin: 16px 16px 0 0;\n}\n.more-wrap_2DA8x {\n  display: inline-block;\n  position: relative;\n  margin-right: 10px;\n}\n.more-wrap_2DA8x:hover .float-wrap_giJYT {\n  display: block;\n}\n.wide-gap_-oZ2Y {\n  margin-right: 28px;\n}\n",""]),t.locals={"float-wrap":"float-wrap_giJYT",floatWrap:"float-wrap_giJYT",text:"text_WQd7Q",title:"title_C_Aaw",subtitle:"subtitle_10NDD",qrcode:"qrcode_3mmyy","more-wrap":"more-wrap_2DA8x",moreWrap:"more-wrap_2DA8x","wide-gap":"wide-gap_-oZ2Y",wideGap:"wide-gap_-oZ2Y"},n.exports=t
},function(n){n.exports=' <div class="{{$style[\'more-wrap\']}} {{newType ? \'\' : $style[\'wide-gap\']}}" on-mouseenter="enter" on-mouseleave="leave"> <a-item text="更多服务" wide class="{{$style.item}}" type="more" nogap></a-item> <div class="{{$style[\'float-wrap\']}}"> <div class="{{$style.text}}"> <div class="{{$style.title}}">扫码下载百度APP</div> <div class="{{$style.subtitle}}">有事搜一搜&nbsp;&nbsp;没事看一看</div> </div> <img class="{{$style.qrcode}}" src="https://dss0.bdstatic.com/5aV1bjqh_Q23odCf/static/mancard/img/qrcode_download-02b84e1f66.png"> </div> </div> '
},function(n,t,e){var i=e(40);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("1a02c38e",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})},function(n,t,e){(t=e(3)(!1)).push([n.i,".voice-wrap_3jP_f {\n  display: inline-block;\n}\n.aging-toast {\n  position: fixed;\n  z-index: 10;\n  padding: 0px 16px;\n  height: 30px;\n  line-height: 30px;\n  background: #fff;\n  border-radius: 15px;\n  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);\n  top: 50%;\n  left: 50%;\n  margin-left: -100px;\n  margin-top: -15px;\n  color: #222;\n}\n",""]),t.locals={"voice-wrap":"voice-wrap_3jP_f",voiceWrap:"voice-wrap_3jP_f"},n.exports=t
},function(n){n.exports=' <div class="{{$style[\'voice-wrap\']}}"> <template s-if="!disableVoice"> <a-item text="{{type.onoff === \'voiceOn\' ? \'声音开\' : \'声音关\'}}" key="{{type.onoff === \'voiceOn\' ? \'声音开启\' : \'声音关闭\'}} Ctrl 加 Alt 加 V" class="{{$style.item}}" on-click="voiceOnOff" type="{{ type.onoff }}"> </a-item> <template s-if="!onlyOnoff"> <a-item text="{{type.speed === \'speed2\' ? \'语速快\' : \'语速\'}}" key="语速调节 Ctrl 加 Alt 加 K" class="{{$style.item}}" on-click="handleSpeed" type="{{ type.speed }}"> </a-item> <a-item text="{{type.mode === \'pointread\' ? \'点读模式\' : \'连读模式\'}}" key="当前为{{type.mode === \'pointread\' ? \'点读模式\' : \'连读模式\'}}，切换 Ctrl 加 Alt 加 F" wide class="{{$style.item}}" on-click="handleReadmode" type="{{ type.mode }}"> </a-item> </template> </template> </div> '
},function(n,t){"use strict";t.__esModule=!0;var e={target:null,val:""};t.default=function(n,t){return n[0]?("index"!==t?function(n){var t="",i="";if(5<n.find("a").length||3<n.find("div").length||3<n.find("span").length||5<n.find("input").length||n.children()[0]&&"HTML"===n.children()[0].tagName||n.hasClass("c-icon")||n.attr("aria-hidden")||"none"===n.css("display"))return e;if(n.attr("aria-label")||n.parents("[aria-label]").length&&n.parents("[aria-label]").attr("aria-label")){var o="";return{val:o=(n.attr("aria-label")?n:n.parents("[aria-label]")).attr("aria-label"),target:n}
}if(n.parents("[tpl=right_toplist1]").length){var a=n.attr("title")||n.find("[title]").attr("title");if(a)return{val:"热搜："+a,target:n};if(n.children().length<1)return{val:n.text(),target:n}}else{if(n.attr("title"))return{val:n.attr("title"),target:n};if("input"===n[0].tagName)return{val:"su"===n.attr("id")?"百度一下：搜索提交按钮":n.val(),target:n};if(n.find("a").length||"A"===n.parent()[0].tagName||"A"===n[0].tagName){if(a=n,n.find("a").length?a=n.find("a"):"A"===n.parent()[0].tagName?a=n.parent():"A"===n[0].tagName&&(a=n),a.attr("href")?(i="链接：",a.hasClass("c-title")||a.hasClass("t")?i="标题链接：":a.parent().length&&a.parent().hasClass("s_tab_inner")&&(i="导航：")):"button"===a.attr("role")&&(i="按钮："),a.find("[aria-label]").length&&a.find("[aria-label]").attr("aria-label")){var s=a.find("[aria-label]");
i="";for(var r=0;r<s.lenth;r++)t+=s.eq(r).attr("aria-label")}else if(a.attr("title"))a.hasClass("c-img")&&(i="图片："),t=a.attr("title");else if(a.find("span").length&&a.find("span").text())t=a.find("span").text();else{if(!a.text())return{val:"",link:a};var l=n.find(".c-icon"),o=a.text();l.length&&(o=o.replace(l.text(),"")),t=o}return{val:i+t,target:a}}if("EM"===n[0].tagName)return{val:n.parent().text(),target:n};if(n.hasClass("c-abstract")||0===n.children("div").length||n.text())return l=n.find(".c-icon"),o=n.text(),{val:o=l.length?o.replace(l.text(),""):o,target:n}
}return e}:function(n){var t="",i="";if(n.children()[0]&&"HTML"===n.children()[0].tagName||"head"===n.attr("id")||"head_wrapper"===n.attr("id")||5<n.find("a").length||5<n.find("div").length||n.hasClass("aging-popup")||n.parents().hasClass("aging-popup")||n.hasClass("c-icon")||n.attr("aria-hidden")||"none"===n.css("display"))return e;if(n.attr("aria-label")||n.parents().length&&n.parents("[aria-label]").length)return{val:(n.attr("aria-label")?n:n.parents("[aria-label]")).attr("aria-label"),target:n};
if(n.parents("[tpl=hot-news]").length||n.parents("#s-hotsearch-wrapper").length){if(0<n.find("span").length)return{val:"热搜："+n.find("span").eq(1).text(),target:n.find("span").eq(1)};if(n.text())return{val:(n.hasClass("title-content-title")?"热搜：":"")+n.text(),target:n}}else{if(n.find("a").length||n.parent()[0]&&"A"===n.parent()[0].tagName||n[0]&&"A"===n[0].tagName){var o=n;return n.find("a").length?o=n.find("a"):"A"===n.parent()[0].tagName?o=n.parent():"A"===n[0].tagName&&(o=n),o.attr("href")?i="链接：":"button"===o.attr("role")&&(i="按钮："),t=o.attr("title")?(o.hasClass("c-img")&&(i="图片："),o.attr("title")):(o.find("span").length?o.find("span"):o).text(),{val:i+t,target:o}
}if(n.attr("title"))return{val:n.attr("title"),target:n};if(0===n.children().length&&n.text()&&!n.hasClass("c-icon"))return{val:n.text(),target:n};if("su"===n.attr("id"))return{val:"百度一下：搜索提交按钮",target:n};if(n.text())return{val:n.text(),target:n}}return e})(n):e}},function(n,t,e){var i=e(2),o=[e(44)],a=e(46),s=e(16).default;n.exports=e(16),n.exports.default=i(s,a,o)},function(n,t,e){var i=e(45);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("7a24e036",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})
},function(n,t,e){(t=e(3)(!1)).push([n.i,".big-wrap_1tRU- {\n  display: inline-block;\n}\n",""]),t.locals={"big-wrap":"big-wrap_1tRU-",bigWrap:"big-wrap_1tRU-"},n.exports=t},function(n){n.exports=' <div class="{{$style[\'big-wrap\']}}"> <a-item text="大字幕" wide class="{{$style.item}}" checked="{{ showPopup }}" type="bigText" on-click="handleBigText" key="大字幕{{showPopup ? \'关闭\' : \'开启\'}} Ctrl 加 Alt 加 B"> </a-item> <a-bottom-popup s-if="showPopup" readText="{{ readText }}" on-close="handleClose" on-logSend="logSend"></a-bottom-popup> </div> '
},function(n,t,e){var i=e(48);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("4c9cf4ac",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})},function(n,t,e){(t=e(3)(!1)).push([n.i,".pop-wrap_1uXqB {\n  background: #FFF;\n  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);\n  position: fixed;\n  bottom: 0;\n  width: 100%;\n  height: 160px;\n  left: 0;\n  padding: 20px 0 16px 30px;\n  box-sizing: border-box;\n  min-width: 850px;\n}\n.right_2nQS4 {\n  width: 10%;\n  display: inline-block;\n  margin: 8px 0 0 26px;\n}\n.btn_3l72k {\n  background: none;\n  border: 0;\n  width: 100px;\n  height: 45px;\n  display: block;\n  background: #F5F5F6;\n  border-radius: 6px;\n  font-size: 18px;\n  color: #222;\n  margin: 0 0 16px;\n}\n.btn_3l72k:hover {\n  color: #4E6EF2;\n}\n.left_2-OWY {\n  width: 85.5%;\n  display: inline-block;\n  position: relative;\n  vertical-align: top;\n  color: #222;\n  height: 100%;\n  overflow: hidden;\n}\n@media screen and (max-width: 1200px) {\n  .left_2-OWY {\n    width: 80%;\n  }\n}\n.scrollBar_3WnAP {\n  position: absolute;\n  width: 5px;\n  height: 120px;\n  right: 0;\n  top: 0;\n}\n.pinyinText_23jFC {\n  font-size: 30px;\n  overflow-y: scroll;\n  width: 101%;\n  padding-right: 70px;\n  box-sizing: border-box;\n  height: 100%;\n  padding-left: 70px;\n}\n.text_uCpY0 {\n  font-size: 53px;\n  letter-spacing: -2px;\n  margin: -10px 15px 0 0;\n  line-height: 82px;\n  height: 100%;\n  overflow-y: scroll;\n  width: 102%;\n  padding-right: 75px;\n  box-sizing: border-box;\n  padding-left: 70px;\n}\n.barInner_28044 {\n  background: #4E6EF2;\n  border-radius: 2.5px;\n  width: 5px;\n  height: 40px;\n  position: absolute;\n}\n.barBac_1AEEx {\n  position: absolute;\n  background: #F1F1F0;\n  border-radius: 2.5px;\n  width: 5px;\n  height: 120px;\n}\n.close_3bbQs {\n  font-size: 18px;\n  color: #9195a3;\n  position: absolute;\n  width: 18px;\n  height: 18px;\n  cursor: pointer;\n  right: 16px;\n  top: 16px;\n}\n.close_3bbQs:hover {\n  color: #315efb;\n}\n.item_1734- {\n  display: inline-block;\n  text-align: center;\n  margin-right: 20px;\n}\n.char_xMu3i {\n  font-size: 25px;\n}\n",""]),t.locals={"pop-wrap":"pop-wrap_1uXqB",popWrap:"pop-wrap_1uXqB",right:"right_2nQS4",btn:"btn_3l72k",left:"left_2-OWY",scrollBar:"scrollBar_3WnAP",pinyinText:"pinyinText_23jFC",text:"text_uCpY0",barInner:"barInner_28044",barBac:"barBac_1AEEx",close:"close_3bbQs",item:"item_1734-","char":"char_xMu3i"},n.exports=t
},function(n){n.exports=' <div class="{{$style[\'pop-wrap\']}} new-pmd aging-popup"> <div class="{{$style.left}}"> <div class="{{showType === \'pinyin\' ? $style.pinyinText : $style.text}}" s-ref="scrollArea"> <div class="{{$style[\'scroll-inner\']}}" s-ref="scrollInner" style="overflow-x: hidden;"> <div s-if="displayFantiText" style="overflow-x: hidden;">{{ displayFantiText }}</div> <div s-else> <div s-for="item in displayPinyinText" class="{{$style.item}}"> <div class="{{$style.char}}">{{item.char}}</div> <div>{{item.text}}</div> </div> </div> </div> </div> <div s-if="showBar" class="{{$style.scrollBar}}"> <div class="{{$style.barBac}}"></div> <div class="{{$style.barInner}}" style="top: {{barTop}}px;"></div> </div> </div> <div class="{{$style.right}}"> <button class="{{$style.btn}} {{$style.pinyin}} {{showType === \'pinyin\' ? $style.checked : \'\'}}" on-click="usePinyin">拼音</button> <button class="{{$style.btn}} {{$style.fanti}} {{showType === \'fanti\' ? $style.checked : \'\'}}" on-click="useFanti">{{ fantiType }}</button> </div> <i class="c-icon {{$style.close}}" on-click="closePopup">&#xe610;</i> </div> '
},function(n,t){"use strict";t.__esModule=!0,t.pinyin=void 0,t.pinyin={"圩":"wéi",1:"yī",2:"èr",3:"sān",4:"sì",5:"wǔ",6:"liù",7:"qī",8:"bā",9:"jiǔ",0:"líng","啊":"ā","阿":"ā","埃":"āi","挨":"āi","哎":"āi","唉":"āi","哀":"āi","皑":"ái","癌":"ái","蔼":"ǎi","矮":"ǎi","艾":"ài","碍":"ài","爱":"ài","隘":"ài","鞍":"ān","氨":"ān","安":"ān","俺":"ǎn","按":"àn","暗":"àn","岸":"àn","胺":"àn","案":"àn","肮":"āng","昂":"áng","盎":"àng","凹":"āo","敖":"áo","熬":"áo","翱":"áo","袄":"ǎo","傲":"ào","奥":"ào","懊":"ào","澳":"ào","芭":"bā","捌":"bā","扒":"bā","叭":"bā","吧":"bā","笆":"bā","八":"bā","疤":"bā","巴":"bā","拔":"bá","跋":"bá","靶":"bǎ","把":"bǎ","耙":"pá","坝":"bà","霸":"bà","罢":"bà","爸":"bà","白":"bái","柏":"bǎi","百":"bǎi","摆":"bǎi","佰":"bǎi","败":"bài","拜":"bài","稗":"bài","斑":"bān","班":"bān","搬":"bān","扳":"bān","般":"bān","颁":"bān","板":"bǎn","版":"bǎn","扮":"bàn","拌":"bàn","伴":"bàn","瓣":"bàn","半":"bàn","办":"bàn","绊":"bàn","邦":"bāng","帮":"bāng","梆":"bāng","榜":"bǎng","膀":"bǎng","绑":"bǎng","棒":"bàng","磅":"páng","蚌":"bàng","镑":"bàng","傍":"bàng","谤":"bàng","苞":"bāo","胞":"bāo","包":"bāo","褒":"bāo","剥":"bāo","薄":"báo","雹":"báo","保":"bǎo","堡":"bǎo","饱":"bǎo","宝":"bǎo","抱":"bào","报":"bào","暴":"bào","豹":"bào","鲍":"bào","爆":"bào","杯":"bēi","碑":"bēi","悲":"bēi","卑":"bēi","北":"běi","辈":"bèi","背":"bèi","贝":"bèi","钡":"bèi","倍":"bèi","狈":"bèi","备":"bèi","惫":"bèi","焙":"bèi","被":"bèi","奔":"bēn","苯":"běn","本":"běn","笨":"bèn","崩":"bēng","绷":"bēng","甭":"béng","泵":"bèng","蹦":"bèng","迸":"bèng","逼":"bī","鼻":"bí","比":"bǐ","鄙":"bǐ","笔":"bǐ","彼":"bǐ","碧":"bì","蓖":"bì","蔽":"bì","毕":"bì","毙":"bì","毖":"bì","币":"bì","庇":"bì","痹":"bì","闭":"bì","敝":"bì","弊":"bì","必":"bì","辟":"pì","壁":"bì","臂":"bì","避":"bì","陛":"bì","鞭":"biān","边":"biān","编":"biān","贬":"biǎn","扁":"biǎn","便":"biàn","变":"biàn","卞":"biàn","辨":"biàn","辩":"biàn","辫":"biàn","遍":"biàn","标":"biāo","彪":"biāo","膘":"biāo","表":"biǎo","鳖":"biē","憋":"biē","别":"bié","瘪":"biě","彬":"bīn","斌":"bīn","濒":"bīn","滨":"bīn","宾":"bīn","摈":"bìn","兵":"bīng","冰":"bīng","柄":"bǐng","丙":"bǐng","秉":"bǐng","饼":"bǐng","炳":"bǐng","病":"bìng","并":"bìng","玻":"bō","菠":"bō","播":"bō","拨":"bō","钵":"bō","波":"bō","博":"bó","勃":"bó","搏":"bó","铂":"bó","箔":"bó","伯":"bó","帛":"bó","舶":"bó","脖":"bó","膊":"bó","渤":"bó","泊":"bó","驳":"bó","捕":"bǔ","卜":"bo","哺":"bǔ","补":"bǔ","埠":"bù","不":"bù","布":"bù","步":"bù","簿":"bù","部":"bù","怖":"bù","擦":"cā","猜":"cāi","裁":"cái","材":"cái","才":"cái","财":"cái","睬":"cǎi","踩":"cǎi","采":"cǎi","彩":"cǎi","菜":"cài","蔡":"cài","餐":"cān","参":"cān","蚕":"cán","残":"cán","惭":"cán","惨":"cǎn","灿":"càn","苍":"cāng","舱":"cāng","仓":"cāng","沧":"cāng","藏":"cáng","操":"cāo","糙":"cāo","槽":"cáo","曹":"cáo","草":"cǎo","厕":"cè","策":"cè","侧":"cè","册":"cè","测":"cè","层":"céng","蹭":"cèng","插":"chā","叉":"chā","茬":"chá","茶":"chá","查":"chá","碴":"chá","搽":"chá","察":"chá","岔":"chà","差":"chà","诧":"chà","拆":"chāi","柴":"chái","豺":"chái","搀":"chān","掺":"chān","蝉":"chán","馋":"chán","谗":"chán","缠":"chán","铲":"chǎn","产":"chǎn","阐":"chǎn","颤":"chàn","昌":"chāng","猖":"chāng","场":"chǎng","尝":"cháng","常":"cháng","长":"cháng","偿":"cháng","肠":"cháng","厂":"chǎng","敞":"chǎng","畅":"chàng","唱":"chàng","倡":"chàng","超":"chāo","抄":"chāo","钞":"chāo","朝":"cháo","嘲":"cháo","潮":"cháo","巢":"cháo","吵":"chǎo","炒":"chǎo","车":"chē","扯":"chě","撤":"chè","掣":"chè","彻":"chè","澈":"chè","郴":"chēn","臣":"chén","辰":"chén","尘":"chén","晨":"chén","忱":"chén","沉":"chén","陈":"chén","趁":"chèn","衬":"chèn","撑":"chēng","称":"chēng","城":"chéng","橙":"chéng","成":"chéng","呈":"chéng","乘":"chéng","程":"chéng","惩":"chéng","澄":"chéng","诚":"chéng","承":"chéng","逞":"chěng","骋":"chěng","秤":"chèng","吃":"chī","痴":"chī","持":"chí","匙":"chí","池":"shi","迟":"chí","弛":"chí","驰":"chí","耻":"chǐ","齿":"chǐ","侈":"chǐ","尺":"chǐ","赤":"chì","翅":"chì","斥":"chì","炽":"chì","充":"chōng","冲":"chōng","虫":"chóng","崇":"chóng","宠":"chǒng","抽":"chōu","酬":"chóu","畴":"chóu","踌":"chóu","稠":"chóu","愁":"chóu","筹":"chóu","仇":"chóu","绸":"chóu","瞅":"chǒu","丑":"chǒu","臭":"chòu","初":"chū","出":"chū","橱":"chú","厨":"chú","躇":"chú","锄":"chú","雏":"chú","滁":"chú","除":"chú","楚":"chǔ","础":"chǔ","储":"chǔ","矗":"chù","搐":"chù","触":"chù","处":"chǔ","揣":"chuāi","川":"chuān","穿":"chuān","椽":"chuán","传":"chuán","船":"chuán","喘":"chuǎn","串":"chuàn","疮":"chuāng","窗":"chuāng","幢":"zhuàng","床":"chuáng","闯":"chuǎng","创":"chuàng","吹":"chuí","炊":"chuī","捶":"chuī","锤":"chuí","垂":"chuí","春":"chūn","椿":"chūn","醇":"chún","唇":"chún","淳":"chún","纯":"chún","蠢":"chǔn","戳":"chuō","绰":"chuō","疵":"cī","茨":"cí","磁":"cí","雌":"cí","辞":"cí","慈":"cí","瓷":"cí","词":"cí","此":"cǐ","刺":"cì","赐":"cì","次":"cì","聪":"cōng","葱":"cōng","囱":"cōng","匆":"cōng","从":"cóng","丛":"cóng","凑":"còu","粗":"cū","醋":"cù","簇":"cù","促":"cù","蹿":"cuān","篡":"cuàn","窜":"cuàn","摧":"cuī","崔":"cuī","催":"cuī","脆":"cuì","瘁":"cuì","粹":"cuì","淬":"cuì","翠":"cuì","村":"cūn","存":"cún","寸":"cùn","磋":"cuō","撮":"cuō","搓":"cuō","措":"cuò","挫":"cuò","错":"cuò","搭":"dā","达":"dá","答":"dá","瘩":"dá","打":"dǎ","大":"dà","呆":"dāi","歹":"dǎi","傣":"dǎi","戴":"dài","带":"dài","殆":"dài","代":"dài","贷":"dài","袋":"dài","待":"dài","逮":"dǎi","怠":"dài","耽":"dān","担":"dān","丹":"dān","单":"dān","郸":"dān","掸":"dǎn","胆":"dǎn","旦":"dàn","氮":"dàn","但":"dàn","惮":"dàn","淡":"dàn","诞":"dàn","弹":"dàn","蛋":"dàn","当":"dāng","挡":"dǎng","党":"dǎng","荡":"dàng","档":"dàng","刀":"dāo","捣":"dǎo","蹈":"dǎo","倒":"dǎo","岛":"dǎo","祷":"dǎo","导":"dǎo","到":"dào","稻":"dào","悼":"dào","道":"dào","盗":"dào","德":"dé","得":"dé","的":"de","蹬":"dēng","灯":"dēng","登":"dēng","等":"děng","瞪":"dèng","凳":"dèng","邓":"dèng","堤":"dī","低":"dī","滴":"dī","迪":"dī","敌":"dí","笛":"dí","狄":"dí","涤":"dí","翟":"dí","嫡":"dí","抵":"dǐ","底":"dǐ","地":"dì","蒂":"dì","第":"dì","帝":"dì","弟":"dì","递":"dì","缔":"dì","颠":"diān","掂":"diān","滇":"diān","碘":"diǎn","点":"diǎn","典":"diǎn","靛":"diàn","垫":"diàn","电":"diàn","佃":"diàn","甸":"diàn","店":"diàn","惦":"diàn","奠":"diàn","淀":"diàn","殿":"diàn","碉":"diāo","叼":"diāo","雕":"diāo","凋":"diāo","刁":"diāo","掉":"diào","吊":"diào","钓":"diào","调":"diào","跌":"diē","爹":"diē","碟":"dié","蝶":"dié","迭":"dié","谍":"dié","叠":"dié","丁":"dīng","盯":"dīng","叮":"dīng","钉":"dìng","顶":"dǐng","鼎":"dǐng","锭":"dìng","定":"dìng","订":"dìng","丢":"diu1","东":"dōng","冬":"dōng","董":"dǒng","懂":"dǒng","动":"dòng","栋":"dòng","侗":"dòng","恫":"dòng","冻":"dòng","洞":"dòng","兜":"dōu","抖":"dǒu","斗":"dòu","陡":"dǒu","豆":"dòu","逗":"dòu","痘":"dòu","都":"dōu","督":"dū","毒":"dú","犊":"dú","独":"dú","读":"dú","堵":"dǔ","睹":"dǔ","赌":"dǔ","杜":"dù","镀":"dù","肚":"dù","度":"dù","渡":"dù","妒":"dù","端":"duān","短":"duǎn","锻":"duàn","段":"duàn","断":"duàn","缎":"duàn","堆":"duī","兑":"duì","队":"duì","对":"duì","墩":"dūn","吨":"dūn","蹲":"dūn","敦":"dūn","顿":"dùn","囤":"dùn","钝":"dùn","盾":"dùn","遁":"dùn","掇":"duō","哆":"duō","多":"duō","夺":"duó","垛":"duǒ","躲":"duǒ","朵":"duǒ","跺":"duò","舵":"duò","剁":"duò","惰":"duò","堕":"duò","蛾":"é","峨":"é","鹅":"é","俄":"é","额":"é","讹":"é","娥":"é","恶":"è","厄":"è","扼":"è","遏":"è","鄂":"è","饿":"è","恩":"ēn","而":"ér","儿":"ér","耳":"ěr","尔":"ěr","饵":"ěr","洱":"ěr","二":"èr","贰":"èr","发":"fā","罚":"fá","筏":"fá","伐":"fá","乏":"fá","阀":"fá","法":"fǎ","珐":"fà","藩":"fān","帆":"fān","番":"fān","翻":"fān","樊":"fán","矾":"fán","钒":"fán","繁":"fán","凡":"fán","烦":"fán","反":"fǎn","返":"fǎn","范":"fàn","贩":"fàn","犯":"fàn","饭":"fàn","泛":"fàn","坊":"fāng","芳":"fāng","方":"fāng","肪":"fáng","房":"fáng","防":"fáng","妨":"fáng","仿":"fǎng","访":"fǎng","纺":"fǎng","放":"fàng","菲":"fēi","非":"fēi","啡":"fēi","飞":"fēi","肥":"féi","匪":"fěi","诽":"fěi","吠":"fèi","肺":"fèi","废":"fèi","沸":"fèi","费":"fèi","芬":"fēn","酚":"fēn","吩":"fēn","氛":"fēn","分":"fēn","纷":"fēn","坟":"fén","焚":"fén","汾":"fén","粉":"fěn","奋":"fèn","份":"fèn","忿":"fèn","愤":"fèn","粪":"fèn","丰":"fēng","封":"fēng","枫":"fēng","蜂":"fēng","峰":"fēng","锋":"fēng","风":"fēng","疯":"fēng","烽":"fēng","逢":"féng","冯":"féng","缝":"féng","讽":"fěng","奉":"fèng","凤":"fèng","佛":"fó","否":"fǒu","夫":"fū","敷":"fū","肤":"fū","孵":"fū","扶":"fú","拂":"fú","辐":"fú","幅":"fú","氟":"fú","符":"fú","伏":"fú","俘":"fú","服":"fú","浮":"fú","涪":"fú","福":"fú","袱":"fú","弗":"fú","甫":"fǔ","抚":"fǔ","辅":"fǔ","俯":"fǔ","釜":"fǔ","斧":"fǔ","脯":"pú","腑":"fǔ","府":"fǔ","腐":"fǔ","赴":"fù","副":"fù","覆":"fù","赋":"fù","复":"fù","傅":"fù","付":"fù","阜":"fù","父":"fù","腹":"fù","负":"fù","富":"fù","讣":"fù","附":"fù","妇":"fù","缚":"fù","咐":"fù","噶":"gá","嘎":"gā","该":"gāi","改":"gǎi","概":"gài","钙":"gài","盖":"gài","溉":"gài","干":"gàn","甘":"gān","杆":"gǎn","柑":"gān","竿":"gān","肝":"gān","赶":"gǎn","感":"gǎn","秆":"gǎn","敢":"gǎn","赣":"gàn","冈":"gāng","刚":"gāng","钢":"gāng","缸":"gāng","肛":"gāng","纲":"gāng","岗":"gǎng","港":"gǎng","杠":"gàng","篙":"gāo","皋":"gāo","高":"gāo","膏":"gāo","羔":"gāo","糕":"gāo","搞":"gǎo","镐":"gǎo","稿":"gǎo","告":"gào","哥":"gē","歌":"gē","搁":"gē","戈":"gē","鸽":"gē","胳":"gē","疙":"gē","割":"gē","革":"gé","葛":"gě","格":"gé","蛤":"há","阁":"gé","隔":"gé","铬":"gè","个":"gè","各":"gè","给":"gěi","根":"gēn","跟":"gēn","耕":"gēng","更":"gèng","庚":"gēng","羹":"gēng","埂":"gěng","耿":"gěng","梗":"gěng","工":"gōng","攻":"gōng","功":"gōng","恭":"gōng","龚":"gōng","供":"gòng","躬":"gōng","公":"gōng","宫":"gōng","弓":"gōng","巩":"gǒng","汞":"gǒng","拱":"gǒng","贡":"gòng","共":"gòng","钩":"gōu","勾":"gōu","沟":"gōu","苟":"gǒu","狗":"gǒu","垢":"gòu","构":"gòu","购":"gòu","够":"gòu","辜":"gū","菇":"gū","咕":"gū","箍":"gū","估":"gū","沽":"gū","孤":"gū","姑":"gū","鼓":"gǔ","古":"gǔ","蛊":"gǔ","骨":"gǔ","谷":"gǔ","股":"gǔ","故":"gù","顾":"gù","固":"gù","雇":"gù","刮":"guā","瓜":"guā","剐":"guǎ","寡":"guǎ","挂":"guà","褂":"guà","乖":"guāi","拐":"guǎi","怪":"guài","棺":"guān","关":"guān","官":"guān","冠":"guàn","观":"guān","管":"guǎn","馆":"guǎn","罐":"guàn","惯":"guàn","灌":"guàn","贯":"guàn","光":"guāng","广":"guǎng","逛":"guàng","瑰":"guī","规":"guī","圭":"guī","硅":"guī","归":"guī","龟":"guī","闺":"guī","轨":"guǐ","鬼":"guǐ","诡":"guǐ","癸":"guǐ","桂":"guì","柜":"guì","跪":"guì","贵":"guì","刽":"guì","辊":"gǔn","滚":"gǔn","棍":"gùn","锅":"guō","郭":"guō","国":"guó","果":"guǒ","裹":"guǒ","过":"guò","哈":"hā","骸":"hái","孩":"hái","海":"hǎi","氦":"hài","亥":"hài","害":"hài","骇":"hài","酣":"hān","憨":"hān","邯":"hán","韩":"hán","含":"hán","涵":"hán","寒":"hán","函":"hán","喊":"hǎn","罕":"hǎn","翰":"hàn","撼":"hàn","捍":"hàn","旱":"hàn","憾":"hàn","悍":"hàn","焊":"hàn","汗":"hàn","汉":"hàn","夯":"hāng","杭":"háng","航":"háng","壕":"háo","嚎":"háo","豪":"háo","毫":"háo","郝":"hǎo","好":"hǎo","耗":"hào","号":"hào","浩":"hào","呵":"hē","喝":"hē","荷":"hé","菏":"hé","核":"hé","禾":"hé","和":"hé","何":"hé","合":"hé","盒":"hé","貉":"hè","阂":"hé","河":"hé","涸":"hé","赫":"hè","褐":"hè","鹤":"hè","贺":"hè","嘿":"hēi","黑":"hēi","痕":"hén","很":"hěn","狠":"hěn","恨":"hèn","哼":"hēng","亨":"hēng","横":"héng","衡":"héng","恒":"héng","轰":"hōng","哄":"hǒng","烘":"hōng","虹":"hóng","鸿":"hóng","洪":"hóng","宏":"hóng","弘":"hóng","红":"hóng","喉":"hóu","侯":"hòu","猴":"hóu","吼":"hǒu","厚":"hòu","候":"hòu","后":"hòu","呼":"hū","乎":"hū","忽":"hū","瑚":"hú","壶":"hú","葫":"hú","胡":"hú","蝴":"hú","狐":"hú","糊":"hú","湖":"hú","弧":"hú","虎":"hǔ","唬":"hǔ","护":"hù","互":"hù","沪":"hù","户":"hù","花":"huā","哗":"huá","华":"huá","猾":"huá","滑":"huá","画":"huà","划":"huá","化":"huà","话":"huà","槐":"huái","徊":"huái","怀":"huái","淮":"huái","坏":"huài","欢":"huān","环":"huán","桓":"huán","还":"hái","缓":"huǎn","换":"huàn","患":"huàn","唤":"huàn","痪":"huàn","豢":"huàn","焕":"huàn","涣":"huàn","宦":"huàn","幻":"huàn","荒":"huāng","慌":"huāng","黄":"huáng","磺":"huáng","蝗":"huáng","簧":"huáng","皇":"huáng","凰":"huáng","惶":"huáng","煌":"huáng","晃":"huàng","幌":"huǎng","恍":"huǎng","谎":"huǎng","灰":"huī","挥":"huī","辉":"huī","徽":"huī","恢":"huī","蛔":"huí","回":"huí","毁":"huǐ","悔":"huǐ","慧":"huì","卉":"huì","惠":"huì","晦":"huì","贿":"huì","秽":"huì","会":"huì","烩":"huì","汇":"huì","讳":"huì","诲":"huì","绘":"huì","荤":"hūn","昏":"hūn","婚":"hūn","魂":"hún","浑":"hún","混":"hùn","豁":"huō","活":"huó","伙":"huǒ","火":"huǒ","获":"huò","或":"huò","惑":"huò","霍":"huò","货":"huò","祸":"huò","击":"jī","圾":"jī","基":"jī","机":"jī","畸":"jī","稽":"jī","积":"jī","箕":"jī","肌":"jī","饥":"jī","迹":"jì","激":"jī","讥":"jī","鸡":"jī","姬":"jī","绩":"jì","缉":"jī","吉":"jí","极":"jí","棘":"jí","辑":"jí","籍":"jí","集":"jí","及":"jí","急":"jí","疾":"jí","汲":"jí","即":"jí","嫉":"jí","级":"jí","挤":"jǐ","几":"jǐ","脊":"jǐ","己":"jǐ","蓟":"jì","技":"jì","冀":"jì","季":"jì","伎":"jì","祭":"jì","剂":"jì","悸":"jì","济":"jì","寄":"jì","寂":"jì","计":"jì","记":"jì","既":"jì","忌":"jì","际":"jì","妓":"jì","继":"jì","纪":"jì","嘉":"jiā","枷":"jiā","夹":"jiā","佳":"jiā","家":"jiā","加":"jiā","荚":"jiá","颊":"jiá","贾":"jiǎ","甲":"jiǎ","钾":"jiǎ","假":"jiǎ","稼":"jià","价":"jià","架":"jià","驾":"jià","嫁":"jià","歼":"jiān","监":"jiān","坚":"jiān","尖":"jiān","笺":"jiān","间":"jiān","煎":"jiān","兼":"jiān","肩":"jiān","艰":"jiān","奸":"jiān","缄":"jiān","茧":"jiǎn","检":"jiǎn","柬":"jiǎn","碱":"jiǎn","硷":"jiǎn","拣":"jiǎn","捡":"jiǎn","简":"jiǎn","俭":"jiǎn","剪":"jiǎn","减":"jiǎn","荐":"jiàn","槛":"kǎn","鉴":"jiàn","践":"jiàn","贱":"jiàn","见":"jiàn","键":"jiàn","箭":"jiàn","件":"jiàn","健":"jiàn","舰":"jiàn","剑":"jiàn","饯":"jiàn","渐":"jiàn","溅":"jiàn","涧":"jiàn","建":"jiàn","僵":"jiāng","姜":"jiāng","将":"jiāng","浆":"jiāng","江":"jiāng","疆":"jiāng","蒋":"jiǎng","桨":"jiǎng","奖":"jiǎng","讲":"jiǎng","匠":"jiàng","酱":"jiàng","降":"jiàng","蕉":"jiāo","椒":"jiāo","礁":"jiāo","焦":"jiāo","胶":"jiāo","交":"jiāo","郊":"jiāo","浇":"jiāo","骄":"jiāo","娇":"jiāo","嚼":"jiáo","搅":"jiǎo","铰":"jiǎo","矫":"jiǎo","侥":"yáo","脚":"jiǎo","狡":"jiǎo","角":"jiǎo","饺":"jiǎo","缴":"jiǎo","绞":"jiǎo","剿":"jiǎo","教":"jiào","酵":"jiào","轿":"jiào","较":"jiào","叫":"jiào","窖":"jiào","揭":"jiē","接":"jiē","皆":"jiē","秸":"jiē","街":"jiē","阶":"jiē","截":"jié","劫":"jié","节":"jié","桔":"jú","杰":"jié","捷":"jié","睫":"jié","竭":"jié","洁":"jié","结":"jié","解":"jiě","姐":"jiě","戒":"jiè","藉":"jiè","芥":"jiè","界":"jiè","借":"jiè","介":"jiè","疥":"jiè","诫":"jiè","届":"jiè","巾":"jīn","筋":"jīn","斤":"jīn","金":"jīn","今":"jīn","津":"jīn","襟":"jīn","紧":"jǐn","锦":"jǐn","仅":"jǐn","谨":"jǐn","进":"jìn","靳":"jìn","晋":"jìn","禁":"jìn","近":"jìn","烬":"jìn","浸":"jìn","尽":"jìn","劲":"jìn","荆":"jīng","兢":"jīng","茎":"jīng","睛":"jīng","晶":"jīng","鲸":"jīng","京":"jīng","惊":"jīng","精":"jīng","粳":"jīng","经":"jīng","井":"jǐng","警":"jǐng","景":"jǐng","颈":"jǐng","静":"jìng","境":"jìng","敬":"jìng","镜":"jìng","径":"jìng","痉":"jìng","靖":"jìng","竟":"jìng","竞":"jìng","净":"jìng","炯":"jiǒng","窘":"jiǒng","揪":"jiū","究":"jiū","纠":"jiū","玖":"jiǔ","韭":"jiǔ","久":"jiǔ","灸":"jiǔ","九":"jiǔ","酒":"jiǔ","厩":"jiù","救":"jiù","旧":"jiù","臼":"jiù","舅":"jiù","咎":"jiù","就":"jiù","疚":"jiù","鞠":"jū","拘":"jū","狙":"jū","疽":"jū","居":"jū","驹":"jū","菊":"jú","局":"jú","咀":"jǔ","矩":"jǔ","举":"jǔ","沮":"jǔ","聚":"jù","拒":"jù","据":"jù","巨":"jù","具":"jù","距":"jù","踞":"jù","锯":"jù","俱":"jù","句":"jù","惧":"jù","炬":"jù","剧":"jù","捐":"juān","鹃":"juān","娟":"juān","倦":"juàn","眷":"juàn","卷":"juàn","绢":"juàn","撅":"juē","攫":"jué","抉":"jué","掘":"jué","倔":"juè","爵":"jué","觉":"jué","决":"jué","诀":"jué","绝":"jué","均":"jūn","菌":"jūn","钧":"jūn","军":"jūn","君":"jūn","峻":"jùn","俊":"jùn","竣":"jùn","浚":"xùn","郡":"jùn","骏":"jùn","喀":"kā","咖":"kā","卡":"kǎ","咯":"gē","开":"kāi","揩":"kāi","楷":"kǎi","凯":"kǎi","慨":"kǎi","刊":"kān","堪":"kān","勘":"kān","坎":"kǎn","砍":"kǎn","看":"kàn","康":"kāng","慷":"kāng","糠":"kāng","扛":"káng","抗":"kàng","亢":"kàng","炕":"kàng","考":"kǎo","拷":"kǎo","烤":"kǎo","靠":"kào","坷":"kě","苛":"kē","柯":"kē","棵":"kē","磕":"kē","颗":"kē","科":"kē","壳":"ké","咳":"ké","可":"kě","渴":"kě","克":"kè","刻":"kè","客":"kè","课":"kè","肯":"kěn","啃":"kěn","垦":"kěn","恳":"kěn","坑":"kēng","吭":"kēng","空":"kōng","恐":"kǒng","孔":"kǒng","控":"kòng","抠":"kōu","口":"kǒu","扣":"kòu","寇":"kòu","枯":"kū","哭":"kū","窟":"kū","苦":"kǔ","酷":"kù","库":"kù","裤":"kù","夸":"kuā","垮":"kuǎ","挎":"kuà","跨":"kuà","胯":"kuà","块":"kuài","筷":"kuài","侩":"kuài","快":"kuài","宽":"kuān","款":"kuǎn","匡":"kuāng","筐":"kuāng","狂":"kuáng","框":"kuàng","矿":"kuàng","眶":"kuàng","旷":"kuàng","况":"kuàng","亏":"kuī","盔":"kuī","岿":"kuī","窥":"kuī","葵":"kuí","奎":"kuí","魁":"kuí","傀":"guī","馈":"kuì","愧":"kuì","溃":"kuì","坤":"kūn","昆":"kūn","捆":"kǔn","困":"kùn","括":"kuò","扩":"kuò","廓":"kuò","阔":"kuò","垃":"lā","拉":"lā","喇":"lǎ","蜡":"là","腊":"là","辣":"là","啦":"lā","莱":"lái","来":"lái","赖":"lài","蓝":"lán","婪":"lán","栏":"lán","拦":"lán","篮":"lán","阑":"lán","兰":"lán","澜":"lán","谰":"lán","揽":"lǎn","览":"lǎn","懒":"lǎn","缆":"lǎn","烂":"làn","滥":"làn","琅":"láng","榔":"láng","狼":"láng","廊":"láng","郎":"láng","朗":"lǎng","浪":"làng","捞":"lāo","劳":"láo","牢":"láo","老":"lǎo","佬":"lǎo","姥":"lǎo","酪":"lào","烙":"lào","涝":"lào","勒":"lè","乐":"lè","雷":"léi","镭":"léi","蕾":"lěi","磊":"lěi","累":"lèi","儡":"lěi","垒":"lěi","擂":"lèi","肋":"lèi","类":"lèi","泪":"lèi","棱":"léng","楞":"léng","冷":"lěng","厘":"lí","梨":"lí","犁":"lí","黎":"lí","篱":"lí","狸":"lí","离":"lí","漓":"lí","理":"lǐ","李":"lǐ","里":"lǐ","鲤":"lǐ","礼":"lǐ","莉":"lì","荔":"lì","吏":"lì","栗":"lì","丽":"lì","厉":"lì","励":"lì","砾":"lì","历":"lì","利":"lì","傈":"lì","例":"lì","俐":"lì","痢":"lì","立":"lì","粒":"lì","沥":"lì","隶":"lì","力":"lì","璃":"lí","哩":"lǐ","俩":"liǎng","联":"lián","莲":"lián","连":"lián","镰":"lián","廉":"lián","怜":"lián","涟":"lián","帘":"lián","敛":"liǎn","脸":"liǎn","链":"liàn","恋":"liàn","炼":"liàn","练":"liàn","粮":"liáng","凉":"liáng","梁":"liáng","粱":"liáng","良":"liáng","两":"liǎng","辆":"liàng","量":"liàng","晾":"liàng","亮":"liàng","谅":"liàng","撩":"liáo","聊":"liáo","僚":"liáo","疗":"liáo","燎":"liáo","寥":"liáo","辽":"liáo","潦":"liáo","了":"le","撂":"liào","镣":"liào","廖":"liào","料":"liào","列":"liè","裂":"liè","烈":"liè","劣":"liè","猎":"liè","琳":"lín","林":"lín","磷":"lín","霖":"lín","临":"lín","邻":"lín","鳞":"lín","淋":"lín","凛":"lǐn","赁":"lìn","吝":"lìn","拎":"līn","玲":"líng","菱":"líng","零":"líng","龄":"líng","铃":"líng","伶":"líng","羚":"líng","凌":"líng","灵":"líng","陵":"líng","岭":"lǐng","领":"lǐng","另":"lìng","令":"lìng","溜":"liū","琉":"liú","榴":"liú","硫":"liú","馏":"liú","留":"liú","刘":"liú","瘤":"liú","流":"liú","柳":"liǔ","六":"liù","龙":"lóng","聋":"lóng","咙":"lóng","笼":"lóng","窿":"lóng","隆":"lóng","垄":"lǒng","拢":"lǒng","陇":"lǒng","楼":"lóu","娄":"lóu","搂":"lǒu","篓":"lǒu","漏":"lòu","陋":"lòu","芦":"lú","卢":"lú","颅":"lú","庐":"lú","炉":"lú","掳":"lǔ","卤":"lǔ","虏":"lǔ","鲁":"lǔ","麓":"lù","碌":"lù","露":"lù","路":"lù","赂":"lù","鹿":"lù","潞":"lù","禄":"lù","录":"lù","陆":"lù","戮":"lù","驴":"lǘ","吕":"lǚ","铝":"lǚ","侣":"lǚ","旅":"lǚ","履":"lǚ","屡":"lǚ","缕":"lǚ","虑":"lǜ","氯":"lǜ","律":"lǜ","率":"lǜ","滤":"lǜ","绿":"lǜ","峦":"luán","挛":"luán","孪":"luán","滦":"luán","卵":"luǎn","乱":"luàn","掠":"lüě","略":"lüè","抡":"lún","轮":"lún","伦":"lún","仑":"lún","沦":"lún","纶":"lún","论":"lùn","萝":"luó","螺":"luó","罗":"luó","逻":"luó","锣":"luó","箩":"luó","骡":"luó","裸":"luǒ","落":"luò","洛":"luò","骆":"luò","络":"luò","妈":"mā","麻":"má","玛":"mǎ","码":"mǎ","蚂":"mǎ","马":"mǎ","骂":"mà","嘛":"ma","吗":"ma","埋":"mái","买":"mǎi","麦":"mài","卖":"mài","迈":"mài","脉":"mài","瞒":"mán","馒":"mán","蛮":"mán","满":"mǎn","蔓":"màn","曼":"màn","慢":"màn","漫":"màn","谩":"màn","芒":"máng","茫":"máng","盲":"máng","氓":"máng","忙":"máng","莽":"mǎng","猫":"māo","茅":"máo","锚":"máo","毛":"máo","矛":"máo","铆":"mǎo","卯":"mǎo","茂":"mào","冒":"mào","帽":"mào","貌":"mào","贸":"mào","么":"me","玫":"méi","枚":"méi","梅":"méi","酶":"méi","霉":"méi","煤":"méi","没":"méi","眉":"méi","媒":"méi","镁":"měi","每":"měi","美":"měi","昧":"mèi","寐":"mèi","妹":"mèi","媚":"mèi","门":"mén","闷":"mèn","们":"mén","萌":"méng","蒙":"méng","檬":"méng","盟":"méng","锰":"měng","猛":"měng","梦":"mèng","孟":"mèng","眯":"mī","醚":"mí","靡":"mí","糜":"mí","迷":"mí","谜":"mí","弥":"mí","米":"mǐ","秘":"mì","觅":"mì","泌":"mì","蜜":"mì","密":"mì","幂":"mì","棉":"mián","眠":"mián","绵":"mián","冕":"miǎn","免":"miǎn","勉":"miǎn","娩":"miǎn","缅":"miǎn","面":"miàn","苗":"miáo","描":"miáo","瞄":"miáo","藐":"miǎo","秒":"miǎo","渺":"miǎo","庙":"miào","妙":"miào","蔑":"miè","灭":"miè","民":"mín","抿":"mǐn","皿":"mǐn","敏":"mǐn","悯":"mǐn","闽":"mǐn","明":"míng","螟":"míng","鸣":"míng","铭":"míng","名":"míng","命":"mìng","谬":"miù","摸":"mō","摹":"mó","蘑":"mó","模":"mó","膜":"mó","磨":"mó","摩":"mó","魔":"mó","抹":"mò","末":"mò","莫":"mò","墨":"mò","默":"mò","沫":"mò","漠":"mò","寞":"mò","陌":"mò","谋":"móu","牟":"móu","某":"mǒu","拇":"mǔ","牡":"mǔ","亩":"mǔ","姆":"mǔ","母":"mǔ","墓":"mù","暮":"mù","幕":"mù","募":"mù","慕":"mù","木":"mù","目":"mù","睦":"mù","牧":"mù","穆":"mù","拿":"ná","哪":"nǎ","呐":"nà","钠":"nà","那":"nà","娜":"nà","纳":"nà","氖":"nǎi","乃":"nǎi","奶":"nǎi","耐":"nai","奈":"nai","南":"nán","男":"nán","难":"nán","囊":"náng","挠":"náo","脑":"nǎo","恼":"nǎo","闹":"nào","淖":"nào","呢":"ne","馁":"něi","内":"nèi","嫩":"nèn","能":"néng","妮":"nī","霓":"ní","倪":"ní","泥":"ní","尼":"ní","拟":"nǐ","你":"nǐ","匿":"nì","腻":"nì","逆":"nì","溺":"nì","蔫":"niān","拈":"niān","年":"nián","碾":"niǎn","撵":"niǎn","捻":"niǎn","念":"niàn","娘":"niáng","酿":"niàng","鸟":"niǎo","尿":"niào","捏":"niē","聂":"niè","孽":"niè","啮":"niè","镊":"niè","镍":"niè","涅":"niè","您":"nín","柠":"níng","狞":"níng","凝":"níng","宁":"níng","拧":"nǐng","泞":"nìng","牛":"niú","扭":"niǔ","钮":"niǔ","纽":"niǔ","脓":"nóng","浓":"nóng","农":"nóng","弄":"nòng","奴":"nú","努":"nǔ","怒":"nù","女":"nǚ","暖":"nuǎn","虐":"nüè","疟":"nüè","挪":"nuó","懦":"nuò","糯":"nuò","诺":"nuò","哦":"ò","欧":"ōu","鸥":"ōu","殴":"ōu","藕":"ǒu","呕":"ǒu","偶":"ǒu","沤":"ōu","啪":"pā","趴":"pā","爬":"pá","帕":"pà","怕":"pà","琶":"pá","拍":"pāi","排":"pái","牌":"pái","徘":"pái","湃":"pai","派":"pai","攀":"pān","潘":"pān","盘":"pán","磐":"pán","盼":"pàn","畔":"pàn","判":"pàn","叛":"pàn","乓":"pāng","庞":"páng","旁":"páng","耪":"pǎng","胖":"pàng","抛":"pāo","咆":"páo","刨":"páo","炮":"pào","袍":"páo","跑":"pǎo","泡":"pào","呸":"pēi","胚":"pēi","培":"péi","裴":"péi","赔":"péi","陪":"péi","配":"pèi","佩":"pèi","沛":"pèi","喷":"pēn","盆":"pén","砰":"pēng","抨":"pēng","烹":"pēng","澎":"péng","彭":"péng","蓬":"péng","棚":"péng","硼":"péng","篷":"péng","膨":"péng","朋":"péng","鹏":"péng","捧":"pěng","碰":"pèng","坯":"pī","砒":"pī","霹":"pī","批":"pī","披":"pī","劈":"pī","琵":"pí","毗":"pí","啤":"pí","脾":"pí","疲":"pí","皮":"pí","匹":"pǐ","痞":"pǐ","僻":"pì","屁":"pì","譬":"pì","篇":"piān","偏":"piān","片":"piàn","骗":"piàn","飘":"piāo","漂":"piāo","瓢":"piáo","票":"piào","撇":"piě","瞥":"piē","拼":"pīn","频":"pín","贫":"pín","品":"pǐn","聘":"pìn","乒":"pīng","坪":"píng","苹":"píng","萍":"píng","平":"píng","凭":"píng","瓶":"píng","评":"píng","屏":"píng","坡":"pō","泼":"pō","颇":"pō","婆":"pó","破":"pò","魄":"pò","迫":"pò","粕":"pò","剖":"pōu","扑":"pū","铺":"pù","仆":"pú","莆":"pú","葡":"pú","菩":"pú","蒲":"pú","埔":"pǔ","朴":"pǔ","圃":"pǔ","普":"pǔ","浦":"pǔ","谱":"pǔ","曝":"pù","瀑":"bào","期":"qī","欺":"qī","栖":"qī","戚":"qī","妻":"qī","七":"qī","凄":"qī","漆":"qī","柒":"qī","沏":"qī","其":"qí","棋":"qí","奇":"qí","歧":"qí","畦":"qí","崎":"qí","脐":"qí","齐":"qí","旗":"qí","祈":"qí","祁":"qí","骑":"qí","起":"qǐ","岂":"qǐ","乞":"qǐ","企":"qǐ","启":"qǐ","契":"qì","砌":"qì","器":"qì","气":"qì","迄":"qì","弃":"qì","汽":"qì","泣":"qì","讫":"qì","掐":"qiā","恰":"qià","洽":"qià","牵":"qiān","扦":"qiān","钎":"qiān","铅":"qiān","千":"qiān","迁":"qiān","签":"qiān","仟":"qiān","谦":"qiān","乾":"qián","黔":"qián","钱":"qián","钳":"qián","前":"qián","潜":"qián","遣":"qiǎn","浅":"qiǎn","谴":"qiǎn","堑":"qiàn","嵌":"qiàn","欠":"qiàn","歉":"qiàn","枪":"qiāng","呛":"qiàng","腔":"qiāng","羌":"qiāng","墙":"qiáng","蔷":"qiáng","强":"qiáng","抢":"qiǎng","橇":"qiāo","锹":"qiāo","敲":"qiāo","悄":"qiāo","桥":"qiáo","瞧":"qiáo","乔":"qiáo","侨":"qiáo","巧":"qiǎo","鞘":"qiào","撬":"qiào","翘":"qiào","峭":"qiào","俏":"qiào","窍":"qiào","切":"qiē","茄":"qié","且":"qiě","怯":"qiè","窃":"qiè","钦":"qīn","侵":"qīn","亲":"qīn","秦":"qín","琴":"qín","勤":"qín","芹":"qín","擒":"qín","禽":"qín","寝":"qǐn","沁":"qìn","青":"qīng","轻":"qīng","氢":"qīng","倾":"qīng","卿":"qīng","清":"qīng","擎":"qíng","晴":"qíng","氰":"qíng","情":"qíng","顷":"qǐng","请":"qǐng","庆":"qìng","琼":"qióng","穷":"qióng","秋":"qiū","丘":"qiū","邱":"qiū","球":"qiú","求":"qiú","囚":"qiú","酋":"qiú","泅":"qiú","趋":"qū","区":"qū","蛆":"qū","曲":"qǔ","躯":"qū","屈":"qū","驱":"qū","渠":"qú","取":"qǔ","娶":"qǔ","龋":"qǔ","趣":"qù","去":"qù","圈":"quān","颧":"quán","权":"quán","醛":"quán","泉":"quán","全":"quán","痊":"quán","拳":"quán","犬":"quǎn","券":"quàn","劝":"quàn","缺":"quē","炔":"quē","瘸":"qué","却":"què","鹊":"què","榷":"què","确":"què","雀":"què","裙":"qún","群":"qún","然":"rán","燃":"rán","冉":"rǎn","染":"rǎn","瓤":"ráng","壤":"rǎng","攘":"rǎng","嚷":"rǎng","让":"ràng","饶":"ráo","扰":"rǎo","绕":"rào","惹":"rě","热":"rè","壬":"rén","仁":"rén","人":"rén","忍":"rěn","韧":"rèn","任":"rèn","认":"rèn","刃":"rèn","妊":"rèn","纫":"rèn","扔":"rēng","仍":"réng","日":"rì","戎":"róng","茸":"róng","蓉":"róng","荣":"róng","融":"róng","熔":"róng","溶":"róng","容":"róng","绒":"róng","冗":"rǒng","揉":"róu","柔":"róu","肉":"ròu","茹":"rú","蠕":"rú","儒":"rú","孺":"rú","如":"rú","辱":"rǔ","乳":"rǔ","汝":"rǔ","入":"rù","褥":"rù","软":"ruǎn","阮":"ruǎn","蕊":"ruǐ","瑞":"ruì","锐":"ruì","闰":"rùn","润":"rùn","若":"ruò","弱":"ruò","撒":"sā","洒":"sǎ","萨":"sà","腮":"sāi","鳃":"sāi","塞":"sāi","赛":"sài","三":"sān","叁":"sān","伞":"sǎn","散":"sàn","桑":"sāng","嗓":"sǎng","丧":"sàng","搔":"sāo","骚":"sāo","扫":"sǎo","嫂":"sǎo","瑟":"sè","色":"sè","涩":"sè","森":"sēn","僧":"sēng","莎":"shā","砂":"shā","杀":"shā","刹":"shā","沙":"shā","纱":"shā","傻":"shǎ","啥":"shá","煞":"shà","筛":"shāi","晒":"shai","珊":"shān","苫":"shān","杉":"shān","山":"shān","删":"shān","煽":"shān","衫":"shān","闪":"shǎn","陕":"shǎn","擅":"shàn","赡":"shàn","膳":"shàn","善":"shàn","汕":"shàn","扇":"shàn","缮":"shàn","墒":"shāng","伤":"shāng","商":"shāng","赏":"shǎng","晌":"shǎng","上":"shàng","尚":"shàng","裳":"shang","梢":"shāo","捎":"shāo","稍":"shāo","烧":"shāo","芍":"sháo","勺":"sháo","韶":"sháo","少":"shǎo","哨":"shào","邵":"shào","绍":"shào","奢":"shē","赊":"shē","蛇":"shé","舌":"shé","舍":"shě","赦":"shè","摄":"shè","射":"shè","慑":"shè","涉":"shè","社":"shè","设":"shè","砷":"shēn","申":"shēn","呻":"shēn","伸":"shēn","身":"shēn","深":"shēn","娠":"shēn","绅":"shēn","神":"shén","沈":"shěn","审":"shěn","婶":"shěn","甚":"shèn","肾":"shèn","慎":"shèn","渗":"shèn","声":"shēng","生":"shēng","甥":"shēng","牲":"shēng","升":"shēng","绳":"shéng","省":"shěng","盛":"shèng","剩":"shèng","胜":"shèng","圣":"shèng","师":"shī","失":"shī","狮":"shī","施":"shī","湿":"shī","诗":"shī","尸":"shī","虱":"shī","十":"shí","石":"shí","拾":"shí","时":"shí","什":"shí","食":"shí","蚀":"shí","实":"shí","识":"shí","史":"shǐ","矢":"shǐ","使":"shǐ","屎":"shǐ","驶":"shǐ","始":"shǐ","式":"shì","示":"shì","士":"shì","世":"shì","柿":"shì","事":"shì","拭":"shì","誓":"shì","逝":"shì","势":"shì","是":"shì","嗜":"shì","噬":"shì","适":"shì","仕":"shì","侍":"shì","释":"shì","饰":"shì","氏":"shì","市":"shì","恃":"shì","室":"shì","视":"shì","试":"shì","收":"shōu","手":"shǒu","首":"shǒu","守":"shǒu","寿":"shòu","授":"shòu","售":"shòu","受":"shòu","瘦":"shòu","兽":"shòu","蔬":"shū","枢":"shū","梳":"shū","殊":"shū","抒":"shū","输":"shū","叔":"shū","舒":"shū","淑":"shū","疏":"shū","书":"shū","赎":"shú","孰":"shú","熟":"shú","薯":"shǔ","暑":"shǔ","曙":"shǔ","署":"shǔ","蜀":"shǔ","黍":"shǔ","鼠":"shǔ","属":"shǔ","术":"shù","述":"shù","树":"shù","束":"shù","戍":"shù","竖":"shù","墅":"shù","庶":"shù","数":"shù","漱":"shù","恕":"shù","刷":"shuā","耍":"shuǎ","摔":"shuāi","衰":"shuāi","甩":"shuǎi","帅":"shuài","栓":"shuān","拴":"shuān","霜":"shuāng","双":"shuāng","爽":"shuǎng","谁":"shuí","水":"shuǐ","睡":"shuì","税":"shuì","吮":"shǔn","瞬":"shùn","顺":"shùn","舜":"shùn","说":"shuō","硕":"shuò","朔":"shuò","烁":"shuò","斯":"sī","撕":"sī","嘶":"sī","思":"sī","私":"sī","司":"sī","丝":"sī","死":"sǐ","肆":"sì","寺":"sì","嗣":"sì","四":"sì","伺":"sì","似":"sì","饲":"sì","巳":"sì","松":"sōng","耸":"sǒng","怂":"sǒng","颂":"sòng","送":"sòng","宋":"sòng","讼":"sòng","诵":"sòng","搜":"sōu","艘":"sōu","擞":"sǒu","嗽":"sòu","苏":"sū","酥":"sū","俗":"sú","素":"sù","速":"sù","粟":"sù","僳":"sù","塑":"sù","溯":"sù","宿":"xiǔ","诉":"sù","肃":"sù","酸":"suān","蒜":"suàn","算":"suàn","虽":"suī","隋":"suí","随":"suí","绥":"suí","髓":"suǐ","碎":"suì","岁":"suì","穗":"suì","遂":"suí","隧":"suì","祟":"suì","孙":"sūn","损":"sǔn","笋":"sǔn","蓑":"suō","梭":"suō","唆":"suō","缩":"suō","琐":"suǒ","索":"suǒ","锁":"suǒ","所":"suǒ","塌":"tā","他":"tā","它":"tā","她":"tā","塔":"tǎ","獭":"tǎ","挞":"tà","蹋":"tà","踏":"tà","胎":"tāi","苔":"tái","抬":"tái","台":"tái","泰":"tai","酞":"tai","太":"tai","态":"tai","汰":"tai","坍":"tān","摊":"tān","贪":"tān","瘫":"tān","滩":"tān","坛":"tán","檀":"tán","痰":"tán","潭":"tán","谭":"tán","谈":"tán","坦":"tǎn","毯":"tǎn","袒":"tǎn","碳":"tàn","探":"tàn","叹":"tàn","炭":"tàn","汤":"tāng","塘":"táng","搪":"táng","堂":"táng","棠":"táng","膛":"táng","唐":"táng","糖":"táng","倘":"tǎng","躺":"tǎng","淌":"tǎng","趟":"tàng","烫":"tàng","掏":"tāo","涛":"tāo","滔":"tāo","绦":"tāo","萄":"táo","桃":"táo","逃":"táo","淘":"táo","陶":"táo","讨":"tǎo","套":"tào","特":"tè","藤":"téng","腾":"téng","疼":"téng","誊":"téng","梯":"tī","剔":"tī","踢":"tī","锑":"tī","提":"tí","题":"tí","蹄":"tí","啼":"tí","体":"tǐ","替":"tì","嚏":"tì","惕":"tì","涕":"tì","剃":"tì","屉":"tì","天":"tiān","添":"tiān","填":"tián","田":"tián","甜":"tián","恬":"tián","舔":"tiǎn","腆":"tiǎn","挑":"tiāo","条":"tiáo","迢":"tiáo","眺":"tiào","跳":"tiào","贴":"tiē","铁":"tiě","帖":"tiē","厅":"tīng","听":"tīng","烃":"tīng","汀":"tīng","廷":"tíng","停":"tíng","亭":"tíng","庭":"tíng","挺":"tǐng","艇":"tǐng","通":"tōng","桐":"tóng","酮":"tóng","瞳":"tóng","同":"tóng","铜":"tóng","彤":"tóng","童":"tóng","桶":"tǒng","捅":"tǒng","筒":"tǒng","统":"tǒng","痛":"tòng","偷":"tōu","投":"tóu","头":"tóu","透":"tòu","凸":"tū","秃":"tū","突":"tū","图":"tú","徒":"tú","途":"tú","涂":"tú","屠":"tú","土":"tǔ","吐":"tǔ","兔":"tù","湍":"tuān","团":"tuán","推":"tuī","颓":"tuí","腿":"tuǐ","蜕":"tuì","褪":"tuì","退":"tuì","吞":"tūn","屯":"tún","臀":"tún","拖":"tuō","托":"tuō","脱":"tuō","鸵":"tuó","陀":"tuó","驮":"tuó","驼":"tuó","椭":"tuǒ","妥":"tuǒ","拓":"tuò","唾":"tuò","挖":"wā","哇":"wa","蛙":"wā","洼":"wā","娃":"wá","瓦":"wǎ","袜":"wà","歪":"wāi","外":"wai","豌":"wān","弯":"wān","湾":"wān","玩":"wán","顽":"wán","丸":"wán","烷":"wán","完":"wán","碗":"wǎn","挽":"wǎn","晚":"wǎn","皖":"wǎn","惋":"wǎn","宛":"wǎn","婉":"wǎn","万":"wàn","腕":"wàn","汪":"wāng","王":"wáng","亡":"wáng","枉":"wǎng","网":"wǎng","往":"wǎng","旺":"wàng","望":"wàng","忘":"wàng","妄":"wàng","威":"wēi","巍":"wēi","微":"wēi","危":"wēi","韦":"wéi","违":"wéi","桅":"wéi","围":"wéi","唯":"wéi","惟":"wéi","为":"wéi","潍":"wéi","维":"wéi","苇":"wěi","萎":"wěi","委":"wěi","伟":"wěi","伪":"wěi","尾":"wěi","纬":"wěi","未":"wèi","蔚":"wèi","味":"wèi","畏":"wèi","胃":"wèi","喂":"wèi","魏":"wèi","位":"wèi","渭":"wèi","谓":"wèi","尉":"wèi","慰":"wèi","卫":"wèi","瘟":"wēn","温":"wēn","蚊":"wén","文":"wén","闻":"wén","纹":"wén","吻":"wěn","稳":"wěn","紊":"wěn","问":"wèn","嗡":"wēng","翁":"wēng","瓮":"wèng","挝":"wō","蜗":"wō","涡":"wō","窝":"wō","我":"wǒ","斡":"wò","卧":"wò","握":"wò","沃":"wò","巫":"wū","呜":"wū","钨":"wū","乌":"wū","污":"wū","诬":"wū","屋":"wū","无":"wú","芜":"wú","梧":"wú","吾":"wú","吴":"wú","毋":"wú","武":"wǔ","五":"wǔ","捂":"wǔ","午":"wǔ","舞":"wǔ","伍":"wǔ","侮":"wǔ","坞":"wù","戊":"wù","雾":"wù","晤":"wù","物":"wù","勿":"wù","务":"wù","悟":"wù","误":"wù","昔":"xī","熙":"xī","析":"xī","西":"xī","硒":"xī","矽":"xī","晰":"xī","嘻":"xī","吸":"xī","锡":"xī","牺":"xī","稀":"xī","息":"xī","希":"xī","悉":"xī","膝":"xī","夕":"xī","惜":"xī","熄":"xī","烯":"xī","溪":"xī","汐":"xī","犀":"xī","檄":"xí","袭":"xí","席":"xí","习":"xí","媳":"xí","喜":"xǐ","铣":"xǐ","洗":"xǐ","系":"xì","隙":"xì","戏":"xì","细":"xì","瞎":"xiā","虾":"xiā","匣":"xiá","霞":"xiá","辖":"xiá","暇":"xiá","峡":"xiá","侠":"xiá","狭":"xiá","下":"xià","厦":"shà","夏":"xià","吓":"xià","掀":"xiān","锨":"xiān","先":"xiān","仙":"xiān","鲜":"xiān","纤":"xiān","咸":"xián","贤":"xián","衔":"xián","舷":"xián","闲":"xián","涎":"xián","弦":"xián","嫌":"xián","显":"xiǎn","险":"xiǎn","现":"xiàn","献":"xiàn","县":"xiàn","腺":"xiàn","馅":"xiàn","羡":"xiàn","宪":"xiàn","陷":"xiàn","限":"xiàn","线":"xiàn","相":"xiāng","厢":"xiāng","镶":"xiāng","香":"xiāng","箱":"xiāng","襄":"xiāng","湘":"xiāng","乡":"xiāng","翔":"xiáng","祥":"xiáng","详":"xiáng","想":"xiǎng","响":"xiǎng","享":"xiǎng","项":"xiàng","巷":"xiàng","橡":"xiàng","像":"xiàng","向":"xiàng","象":"xiàng","萧":"xiāo","硝":"xiāo","霄":"xiāo","削":"xuē","哮":"xiāo","嚣":"xiāo","销":"xiāo","消":"xiāo","宵":"xiāo","淆":"xiáo","晓":"xiǎo","小":"xiǎo","孝":"xiào","校":"xiào","肖":"xiāo","啸":"xiào","笑":"xiào","效":"xiào","楔":"xiē","些":"xiē","歇":"xiē","蝎":"xiē","鞋":"xié","协":"xié","挟":"xiā","携":"xié","邪":"xié","斜":"xié","胁":"xié","谐":"xié","写":"xiě","械":"xiè","卸":"xiè","蟹":"xiè","懈":"xiè","泄":"xiè","泻":"xiè","谢":"xiè","屑":"xiè","薪":"xīn","芯":"xīn","锌":"xīn","欣":"xīn","辛":"xīn","新":"xīn","忻":"xīn","心":"xīn","信":"xìn","衅":"xìn","星":"xīng","腥":"xīng","猩":"xīng","惺":"xīng","兴":"xīng","刑":"xíng","型":"xíng","形":"xíng","邢":"xíng","行":"xíng","醒":"xǐng","幸":"xìng","杏":"xìng","性":"xìng","姓":"xìng","兄":"xiōng","凶":"xiōng","胸":"xiōng","匈":"xiōng","汹":"xiōng","雄":"xióng","熊":"xióng","休":"xiū","修":"xiū","羞":"xiū","朽":"xiǔ","嗅":"xiù","锈":"xiù","秀":"xiù","袖":"xiù","绣":"xiù","墟":"xū","戌":"xū","需":"xū","虚":"xū","嘘":"xū","须":"xū","徐":"xú","许":"xǔ","蓄":"xù","酗":"xù","叙":"xù","旭":"xù","序":"xù","畜":"chù","恤":"xù","絮":"xù","婿":"xù","绪":"xù","续":"xù","轩":"xuān","喧":"xuān","宣":"xuān","悬":"xuán","旋":"xuán","玄":"xuán","选":"xuǎn","癣":"xuǎn","眩":"xuàn","绚":"xuàn","靴":"xuē","薛":"xuē","学":"xué","穴":"xué","雪":"xuě","血":"xuè","勋":"xūn","熏":"xūn","循":"xún","旬":"xún","询":"xún","寻":"xún","驯":"xùn","巡":"xún","殉":"xùn","汛":"xùn","训":"xùn","讯":"xùn","逊":"xùn","迅":"xùn","压":"yā","押":"yā","鸦":"yā","鸭":"yā","呀":"ya","丫":"yā","芽":"yá","牙":"yá","蚜":"yá","崖":"yá","衙":"yá","涯":"yá","雅":"yǎ","哑":"yǎ","亚":"yà","讶":"yà","焉":"yān","咽":"yān","阉":"yān","烟":"yān","淹":"yān","盐":"yán","严":"yán","研":"yán","蜒":"yán","岩":"yán","延":"yán","言":"yán","颜":"yán","阎":"yán","炎":"yán","沿":"yán","奄":"yǎn","掩":"yǎn","眼":"yǎn","衍":"yǎn","演":"yǎn","艳":"yàn","堰":"yàn","燕":"yàn","厌":"yàn","砚":"yàn","雁":"yàn","唁":"yàn","彦":"yàn","焰":"yàn","宴":"yàn","谚":"yàn","验":"yàn","殃":"yāng","央":"yāng","鸯":"yāng","秧":"yāng","杨":"yáng","扬":"yáng","佯":"yáng","疡":"yáng","羊":"yáng","洋":"yáng","阳":"yáng","氧":"yǎng","仰":"yǎng","痒":"yǎng","养":"yǎng","样":"yàng","漾":"yàng","邀":"yāo","腰":"yāo","妖":"yāo","瑶":"yáo","摇":"yáo","尧":"yáo","遥":"yáo","窑":"yáo","谣":"yáo","姚":"yáo","咬":"yǎo","舀":"yǎo","药":"yào","要":"yào","耀":"yào","椰":"yē","噎":"yē","耶":"yē","爷":"yé","野":"yě","冶":"yě","也":"yě","页":"yè","掖":"yè","业":"yè","叶":"yè","曳":"yè","腋":"yè","夜":"yè","液":"yè","一":"yī","壹":"yī","医":"yī","揖":"yī","铱":"yī","依":"yī","伊":"yī","衣":"yī","颐":"yí","夷":"yí","遗":"yí","移":"yí","仪":"yí","胰":"yí","疑":"yí","沂":"yí","宜":"yí","姨":"yí","彝":"yí","椅":"yǐ","蚁":"yǐ","倚":"yǐ","已":"yǐ","乙":"yǐ","矣":"yǐ","以":"yǐ","艺":"yì","抑":"yì","易":"yì","邑":"yì","屹":"yì","亿":"yì","役":"yì","臆":"yì","逸":"yì","肄":"yì","疫":"yì","亦":"yì","裔":"yì","意":"yì","毅":"yì","忆":"yì","义":"yì","益":"yì","溢":"yì","诣":"yì","议":"yì","谊":"yì","译":"yì","异":"yì","翼":"yì","翌":"yì","绎":"yì","茵":"yīn","荫":"yīn","因":"yīn","殷":"yīn","音":"yīn","阴":"yīn","姻":"yīn","吟":"yín","银":"yín","淫":"yín","寅":"yín","饮":"yǐn","尹":"yǐn","引":"yǐn","隐":"yǐn","印":"yìn","英":"yīng","樱":"yīng","婴":"yīng","鹰":"yīng","应":"yīng","缨":"yīng","莹":"yíng","萤":"yíng","营":"yíng","荧":"yíng","蝇":"yíng","迎":"yíng","赢":"yíng","盈":"yíng","影":"yǐng","颖":"yǐng","硬":"yìng","映":"yìng","哟":"yō","拥":"yōng","佣":"yòng","臃":"yōng","痈":"yōng","庸":"yōng","雍":"yōng","踊":"yǒng","蛹":"yǒng","咏":"yǒng","泳":"yǒng","涌":"yǒng","永":"yǒng","恿":"yǒng","勇":"yǒng","用":"yòng","幽":"yōu","优":"yōu","悠":"yōu","忧":"yōu","尤":"yóu","由":"yóu","邮":"yóu","铀":"yóu","犹":"yóu","油":"yóu","游":"yóu","酉":"yǒu","有":"yǒu","友":"yǒu","右":"yòu","佑":"yòu","釉":"yòu","诱":"yòu","又":"yòu","幼":"yòu","迂":"yū","淤":"yū","于":"yú","盂":"yú","榆":"yú","虞":"yú","愚":"yú","舆":"yú","余":"yú","俞":"yú","逾":"yú","鱼":"yú","愉":"yú","渝":"yú","渔":"yú","隅":"yú","予":"yǔ","娱":"yú","雨":"yǔ","与":"yǔ","屿":"yǔ","禹":"yǔ","宇":"yǔ","语":"yǔ","羽":"yǔ","玉":"yù","域":"yù","芋":"yù","郁":"yù","吁":"yù","遇":"yù","喻":"yù","峪":"yù","御":"yù","愈":"yù","欲":"yù","狱":"yù","育":"yù","誉":"yù","浴":"yù","寓":"yù","裕":"yù","预":"yù","豫":"yù","驭":"yù","鸳":"yuān","渊":"yuān","冤":"yuān","元":"yuán","垣":"yuán","袁":"yuán","原":"yuán","援":"yuán","辕":"yuán","园":"yuán","员":"yuán","圆":"yuán","猿":"yuán","源":"yuán","缘":"yuán","远":"yuǎn","苑":"yuàn","愿":"yuàn","怨":"yuàn","院":"yuàn","曰":"yuē","约":"yuē","越":"yuè","跃":"yuè","钥":"yào","岳":"yuè","粤":"yuè","月":"yuè","悦":"yuè","阅":"yuè","耘":"yún","云":"yún","郧":"yún","匀":"yún","陨":"yǔn","允":"yǔn","运":"yùn","蕴":"yùn","酝":"yùn","晕":"yūn","韵":"yùn","孕":"yùn","匝":"zā","砸":"zá","杂":"zá","栽":"zāi","哉":"zāi","灾":"zāi","宰":"zǎi","载":"zǎi","再":"zài","在":"zài","咱":"zán","攒":"zǎn","暂":"zàn","赞":"zàn","赃":"zāng","脏":"zāng","葬":"zàng","遭":"zāo","糟":"zāo","凿":"záo","藻":"zǎo","枣":"zǎo","早":"zǎo","澡":"zǎo","蚤":"zǎo","躁":"zào","噪":"zào","造":"zào","皂":"zào","灶":"zào","燥":"zào","责":"zé","择":"zé","则":"zé","泽":"zé","贼":"zéi","怎":"zěn","增":"zēng","憎":"zēng","曾":"céng","赠":"zèng","扎":"zhā","喳":"zhā","渣":"zhā","札":"zhá","轧":"zhá","铡":"zhá","闸":"zhá","眨":"zhǎ","栅":"shān","榨":"zhà","咋":"zǎ","乍":"zhà","炸":"zhà","诈":"zhà","摘":"zhāi","斋":"zhāi","宅":"zhái","窄":"zhǎi","债":"zhai","寨":"zhai","瞻":"zhān","毡":"zhān","詹":"zhān","粘":"zhān","沾":"zhān","盏":"zhǎn","斩":"zhǎn","辗":"niǎn","崭":"zhǎn","展":"zhǎn","蘸":"zhàn","栈":"zhàn","占":"zhàn","战":"zhàn","站":"zhàn","湛":"zhàn","绽":"zhàn","樟":"zhāng","章":"zhāng","彰":"zhāng","漳":"zhāng","张":"zhāng","掌":"zhǎng","涨":"zhǎng","杖":"zhàng","丈":"zhàng","帐":"zhàng","账":"zhàng","仗":"zhàng","胀":"zhàng","瘴":"zhàng","障":"zhàng","招":"zhāo","昭":"zhāo","找":"zhǎo","沼":"zhǎo","赵":"zhào","照":"zhào","罩":"zhào","兆":"zhào","肇":"zhào","召":"zhào","遮":"zhē","折":"shé","哲":"zhé","蛰":"zhé","辙":"zhé","者":"zhě","锗":"zhě","蔗":"zhè","这":"zhè","浙":"zhè","珍":"zhēn","斟":"zhēn","真":"zhēn","甄":"zhēn","砧":"zhēn","臻":"zhēn","贞":"zhēn","针":"zhēn","侦":"zhēn","枕":"zhěn","疹":"zhěn","诊":"zhěn","震":"zhèn","振":"zhèn","镇":"zhèn","阵":"zhèn","蒸":"zhēng","挣":"zhèng","睁":"zhēng","征":"zhēng","狰":"zhēng","争":"zhēng","怔":"zhēng","整":"zhěng","拯":"zhěng","正":"zhèng","政":"zhèng","帧":"zhēn","症":"zhèng","郑":"zhèng","证":"zhèng","芝":"zhī","枝":"zhī","支":"zhī","吱":"zī","蜘":"zhī","知":"zhī","肢":"zhī","脂":"zhī","汁":"zhī","之":"zhī","织":"zhī","职":"zhí","直":"zhí","植":"zhí","殖":"zhí","执":"zhí","值":"zhí","侄":"zhí","址":"zhǐ","指":"zhǐ","止":"zhǐ","趾":"zhǐ","只":"zhī","旨":"zhǐ","纸":"zhǐ","志":"zhì","挚":"zhì","掷":"zhì","至":"zhì","致":"zhì","置":"zhì","帜":"zhì","峙":"zhì","制":"zhì","智":"zhì","秩":"zhì","稚":"zhì","质":"zhì","炙":"zhì","痔":"zhì","滞":"zhì","治":"zhì","窒":"zhì","中":"zhōng","盅":"zhōng","忠":"zhōng","钟":"zhōng","衷":"zhōng","终":"zhōng","种":"zhǒng","肿":"zhǒng","重":"zhòng","仲":"zhòng","众":"zhòng","舟":"zhōu","周":"zhōu","州":"zhōu","洲":"zhōu","诌":"zhōu","粥":"zhōu","轴":"zhóu","肘":"zhǒu","帚":"zhǒu","咒":"zhòu","皱":"zhòu","宙":"zhòu","昼":"zhòu","骤":"zhòu","珠":"zhū","株":"zhū","蛛":"zhū","朱":"zhū","猪":"zhū","诸":"zhū","诛":"zhū","逐":"zhú","竹":"zhú","烛":"zhú","煮":"zhǔ","拄":"zhǔ","瞩":"zhǔ","嘱":"zhǔ","主":"zhǔ","著":"zhù","柱":"zhù","助":"zhù","蛀":"zhù","贮":"zhù","铸":"zhù","筑":"zhù","住":"zhù","注":"zhù","祝":"zhù","驻":"zhù","抓":"zhuā","爪":"zhǎo","拽":"zhuài","专":"zhuān","砖":"zhuān","转":"zhuǎn","撰":"zhuàn","赚":"zuàn","篆":"zhuàn","桩":"zhuāng","庄":"zhuāng","装":"zhuāng","妆":"zhuāng","撞":"zhuàng","壮":"zhuàng","状":"zhuàng","椎":"zhuī","锥":"zhuī","追":"zhuī","赘":"zhuì","坠":"zhuì","缀":"zhuì","谆":"zhūn","准":"zhǔn","捉":"zhuō","拙":"zhuō","卓":"zhuó","桌":"zhuō","琢":"zhuó","茁":"zhuó","酌":"zhuó","啄":"zhuó","着":"zhe","灼":"zhuó","浊":"zhuó","兹":"zī","咨":"zī","资":"zī","姿":"zī","滋":"zī","淄":"zī","孜":"zī","紫":"zǐ","仔":"zǎi","籽":"zǐ","滓":"zǐ","子":"zǐ","自":"zì","渍":"zì","字":"zì","鬃":"zōng","棕":"zōng","踪":"zōng","宗":"zōng","综":"zōng","总":"zǒng","纵":"zòng","邹":"zōu","走":"zǒu","奏":"zòu","揍":"zòu","租":"zū","足":"zú","卒":"zú","族":"zú","祖":"zǔ","诅":"zǔ","阻":"zǔ","组":"zǔ","钻":"zuàn","纂":"zuǎn","嘴":"zuǐ","醉":"zuì","最":"zuì","罪":"zuì","尊":"zūn","遵":"zūn","昨":"zuó","左":"zuǒ","佐":"zuǒ","柞":"zhà","做":"zuò","作":"zuò","坐":"zuò","座":"zuò"}
},function(n,t){"use strict";t.__esModule=!0,t.fanti=void 0,t.fanti={"锕":"錒","皑":"皚","蔼":"藹","碍":"礙","爱":"愛","嗳":"噯","嫒":"嬡","瑷":"璦","暧":"曖","霭":"靄","谙":"諳","铵":"銨","鹌":"鵪","肮":"骯","袄":"襖","奥":"奧","媪":"媼","骜":"驁","鳌":"鰲","坝":"壩","罢":"罷","钯":"鈀","摆":"擺","败":"敗","呗":"唄","颁":"頒","办":"辦","绊":"絆","钣":"鈑","帮":"幫","绑":"綁","镑":"鎊","谤":"謗","剥":"剝","饱":"飽","宝":"寶","报":"報","鲍":"鮑","鸨":"鴇","龅":"齙","辈":"輩","贝":"貝","钡":"鋇","狈":"狽","备":"備","惫":"憊","鹎":"鵯","贲":"賁","锛":"錛","绷":"繃","笔":"筆","毕":"畢","毙":"斃","币":"幣","闭":"閉","荜":"蓽","哔":"嗶","滗":"潷","铋":"鉍","筚":"篳","跸":"蹕","边":"邊","编":"編","贬":"貶","变":"變","辩":"辯","辫":"辮","苄":"芐","缏":"緶","笾":"籩","标":"標","骠":"驃","飑":"颮","飙":"飆","镖":"鏢","镳":"鑣","鳔":"鰾","鳖":"鱉","别":"別","瘪":"癟","濒":"瀕","滨":"濱","宾":"賓","摈":"擯","傧":"儐","缤":"繽","槟":"檳","殡":"殯","膑":"臏","镔":"鑌","髌":"髕","鬓":"鬢","饼":"餅","禀":"稟","拨":"撥","钵":"缽","铂":"鉑","驳":"駁","饽":"餑","钹":"鈸","鹁":"鵓","补":"補","钸":"鈽","财":"財","参":"參","蚕":"蠶","残":"殘","惭":"慚","惨":"慘","灿":"燦","骖":"驂","黪":"黲","苍":"蒼","舱":"艙","仓":"倉","沧":"滄","厕":"廁","侧":"側","册":"冊","测":"測","恻":"惻","层":"層","诧":"詫","锸":"鍤","侪":"儕","钗":"釵","搀":"攙","掺":"摻","蝉":"蟬","馋":"饞","谗":"讒","缠":"纏","铲":"鏟","产":"產","阐":"闡","颤":"顫","冁":"囅","谄":"諂","谶":"讖","蒇":"蕆","忏":"懺","婵":"嬋","骣":"驏","觇":"覘","禅":"禪","镡":"鐔","场":"場","尝":"嘗","长":"長","偿":"償","肠":"腸","厂":"廠","畅":"暢","伥":"倀","苌":"萇","怅":"悵","阊":"閶","鲳":"鯧","钞":"鈔","车":"車","彻":"徹","砗":"硨","尘":"塵","陈":"陳","衬":"襯","伧":"傖","谌":"諶","榇":"櫬","碜":"磣","龀":"齔","撑":"撐","称":"稱","惩":"懲","诚":"誠","骋":"騁","枨":"棖","柽":"檉","铖":"鋮","铛":"鐺","痴":"癡","迟":"遲","驰":"馳","耻":"恥","齿":"齒","炽":"熾","饬":"飭","鸱":"鴟","冲":"衝","虫":"蟲","宠":"寵","铳":"銃","畴":"疇","踌":"躊","筹":"籌","绸":"綢","俦":"儔","帱":"幬","雠":"讎","橱":"櫥","厨":"廚","锄":"鋤","雏":"雛","础":"礎","储":"儲","触":"觸","处":"處","刍":"芻","绌":"絀","蹰":"躕","传":"傳","钏":"釧","疮":"瘡","闯":"闖","创":"創","怆":"愴","锤":"錘","缍":"綞","纯":"純","鹑":"鶉","绰":"綽","辍":"輟","龊":"齪","辞":"辭","词":"詞","赐":"賜","鹚":"鶿","聪":"聰","葱":"蔥","囱":"囪","从":"從","丛":"叢","苁":"蓯","骢":"驄","枞":"樅","凑":"湊","辏":"輳","蹿":"躥","窜":"竄","撺":"攛","错":"錯","锉":"銼","鹾":"鹺","达":"達","哒":"噠","鞑":"韃","带":"帶","贷":"貸","骀":"駘","绐":"紿","担":"擔","单":"單","郸":"鄲","掸":"撣","胆":"膽","惮":"憚","诞":"誕","弹":"彈","殚":"殫","赕":"賧","瘅":"癉","箪":"簞","当":"當","挡":"擋","党":"黨","荡":"蕩","档":"檔","谠":"讜","砀":"碭","裆":"襠","捣":"搗","岛":"島","祷":"禱","导":"導","盗":"盜","焘":"燾","灯":"燈","邓":"鄧","镫":"鐙","敌":"敵","涤":"滌","递":"遞","缔":"締","籴":"糴","诋":"詆","谛":"諦","绨":"綈","觌":"覿","镝":"鏑","颠":"顛","点":"點","垫":"墊","电":"電","巅":"巔","钿":"鈿","癫":"癲","钓":"釣","调":"調","铫":"銚","鲷":"鯛","谍":"諜","叠":"疊","鲽":"鰈","钉":"釘","顶":"頂","锭":"錠","订":"訂","铤":"鋌","丢":"丟","铥":"銩","东":"東","动":"動","栋":"棟","冻":"凍","岽":"崠","鸫":"鶇","窦":"竇","犊":"犢","独":"獨","读":"讀","赌":"賭","镀":"鍍","渎":"瀆","椟":"櫝","牍":"牘","笃":"篤","黩":"黷","锻":"鍛","断":"斷","缎":"緞","簖":"籪","兑":"兌","队":"隊","对":"對","怼":"懟","镦":"鐓","吨":"噸","顿":"頓","钝":"鈍","炖":"燉","趸":"躉","夺":"奪","堕":"墮","铎":"鐸","鹅":"鵝","额":"額","讹":"訛","恶":"惡","饿":"餓","谔":"諤","垩":"堊","阏":"閼","轭":"軛","锇":"鋨","锷":"鍔","鹗":"鶚","颚":"顎","颛":"顓","鳄":"鱷","诶":"誒","儿":"兒","尔":"爾","饵":"餌","贰":"貳","迩":"邇","铒":"鉺","鸸":"鴯","鲕":"鮞","发":"發","罚":"罰","阀":"閥","珐":"琺","矾":"礬","钒":"釩","烦":"煩","贩":"販","饭":"飯","访":"訪","纺":"紡","钫":"鈁","鲂":"魴","飞":"飛","诽":"誹","废":"廢","费":"費","绯":"緋","镄":"鐨","鲱":"鯡","纷":"紛","坟":"墳","奋":"奮","愤":"憤","粪":"糞","偾":"僨","丰":"豐","枫":"楓","锋":"鋒","风":"風","疯":"瘋","冯":"馮","缝":"縫","讽":"諷","凤":"鳳","沣":"灃","肤":"膚","辐":"輻","抚":"撫","辅":"輔","赋":"賦","复":"復","负":"負","讣":"訃","妇":"婦","缚":"縛","凫":"鳧","驸":"駙","绂":"紱","绋":"紼","赙":"賻","麸":"麩","鲋":"鮒","鳆":"鰒","钆":"釓","该":"該","钙":"鈣","盖":"蓋","赅":"賅","杆":"桿","赶":"趕","秆":"稈","赣":"贛","尴":"尷","擀":"搟","绀":"紺","冈":"岡","刚":"剛","钢":"鋼","纲":"綱","岗":"崗","戆":"戇","镐":"鎬","睾":"睪","诰":"誥","缟":"縞","锆":"鋯","搁":"擱","鸽":"鴿","阁":"閣","铬":"鉻","个":"個","纥":"紇","镉":"鎘","颍":"潁","给":"給","亘":"亙","赓":"賡","绠":"綆","鲠":"鯁","龚":"龔","宫":"宮","巩":"鞏","贡":"貢","钩":"鉤","沟":"溝","苟":"茍","构":"構","购":"購","够":"夠","诟":"詬","缑":"緱","觏":"覯","蛊":"蠱","顾":"顧","诂":"詁","毂":"轂","钴":"鈷","锢":"錮","鸪":"鴣","鹄":"鵠","鹘":"鶻","剐":"剮","挂":"掛","鸹":"鴰","掴":"摑","关":"關","观":"觀","馆":"館","惯":"慣","贯":"貫","诖":"詿","掼":"摜","鹳":"鸛","鳏":"鰥","广":"廣","犷":"獷","规":"規","归":"歸","龟":"龜","闺":"閨","轨":"軌","诡":"詭","贵":"貴","刽":"劊","匦":"匭","刿":"劌","妫":"媯","桧":"檜","鲑":"鮭","鳜":"鱖","辊":"輥","滚":"滾","衮":"袞","绲":"緄","鲧":"鯀","锅":"鍋","国":"國","过":"過","埚":"堝","呙":"咼","帼":"幗","椁":"槨","蝈":"蟈","铪":"鉿","骇":"駭","韩":"韓","汉":"漢","阚":"闞","绗":"絎","颉":"頡","号":"號","灏":"灝","颢":"顥","阂":"閡","鹤":"鶴","贺":"賀","诃":"訶","阖":"闔","蛎":"蠣","横":"橫","轰":"轟","鸿":"鴻","红":"紅","黉":"黌","讧":"訌","荭":"葒","闳":"閎","鲎":"鱟","壶":"壺","护":"護","沪":"滬","户":"戶","浒":"滸","鹕":"鶘","哗":"嘩","华":"華","画":"畫","划":"劃","话":"話","骅":"驊","桦":"樺","铧":"鏵","怀":"懷","坏":"壞","欢":"歡","环":"環","还":"還","缓":"緩","换":"換","唤":"喚","痪":"瘓","焕":"煥","涣":"渙","奂":"奐","缳":"繯","锾":"鍰","鲩":"鯇","黄":"黃","谎":"謊","鳇":"鰉","挥":"揮","辉":"輝","毁":"毀","贿":"賄","秽":"穢","会":"會","烩":"燴","汇":"匯","讳":"諱","诲":"誨","绘":"繪","诙":"詼","荟":"薈","哕":"噦","浍":"澮","缋":"繢","珲":"琿","晖":"暉","荤":"葷","浑":"渾","诨":"諢","馄":"餛","阍":"閽","获":"獲","货":"貨","祸":"禍","钬":"鈥","镬":"鑊","击":"擊","机":"機","积":"積","饥":"饑","迹":"跡","讥":"譏","鸡":"雞","绩":"績","缉":"緝","极":"極","辑":"輯","级":"級","挤":"擠","几":"幾","蓟":"薊","剂":"劑","济":"濟","计":"計","记":"記","际":"際","继":"繼","纪":"紀","讦":"訐","诘":"詰","荠":"薺","叽":"嘰","哜":"嚌","骥":"驥","玑":"璣","觊":"覬","齑":"齏","矶":"磯","羁":"羈","虿":"蠆","跻":"躋","霁":"霽","鲚":"鱭","鲫":"鯽","夹":"夾","荚":"莢","颊":"頰","贾":"賈","钾":"鉀","价":"價","驾":"駕","郏":"郟","浃":"浹","铗":"鋏","镓":"鎵","蛲":"蟯","歼":"殲","监":"監","坚":"堅","笺":"箋","间":"間","艰":"艱","缄":"緘","茧":"繭","检":"檢","碱":"堿","硷":"鹼","拣":"揀","捡":"撿","简":"簡","俭":"儉","减":"減","荐":"薦","槛":"檻","鉴":"鑒","践":"踐","贱":"賤","见":"見","键":"鍵","舰":"艦","剑":"劍","饯":"餞","渐":"漸","溅":"濺","涧":"澗","谏":"諫","缣":"縑","戋":"戔","戬":"戩","睑":"瞼","鹣":"鶼","笕":"筧","鲣":"鰹","鞯":"韉","将":"將","浆":"漿","蒋":"蔣","桨":"槳","奖":"獎","讲":"講","酱":"醬","绛":"絳","缰":"韁","胶":"膠","浇":"澆","骄":"驕","娇":"嬌","搅":"攪","铰":"鉸","矫":"矯","侥":"僥","脚":"腳","饺":"餃","缴":"繳","绞":"絞","轿":"轎","较":"較","挢":"撟","峤":"嶠","鹪":"鷦","鲛":"鮫","阶":"階","节":"節","洁":"潔","结":"結","诫":"誡","届":"屆","疖":"癤","颌":"頜","鲒":"鮚","紧":"緊","锦":"錦","仅":"僅","谨":"謹","进":"進","晋":"晉","烬":"燼","尽":"盡","劲":"勁","荆":"荊","茎":"莖","卺":"巹","荩":"藎","馑":"饉","缙":"縉","赆":"贐","觐":"覲","鲸":"鯨","惊":"驚","经":"經","颈":"頸","静":"靜","镜":"鏡","径":"徑","痉":"痙","竞":"競","净":"凈","刭":"剄","泾":"涇","迳":"逕","弪":"弳","胫":"脛","靓":"靚","纠":"糾","厩":"廄","旧":"舊","阄":"鬮","鸠":"鳩","鹫":"鷲","驹":"駒","举":"舉","据":"據","锯":"鋸","惧":"懼","剧":"劇","讵":"詎","屦":"屨","榉":"櫸","飓":"颶","钜":"鉅","锔":"鋦","窭":"窶","龃":"齟","鹃":"鵑","绢":"絹","锩":"錈","镌":"鐫","隽":"雋","觉":"覺","决":"決","绝":"絕","谲":"譎","珏":"玨","钧":"鈞","军":"軍","骏":"駿","皲":"皸","开":"開","凯":"凱","剀":"剴","垲":"塏","忾":"愾","恺":"愷","铠":"鎧","锴":"鍇","龛":"龕","闶":"閌","钪":"鈧","铐":"銬","颗":"顆","壳":"殼","课":"課","骒":"騍","缂":"緙","轲":"軻","钶":"鈳","锞":"錁","颔":"頷","垦":"墾","恳":"懇","龈":"齦","铿":"鏗","抠":"摳","库":"庫","裤":"褲","喾":"嚳","块":"塊","侩":"儈","郐":"鄶","哙":"噲","脍":"膾","宽":"寬","狯":"獪","髋":"髖","矿":"礦","旷":"曠","况":"況","诓":"誆","诳":"誑","邝":"鄺","圹":"壙","纩":"纊","贶":"貺","亏":"虧","岿":"巋","窥":"窺","馈":"饋","溃":"潰","匮":"匱","蒉":"蕢","愦":"憒","聩":"聵","篑":"簣","阃":"閫","锟":"錕","鲲":"鯤","扩":"擴","阔":"闊","蛴":"蠐","蜡":"蠟","腊":"臘","莱":"萊","来":"來","赖":"賴","崃":"崍","徕":"徠","涞":"淶","濑":"瀨","赉":"賚","睐":"睞","铼":"錸","癞":"癩","籁":"籟","蓝":"藍","栏":"欄","拦":"攔","篮":"籃","阑":"闌","兰":"蘭","澜":"瀾","谰":"讕","揽":"攬","览":"覽","懒":"懶","缆":"纜","烂":"爛","滥":"濫","岚":"嵐","榄":"欖","斓":"斕","镧":"鑭","褴":"襤","琅":"瑯","阆":"閬","锒":"鋃","捞":"撈","劳":"勞","涝":"澇","唠":"嘮","崂":"嶗","铑":"銠","铹":"鐒","痨":"癆","乐":"樂","鳓":"鰳","镭":"鐳","垒":"壘","类":"類","泪":"淚","诔":"誄","缧":"縲","篱":"籬","狸":"貍","离":"離","鲤":"鯉","礼":"禮","丽":"麗","厉":"厲","励":"勵","砾":"礫","历":"歷","沥":"瀝","隶":"隸","俪":"儷","郦":"酈","坜":"壢","苈":"藶","莅":"蒞","蓠":"蘺","呖":"嚦","逦":"邐","骊":"驪","缡":"縭","枥":"櫪","栎":"櫟","轹":"轢","砺":"礪","锂":"鋰","鹂":"鸝","疠":"癘","粝":"糲","跞":"躒","雳":"靂","鲡":"鱺","鳢":"鱧","俩":"倆","联":"聯","莲":"蓮","连":"連","镰":"鐮","怜":"憐","涟":"漣","帘":"簾","敛":"斂","脸":"臉","链":"鏈","恋":"戀","炼":"煉","练":"練","蔹":"蘞","奁":"奩","潋":"瀲","琏":"璉","殓":"殮","裢":"褳","裣":"襝","鲢":"鰱","粮":"糧","凉":"涼","两":"兩","辆":"輛","谅":"諒","魉":"魎","疗":"療","辽":"遼","镣":"鐐","缭":"繚","钌":"釕","鹩":"鷯","猎":"獵","临":"臨","邻":"鄰","鳞":"鱗","凛":"凜","赁":"賃","蔺":"藺","廪":"廩","檩":"檁","辚":"轔","躏":"躪","龄":"齡","铃":"鈴","灵":"靈","岭":"嶺","领":"領","绫":"綾","棂":"欞","蛏":"蟶","鲮":"鯪","馏":"餾","刘":"劉","浏":"瀏","骝":"騮","绺":"綹","镏":"鎦","鹨":"鷚","龙":"龍","聋":"聾","咙":"嚨","笼":"籠","垄":"壟","拢":"攏","陇":"隴","茏":"蘢","泷":"瀧","珑":"瓏","栊":"櫳","胧":"朧","砻":"礱","楼":"樓","娄":"婁","搂":"摟","篓":"簍","偻":"僂","蒌":"蔞","喽":"嘍","嵝":"嶁","镂":"鏤","瘘":"瘺","耧":"耬","蝼":"螻","髅":"髏","芦":"蘆","卢":"盧","颅":"顱","庐":"廬","炉":"爐","掳":"擄","卤":"鹵","虏":"虜","鲁":"魯","赂":"賂","禄":"祿","录":"錄","陆":"陸","垆":"壚","撸":"擼","噜":"嚕","闾":"閭","泸":"瀘","渌":"淥","栌":"櫨","橹":"櫓","轳":"轤","辂":"輅","辘":"轆","氇":"氌","胪":"臚","鸬":"鸕","鹭":"鷺","舻":"艫","鲈":"鱸","峦":"巒","挛":"攣","孪":"孿","滦":"灤","乱":"亂","脔":"臠","娈":"孌","栾":"欒","鸾":"鸞","銮":"鑾","抡":"掄","轮":"輪","伦":"倫","仑":"侖","沦":"淪","纶":"綸","论":"論","囵":"圇","萝":"蘿","罗":"羅","逻":"邏","锣":"鑼","箩":"籮","骡":"騾","骆":"駱","络":"絡","荦":"犖","猡":"玀","泺":"濼","椤":"欏","脶":"腡","镙":"鏍","驴":"驢","吕":"呂","铝":"鋁","侣":"侶","屡":"屢","缕":"縷","虑":"慮","滤":"濾","绿":"綠","榈":"櫚","褛":"褸","锊":"鋝","呒":"嘸","妈":"媽","玛":"瑪","码":"碼","蚂":"螞","马":"馬","骂":"罵","吗":"嗎","唛":"嘜","嬷":"嬤","杩":"榪","买":"買","麦":"麥","卖":"賣","迈":"邁","脉":"脈","劢":"勱","瞒":"瞞","馒":"饅","蛮":"蠻","满":"滿","谩":"謾","缦":"縵","镘":"鏝","颡":"顙","鳗":"鰻","猫":"貓","锚":"錨","铆":"鉚","贸":"貿","麽":"麼","没":"沒","镁":"鎂","门":"門","闷":"悶","们":"們","扪":"捫","焖":"燜","懑":"懣","钔":"鍆","锰":"錳","梦":"夢","眯":"瞇","谜":"謎","弥":"彌","觅":"覓","幂":"冪","芈":"羋","谧":"謐","猕":"獼","祢":"禰","绵":"綿","缅":"緬","渑":"澠","腼":"靦","黾":"黽","庙":"廟","缈":"緲","缪":"繆","灭":"滅","悯":"憫","闽":"閩","闵":"閔","缗":"緡","鸣":"鳴","铭":"銘","谬":"謬","谟":"謨","蓦":"驀","馍":"饃","殁":"歿","镆":"鏌","谋":"謀","亩":"畝","钼":"鉬","呐":"吶","钠":"鈉","纳":"納","难":"難","挠":"撓","脑":"腦","恼":"惱","闹":"鬧","铙":"鐃","讷":"訥","馁":"餒","内":"內","拟":"擬","腻":"膩","铌":"鈮","鲵":"鯢","撵":"攆","辇":"輦","鲶":"鯰","酿":"釀","鸟":"鳥","茑":"蔦","袅":"裊","聂":"聶","啮":"嚙","镊":"鑷","镍":"鎳","陧":"隉","蘖":"蘗","嗫":"囁","颟":"顢","蹑":"躡","柠":"檸","狞":"獰","宁":"寧","拧":"擰","泞":"濘","苎":"苧","咛":"嚀","聍":"聹","钮":"鈕","纽":"紐","脓":"膿","浓":"濃","农":"農","侬":"儂","哝":"噥","驽":"駑","钕":"釹","诺":"諾","傩":"儺","疟":"瘧","欧":"歐","鸥":"鷗","殴":"毆","呕":"嘔","沤":"漚","讴":"謳","怄":"慪","瓯":"甌","盘":"盤","蹒":"蹣","庞":"龐","抛":"拋","疱":"皰","赔":"賠","辔":"轡","喷":"噴","鹏":"鵬","纰":"紕","罴":"羆","铍":"鈹","骗":"騙","谝":"諞","骈":"駢","飘":"飄","缥":"縹","频":"頻","贫":"貧","嫔":"嬪","苹":"蘋","凭":"憑","评":"評","泼":"潑","颇":"頗","钋":"釙","扑":"撲","铺":"鋪","朴":"樸","谱":"譜","镤":"鏷","镨":"鐠","栖":"棲","脐":"臍","齐":"齊","骑":"騎","岂":"豈","启":"啟","气":"氣","弃":"棄","讫":"訖","蕲":"蘄","骐":"騏","绮":"綺","桤":"榿","碛":"磧","颀":"頎","颃":"頏","鳍":"鰭","牵":"牽","钎":"釬","铅":"鉛","迁":"遷","签":"簽","谦":"謙","钱":"錢","钳":"鉗","潜":"潛","浅":"淺","谴":"譴","堑":"塹","佥":"僉","荨":"蕁","悭":"慳","骞":"騫","缱":"繾","椠":"槧","钤":"鈐","枪":"槍","呛":"嗆","墙":"墻","蔷":"薔","强":"強","抢":"搶","嫱":"嬙","樯":"檣","戗":"戧","炝":"熗","锖":"錆","锵":"鏘","镪":"鏹","羟":"羥","跄":"蹌","锹":"鍬","桥":"橋","乔":"喬","侨":"僑","翘":"翹","窍":"竅","诮":"誚","谯":"譙","荞":"蕎","缲":"繰","硗":"磽","跷":"蹺","窃":"竊","惬":"愜","锲":"鍥","箧":"篋","钦":"欽","亲":"親","寝":"寢","锓":"鋟","轻":"輕","氢":"氫","倾":"傾","顷":"頃","请":"請","庆":"慶","揿":"撳","鲭":"鯖","琼":"瓊","穷":"窮","茕":"煢","蛱":"蛺","巯":"巰","赇":"賕","虮":"蟣","鳅":"鰍","趋":"趨","区":"區","躯":"軀","驱":"驅","龋":"齲","诎":"詘","岖":"嶇","阒":"闃","觑":"覷","鸲":"鴝","颧":"顴","权":"權","劝":"勸","诠":"詮","绻":"綣","辁":"輇","铨":"銓","却":"卻","鹊":"鵲","确":"確","阕":"闋","阙":"闕","悫":"愨","让":"讓","饶":"饒","扰":"擾","绕":"繞","荛":"蕘","娆":"嬈","桡":"橈","热":"熱","韧":"韌","认":"認","纫":"紉","饪":"飪","轫":"軔","荣":"榮","绒":"絨","嵘":"嶸","蝾":"蠑","缛":"縟","铷":"銣","颦":"顰","软":"軟","锐":"銳","蚬":"蜆","闰":"閏","润":"潤","洒":"灑","萨":"薩","飒":"颯","鳃":"鰓","赛":"賽","伞":"傘","毵":"毿","糁":"糝","丧":"喪","骚":"騷","扫":"掃","缫":"繅","涩":"澀","啬":"嗇","铯":"銫","穑":"穡","杀":"殺","刹":"剎","纱":"紗","铩":"鎩","鲨":"鯊","筛":"篩","晒":"曬","酾":"釃","删":"刪","闪":"閃","陕":"陜","赡":"贍","缮":"繕","讪":"訕","姗":"姍","骟":"騸","钐":"釤","鳝":"鱔","墒":"墑","伤":"傷","赏":"賞","垧":"坰","殇":"殤","觞":"觴","烧":"燒","绍":"紹","赊":"賒","摄":"攝","慑":"懾","设":"設","厍":"厙","滠":"灄","畲":"畬","绅":"紳","审":"審","婶":"嬸","肾":"腎","渗":"滲","诜":"詵","谂":"諗","渖":"瀋","声":"聲","绳":"繩","胜":"勝","师":"師","狮":"獅","湿":"濕","诗":"詩","时":"時","蚀":"蝕","实":"實","识":"識","驶":"駛","势":"勢","适":"適","释":"釋","饰":"飾","视":"視","试":"試","谥":"謚","埘":"塒","莳":"蒔","弑":"弒","轼":"軾","贳":"貰","铈":"鈰","鲥":"鰣","寿":"壽","兽":"獸","绶":"綬","枢":"樞","输":"輸","书":"書","赎":"贖","属":"屬","术":"術","树":"樹","竖":"豎","数":"數","摅":"攄","纾":"紓","帅":"帥","闩":"閂","双":"雙","谁":"誰","税":"稅","顺":"順","说":"說","硕":"碩","烁":"爍","铄":"鑠","丝":"絲","饲":"飼","厮":"廝","驷":"駟","缌":"緦","锶":"鍶","鸶":"鷥","耸":"聳","怂":"慫","颂":"頌","讼":"訟","诵":"誦","擞":"擻","薮":"藪","馊":"餿","飕":"颼","锼":"鎪","苏":"蘇","诉":"訴","肃":"肅","谡":"謖","稣":"穌","虽":"雖","随":"隨","绥":"綏","岁":"歲","谇":"誶","孙":"孫","损":"損","笋":"筍","荪":"蓀","狲":"猻","缩":"縮","琐":"瑣","锁":"鎖","唢":"嗩","睃":"脧","獭":"獺","挞":"撻","闼":"闥","铊":"鉈","鳎":"鰨","台":"臺","态":"態","钛":"鈦","鲐":"鮐","摊":"攤","贪":"貪","瘫":"癱","滩":"灘","坛":"壇","谭":"譚","谈":"談","叹":"嘆","昙":"曇","钽":"鉭","锬":"錟","顸":"頇","汤":"湯","烫":"燙","傥":"儻","饧":"餳","铴":"鐋","镗":"鏜","涛":"濤","绦":"絳","讨":"討","韬":"韜","铽":"鋱","腾":"騰","誊":"謄","锑":"銻","题":"題","体":"體","屉":"屜","缇":"緹","鹈":"鵜","阗":"闐","条":"條","粜":"糶","龆":"齠","鲦":"鰷","贴":"貼","铁":"鐵","厅":"廳","听":"聽","烃":"烴","铜":"銅","统":"統","恸":"慟","头":"頭","钭":"鈄","秃":"禿","图":"圖","钍":"釷","团":"團","抟":"摶","颓":"頹","蜕":"蛻","饨":"飩","脱":"脫","鸵":"鴕","驮":"馱","驼":"駝","椭":"橢","箨":"籜","鼍":"鼉","袜":"襪","娲":"媧","腽":"膃","弯":"彎","湾":"灣","顽":"頑","万":"萬","纨":"紈","绾":"綰","网":"網","辋":"輞","韦":"韋","违":"違","围":"圍","为":"為","潍":"濰","维":"維","苇":"葦","伟":"偉","伪":"偽","纬":"緯","谓":"謂","卫":"衛","诿":"諉","帏":"幃","闱":"闈","沩":"溈","涠":"潿","玮":"瑋","韪":"韙","炜":"煒","鲔":"鮪","温":"溫","闻":"聞","纹":"紋","稳":"穩","问":"問","阌":"閿","瓮":"甕","挝":"撾","蜗":"蝸","涡":"渦","窝":"窩","卧":"臥","莴":"萵","龌":"齷","呜":"嗚","钨":"鎢","乌":"烏","诬":"誣","无":"無","芜":"蕪","吴":"吳","坞":"塢","雾":"霧","务":"務","误":"誤","邬":"鄔","庑":"廡","怃":"憮","妩":"嫵","骛":"騖","鹉":"鵡","鹜":"鶩","锡":"錫","牺":"犧","袭":"襲","习":"習","铣":"銑","戏":"戲","细":"細","饩":"餼","阋":"鬩","玺":"璽","觋":"覡","虾":"蝦","辖":"轄","峡":"峽","侠":"俠","狭":"狹","厦":"廈","吓":"嚇","硖":"硤","鲜":"鮮","纤":"纖","贤":"賢","衔":"銜","闲":"閑","显":"顯","险":"險","现":"現","献":"獻","县":"縣","馅":"餡","羡":"羨","宪":"憲","线":"線","苋":"莧","莶":"薟","藓":"蘚","岘":"峴","猃":"獫","娴":"嫻","鹇":"鷴","痫":"癇","蚝":"蠔","籼":"秈","跹":"躚","厢":"廂","镶":"鑲","乡":"鄉","详":"詳","响":"響","项":"項","芗":"薌","饷":"餉","骧":"驤","缃":"緗","飨":"饗","萧":"蕭","嚣":"囂","销":"銷","晓":"曉","啸":"嘯","哓":"嘵","潇":"瀟","骁":"驍","绡":"綃","枭":"梟","箫":"簫","协":"協","挟":"挾","携":"攜","胁":"脅","谐":"諧","写":"寫","泻":"瀉","谢":"謝","亵":"褻","撷":"擷","绁":"紲","缬":"纈","锌":"鋅","衅":"釁","兴":"興","陉":"陘","荥":"滎","凶":"兇","汹":"洶","锈":"銹","绣":"繡","馐":"饈","鸺":"鵂","虚":"虛","嘘":"噓","须":"須","许":"許","叙":"敘","绪":"緒","续":"續","诩":"詡","顼":"頊","轩":"軒","悬":"懸","选":"選","癣":"癬","绚":"絢","谖":"諼","铉":"鉉","镟":"鏇","学":"學","谑":"謔","泶":"澩","鳕":"鱈","勋":"勛","询":"詢","寻":"尋","驯":"馴","训":"訓","讯":"訊","逊":"遜","埙":"塤","浔":"潯","鲟":"鱘","压":"壓","鸦":"鴉","鸭":"鴨","哑":"啞","亚":"亞","讶":"訝","垭":"埡","娅":"婭","桠":"椏","氩":"氬","阉":"閹","烟":"煙","盐":"鹽","严":"嚴","岩":"巖","颜":"顏","阎":"閻","艳":"艷","厌":"厭","砚":"硯","彦":"彥","谚":"諺","验":"驗","厣":"厴","赝":"贗","俨":"儼","兖":"兗","谳":"讞","恹":"懨","闫":"閆","酽":"釅","魇":"魘","餍":"饜","鼹":"鼴","鸯":"鴦","杨":"楊","扬":"揚","疡":"瘍","阳":"陽","痒":"癢","养":"養","样":"樣","炀":"煬","瑶":"瑤","摇":"搖","尧":"堯","遥":"遙","窑":"窯","谣":"謠","药":"藥","轺":"軺","鹞":"鷂","鳐":"鰩","爷":"爺","页":"頁","业":"業","叶":"葉","靥":"靨","谒":"謁","邺":"鄴","晔":"曄","烨":"燁","医":"醫","铱":"銥","颐":"頤","遗":"遺","仪":"儀","蚁":"蟻","艺":"藝","亿":"億","忆":"憶","义":"義","诣":"詣","议":"議","谊":"誼","译":"譯","异":"異","绎":"繹","诒":"詒","呓":"囈","峄":"嶧","饴":"飴","怿":"懌","驿":"驛","缢":"縊","轶":"軼","贻":"貽","钇":"釔","镒":"鎰","镱":"鐿","瘗":"瘞","舣":"艤","荫":"蔭","阴":"陰","银":"銀","饮":"飲","隐":"隱","铟":"銦","瘾":"癮","樱":"櫻","婴":"嬰","鹰":"鷹","应":"應","缨":"纓","莹":"瑩","萤":"螢","营":"營","荧":"熒","蝇":"蠅","赢":"贏","颖":"穎","茔":"塋","莺":"鶯","萦":"縈","蓥":"鎣","撄":"攖","嘤":"嚶","滢":"瀅","潆":"瀠","璎":"瓔","鹦":"鸚","瘿":"癭","颏":"頦","罂":"罌","哟":"喲","拥":"擁","佣":"傭","痈":"癰","踊":"踴","咏":"詠","镛":"鏞","优":"優","忧":"憂","邮":"郵","铀":"鈾","犹":"猶","诱":"誘","莸":"蕕","铕":"銪","鱿":"魷","舆":"輿","鱼":"魚","渔":"漁","娱":"娛","与":"與","屿":"嶼","语":"語","狱":"獄","誉":"譽","预":"預","驭":"馭","伛":"傴","俣":"俁","谀":"諛","谕":"諭","蓣":"蕷","嵛":"崳","饫":"飫","阈":"閾","妪":"嫗","纡":"紆","觎":"覦","欤":"歟","钰":"鈺","鹆":"鵒","鹬":"鷸","龉":"齬","鸳":"鴛","渊":"淵","辕":"轅","园":"園","员":"員","圆":"圓","缘":"緣","远":"遠","橼":"櫞","鸢":"鳶","鼋":"黿","约":"約","跃":"躍","钥":"鑰","粤":"粵","悦":"悅","阅":"閱","钺":"鉞","郧":"鄖","匀":"勻","陨":"隕","运":"運","蕴":"蘊","酝":"醞","晕":"暈","韵":"韻","郓":"鄆","芸":"蕓","恽":"惲","愠":"慍","纭":"紜","韫":"韞","殒":"殞","氲":"氳","杂":"雜","灾":"災","载":"載","攒":"攢","暂":"暫","赞":"贊","瓒":"瓚","趱":"趲","錾":"鏨","赃":"贓","脏":"臟","驵":"駔","凿":"鑿","枣":"棗","责":"責","择":"擇","则":"則","泽":"澤","赜":"賾","啧":"嘖","帻":"幘","箦":"簀","贼":"賊","谮":"譖","赠":"贈","综":"綜","缯":"繒","轧":"軋","铡":"鍘","闸":"閘","栅":"柵","诈":"詐","斋":"齋","债":"債","毡":"氈","盏":"盞","斩":"斬","辗":"輾","崭":"嶄","栈":"棧","战":"戰","绽":"綻","谵":"譫","张":"張","涨":"漲","帐":"帳","账":"賬","胀":"脹","赵":"趙","诏":"詔","钊":"釗","蛰":"蟄","辙":"轍","锗":"鍺","这":"這","谪":"謫","辄":"輒","鹧":"鷓","贞":"貞","针":"針","侦":"偵","诊":"診","镇":"鎮","阵":"陣","浈":"湞","缜":"縝","桢":"楨","轸":"軫","赈":"賑","祯":"禎","鸩":"鴆","挣":"掙","睁":"睜","狰":"猙","争":"爭","帧":"幀","症":"癥","郑":"鄭","证":"證","诤":"諍","峥":"崢","钲":"鉦","铮":"錚","筝":"箏","织":"織","职":"職","执":"執","纸":"紙","挚":"摯","掷":"擲","帜":"幟","质":"質","滞":"滯","骘":"騭","栉":"櫛","栀":"梔","轵":"軹","轾":"輊","贽":"贄","鸷":"鷙","蛳":"螄","絷":"縶","踬":"躓","踯":"躑","觯":"觶","钟":"鐘","终":"終","种":"種","肿":"腫","众":"眾","锺":"鍾","诌":"謅","轴":"軸","皱":"皺","昼":"晝","骤":"驟","纣":"紂","绉":"縐","猪":"豬","诸":"諸","诛":"誅","烛":"燭","瞩":"矚","嘱":"囑","贮":"貯","铸":"鑄","驻":"駐","伫":"佇","槠":"櫧","铢":"銖","专":"專","砖":"磚","转":"轉","赚":"賺","啭":"囀","馔":"饌","颞":"顳","桩":"樁","庄":"莊","装":"裝","妆":"妝","壮":"壯","状":"狀","锥":"錐","赘":"贅","坠":"墜","缀":"綴","骓":"騅","缒":"縋","谆":"諄","准":"準","着":"著","浊":"濁","诼":"諑","镯":"鐲","兹":"茲","资":"資","渍":"漬","谘":"諮","缁":"緇","辎":"輜","赀":"貲","眦":"眥","锱":"錙","龇":"齜","鲻":"鯔","踪":"蹤","总":"總","纵":"縱","偬":"傯","邹":"鄒","诹":"諏","驺":"騶","鲰":"鯫","诅":"詛","组":"組","镞":"鏃","钻":"鉆","缵":"纘","躜":"躦","鳟":"鱒","翱":"翺","并":"並","卜":"蔔","沉":"沈","丑":"醜","淀":"澱","迭":"叠","斗":"鬥","范":"範","干":"幹","皋":"臯","硅":"矽","柜":"櫃","后":"後","伙":"夥","秸":"稭","杰":"傑","诀":"訣","夸":"誇","里":"裏","凌":"淩","么":"麽","霉":"黴","捻":"撚","凄":"淒","扦":"扡","圣":"聖","尸":"屍","抬":"擡","涂":"塗","洼":"窪","喂":"餵","污":"汙","锨":"鍁","咸":"鹹","蝎":"蠍","彝":"彜","涌":"湧","游":"遊","吁":"籲","御":"禦","愿":"願","岳":"嶽","云":"雲","灶":"竈","扎":"紮","札":"劄","筑":"築","于":"於","志":"誌","注":"註","凋":"雕","讠":"訁","谫":"譾","郄":"郤","勐":"猛","凼":"氹","坂":"阪","垅":"壟","垴":"堖","埯":"垵","埝":"墊","苘":"檾","荬":"蕒","荮":"葤","莜":"蓧","莼":"蒓","菰":"菇","藁":"槁","揸":"摣","吒":"咤","吣":"唚","咔":"哢","咝":"噝","咴":"噅","噘":"撅","噼":"劈","嚯":"謔","幞":"襆","岙":"嶴","嵴":"脊","彷":"仿","徼":"僥","犸":"獁","狍":"麅","馀":"餘","馇":"餷","馓":"饊","馕":"饢","愣":"楞","憷":"怵","懔":"懍","丬":"爿","溆":"漵","滟":"灩","溷":"混","漤":"濫","潴":"瀦","澹":"淡","甯":"寧","纟":"糸","绔":"絝","绱":"緔","珉":"瑉","枧":"梘","桊":"棬","桉":"案","槔":"橰","橥":"櫫","轱":"軲","轷":"軤","赍":"賫","肷":"膁","胨":"腖","飚":"飈","煳":"糊","煅":"煆","熘":"溜","愍":"湣","淼":"渺","砜":"碸","磙":"滾","眍":"瞘","钚":"鈈","钷":"鉕","铘":"鋣","铞":"銱","锃":"鋥","锍":"鋶","锎":"鐦","锏":"鐧","锘":"鍩","锝":"鍀","锪":"鍃","锫":"錇","锿":"鎄","镅":"鎇","镎":"鎿","镢":"鐝","镥":"鑥","镩":"鑹","镲":"鑔","稆":"穭","鹋":"鶓","鹛":"鶥","鹱":"鸌","疬":"癧","疴":"屙","痖":"瘂","癯":"臒","裥":"襇","襁":"繈","耢":"耮","颥":"顬","螨":"蟎","麴":"麯","鲅":"鮁","鲆":"鮃","鲇":"鮎","鲞":"鯗","鲴":"鯝","鲺":"鯴","鲼":"鱝","鳊":"鯿","鳋":"鰠","鳘":"鰵","鳙":"鱅","鞒":"鞽","鞴":"韝","齄":"齇"}
},function(n,t){"use strict";t.__esModule=!0,t.changeFocus=t.press=void 0;var e={69:"quit",77:"cursor",78:"cross",187:"enlarge",189:"narrow",84:"color",67:"reset",191:"explain",86:"voiceSwitch",75:"voiceSpeed",70:"readMode",66:"bigTextSwitch",82:"readScreen",192:"start"};t.press=function(n){var t,i,o=(t=(s=n).some(function(n){return 18===n}),o=s.some(function(n){return 17===n}),s=(i=s.filter(function(n){return 17!==n&&18!==n})).every(function(n){return n===i[0]}),t&&o&&i[0]&&s?i[0]:void 0),a=n,s=1===a.length,n=a.every(function(n){return n===a[0]
});return o?e[o]:(s||n)&&13===a[0]?"enter":void 0},t.changeFocus=function(n){return 9===n?"changeFocus":void 0}},function(n,t,e){var i=e(2),o=[e(54)],a=e(56),s=e(19).default;n.exports=e(19),n.exports.default=i(s,a,o)},function(n,t,e){var i=e(55);(i="string"==typeof i?[[n.i,i,""]]:i).locals&&(n.exports=i.locals),e(4).default("a6427286",i,!0,{attributes:{"data-for":"result"},runAsProduction:!0})},function(n,t,e){(t=e(3)(!1)).push([n.i,".left_WZkhf {\n  float: left;\n}\n.right_2aKzf {\n  float: right;\n}\n.area-item_365JP {\n  width: 114px;\n  height: 78px;\n  display: inline-block;\n  padding: 15px;\n  box-sizing: border-box;\n  background: #fff;\n  box-shadow: 0 0 8px 0 rgba(98, 102, 117, 0.1);\n  border-radius: 20px;\n  margin-right: 10px;\n  vertical-align: top;\n  cursor: pointer;\n}\n.info_2NBYW {\n  margin-bottom: 7px;\n}\n.info-left_1q7Gg {\n  font-size: 18px;\n  color: #222;\n}\n.subinfo_14Bq4 {\n  font-size: 16px;\n  color: #626675;\n  margin-left: 1px;\n}\n.explain-link_2dETD {\n  display: inline-block;\n  margin-right: 10px;\n}\n.last_1HbUy {\n  margin-right: 75px!important;\n}\n.num_1mI7x {\n  font-size: 18px;\n  color: #4E6EF2;\n}\n",""]),t.locals={left:"left_WZkhf",right:"right_2aKzf","area-item":"area-item_365JP",areaItem:"area-item_365JP",info:"info_2NBYW","info-left":"info-left_1q7Gg",infoLeft:"info-left_1q7Gg",subinfo:"subinfo_14Bq4","explain-link":"explain-link_2dETD",explainLink:"explain-link_2dETD",last:"last_1HbUy",num:"num_1mI7x"},n.exports=t
},function(n){n.exports=' <div> <div class="{{$style.left}}"> <div class="{{$style[\'area-item\']}}" s-if="useNav" on-click="handleNav(num[0])"> <div class="{{$style.info}}"> <span class="{{$style[\'info-left\']}}">导航区</span> <span class="{{$style.num}}">{{num[0]}}</span> </div> <span class="{{$style.subinfo}}">{{ areaQuick[0] }}</span> </div> <div class="{{$style[\'area-item\']}}" on-click="handleView(num[1])"> <div class="{{$style.info}}"> <span class="{{$style[\'info-left\']}}">视窗区</span> <span class="{{$style.num}}">{{num[1]}}</span> </div> <span class="{{$style.subinfo}}">{{ areaQuick[1] }}</span> </div> <div class="{{$style[\'area-item\']}}" on-click="handleOperation(num[2])"> <div class="{{$style.info}}"> <span class="{{$style[\'info-left\']}}">交互区</span> <span class="{{$style.num}}">{{num[2]}}</span> </div> <span class="{{$style.subinfo}}">{{ areaQuick[2] }}</span> </div> <div class="{{$style[\'area-item\']}}" on-click="handleService(num[3])"> <div class="{{$style.info}}"> <span class="{{$style[\'info-left\']}}">服务区</span> <span class="{{$style.num}}">{{num[3]}}</span> </div> <span class="{{$style.subinfo}}">{{ areaQuick[3] }}</span> </div> </div> <div class="{{$style.right}}"> <a-voice-item onlyOnoff="{{ true }}" isReadScreen="{{ true }}" statusList="{{ statusList }}" screenText="{{ screenText }}" from="{{ from }}" on-logSend="logSend"> </a-voice-item> <a href="{{ explainUrl }}" class="{{$style[\'explain-link\']}}" target="_blank" on-click="logSend(\'说明\')"> <a-item text="说明" class="{{$style.item}}" nogap type="explain" key="说明 Ctrl 加 Alt 加 问号键"> </a-item> </a> <a-item text="退出引导" wide class="{{$style.item}}" type="areaQuit" key="退出引导 Ctrl 加 Alt 加 R" on-click="handleAreaClick"> </a-item> <a-item text="退出服务" class="{{$style.item}} {{$style.last}}" type="quit" on-click="handleQuit" key="关闭服务 Ctrl 加 Alt 加 E"> </a-item> </div> <a-bottom-popup s-if="showPopup" readText="{{ readText }}" on-close="handleClose" on-logSend="logSend"></a-bottom-popup> </div> '
}],o={},e.m=i,e.c=o,e.d=function(n,t,i){e.o(n,t)||Object.defineProperty(n,t,{enumerable:!0,get:i})},e.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},e.t=function(n,t){if(1&t&&(n=e(n)),8&t)return n;if(4&t&&"object"==typeof n&&n&&n.__esModule)return n;var i=Object.create(null);if(e.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:n}),2&t&&"string"!=typeof n)for(var o in n)e.d(i,o,function(t){return n[t]
}.bind(null,o));return i},e.n=function(n){var t=n&&n.__esModule?function(){return n.default}:function(){return n};return e.d(t,"a",t),t},e.o=function(n,t){return Object.prototype.hasOwnProperty.call(n,t)},e.p="",e(e.s=20);var i,o}),define("@baidu/aging-tools-pc",["@baidu/aging-tools-pc/dist/index"],function(n){return n});