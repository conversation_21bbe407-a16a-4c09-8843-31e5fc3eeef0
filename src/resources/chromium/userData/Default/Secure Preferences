{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "manifest_permissions": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13321421511204679", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "在这里，您可以找到适用于 Chromium 的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Chrome 网上应用店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "D:\\Project\\Python\\drissionpage-demo\\chromium\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "kmendfapggjehodndflmmgagdbamhnfd": {"active_permissions": {"api": ["cryptotokenPrivate", "externally_connectable.all_urls", "tabs"], "explicit_host": ["http://*/*", "https://*/*"], "manifest_permissions": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["runtime.onConnectExternal"], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13321421511207344", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["util.js", "b64.js", "cbor.js", "sha256.js", "timer.js", "countdown.js", "countdowntimer.js", "devicestatuscodes.js", "approvedorigins.js", "errorcodes.js", "webrequest.js", "messagetypes.js", "factoryregistry.js", "requesthelper.js", "asn1.js", "enroller.js", "requestqueue.js", "signer.js", "origincheck.js", "textfetcher.js", "appid.js", "watchdog.js", "logging.js", "webrequestsender.js", "window-timer.js", "cryptotokenorigincheck.js", "cryptotokenapprovedorigins.js", "inherits.js", "individualattest.js", "googlecorpindividualattest.js", "cryptotokenbackground.js"]}, "description": "CryptoToken Component Extension", "externally_connectable": {"ids": ["fjajfjhkeibgmiggdfehjplbhmfkialk"], "matches": ["https://*/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7zRobvA+AVlvNqkHSSVhh1sEWsHSqz4oR/XptkDe/Cz3+gW9ZGumZ20NCHjaac8j1iiesdigp8B1LJsd/2WWv2Dbnto4f8GrQ5MVphKyQ9WJHwejEHN2K4vzrTcwaXqv5BSTXwxlxS/mXCmXskTfryKTLuYrcHEWK8fCHb+0gvr8b/kvsi75A1aMmb6nUnFJvETmCkOCPNX5CHTdy634Ts/x0fLhRuPlahk63rdf7agxQv5viVjQFk+tbgv6aa9kdSd11Js/RZ9yZjrFgHOBWgP4jTBqud4+HUglrzu8qynFipyNRLCZsaxhm+NItTyNgesxLdxZcwOz56KD1Q4IQIDAQAB", "manifest_version": 2, "name": "CryptoTokenExtension", "permissions": ["cryptotokenPrivate", "externally_connectable.all_urls", "tabs", "https://*/*", "http://*/*"], "version": "0.9.74"}, "path": "D:\\Project\\Python\\drissionpage-demo\\chromium\\resources\\cryptotoken", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13321421511205909", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "D:\\Project\\Python\\drissionpage-demo\\chromium\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ncennffkjdiamlpmcbajkmaiiiddgioo": {"ack_prompt_count": 1, "active_bit": false, "active_permissions": {"api": ["contextMenus", "cookies", "downloads", "downloadsInternal", "nativeMessaging", "storage", "tabs", "webRequest", "scripting"], "explicit_host": ["<all_urls>", "http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["ftp://*/*", "http://*/*", "https://*/*"]}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 4097, "disable_reasons": 8192, "external_first_run": true, "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13321421512823850", "location": 3, "manifest": {"action": {"default_icon": "images/icon19_normal.png", "default_popup": "popup.html", "default_title": "迅雷Chrome支持"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"all_frames": true, "css": ["js/xl-content.css"], "js": ["js/xl-content.js"], "matches": ["http://*/*", "https://*/*", "ftp://*/*"], "run_at": "document_start"}], "current_locale": "zh_CN", "default_locale": "zh_CN", "description": "迅雷下载支持", "host_permissions": ["<all_urls>", "http://*/*", "https://*/*"], "icons": {"128": "images/install_logo.png", "16": "images/menu_logo.png", "48": "images/extension_logo.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDEa5DG04lhgzzm3gRSXPPOZOv6ZXnzQrBv+rjUE/dL5br9Duh1kbwGQJCO4QMDvD1usf6FoXDsuvZwYzH6lg1pLI7m/wmQC3NQURHQ7J5zAy7VY0F7qSVqclcpRKY2k00vcqxok6lota3Z1QxUVUwWc9VUfr4gRUeQa4KlEsXzGwIDAQAB", "manifest_version": 3, "name": "迅雷下载支持", "options_page": "options.html", "permissions": ["contextMenus", "cookies", "tabs", "webRequest", "downloads", "nativeMessaging", "storage", "scripting"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "3.32"}, "path": "ncennffkjdiamlpmcbajkmaiiiddgioo\\3.32_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "state": 0, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "8FF9E68AE53B8499CC503F4AC47A4E75460A02B13F53F438CE1F6EA3D657054A"}, "default_search_provider_data": {"template_url_data": "8BCDEB2E59096308EEFFC800BDB9272DE1D1C249A9C44BBAA1C028D2568E86F8"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "D1EE592B6570218C6BC5C6E60A62C4F3FF975E92B9717C52AF87DA2FFE31122A", "kmendfapggjehodndflmmgagdbamhnfd": "F2FD4CE9EEF6D11FDC4A620F95F2B98A50159A8E7EF0C975781CD38E5802C031", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "03BB1E463983968B3C0AAB04DFDA2D32607275C8CD6E68ABE15BD3D91C2F4D34", "ncennffkjdiamlpmcbajkmaiiiddgioo": "20AD593627754A253CBBF52EFE9FBCF500F45BB902CB0263B5BEB0782FFA8EE0"}}, "google": {"services": {"account_id": "6F8768218226F2E05311E629A8D1B99C7CC186780FC06904041023E176D002C1", "last_account_id": "5B5406DBB164564F955CFBEE5B34158F60FF4159BA900CF42AD0476B5859EA25", "last_username": "E90BDEA7599BB01997B296D694C409314068805AEF9D762D505179CFAA023BE9"}}, "homepage": "F47090A18F29DAE5F6361F7A9524F3F4DFF6775AB3BF9D480806EBECC95B37C9", "homepage_is_newtabpage": "589DD17027C1E9CCFE5C6E6E2C67114F28B0601F781BEA3C5EDAF6A82CAA12C4", "media": {"cdm": {"origin_data": "B72E5926128A29AF66C70EEE2286DFFA080FCDEF8FF71A97B5A7FFECF91245F0"}, "storage_id_salt": "61AF26EAC86F4309780C444B9500D4256FFDF4387DE26C40847025EE7BB69071"}, "pinned_tabs": "D77E2A18D2AC01D5FC59150909ED1DC80F245DBE4BAAC98D6DA5AC245285894E", "prefs": {"preference_reset_time": "C3636B864CD3CD2C44A92FC0A82775934D0B6756C2A79A59AE690320C09106E3"}, "safebrowsing": {"incidents_sent": "73B16F93E5B2E1260F4E719BBE015828072F29A1A01BF5D9C45BE2E66AC255FF"}, "search_provider_overrides": "35A20DCD65B0D4A53E47F40688DC5B35B25965D886A22FD2E91D01E8E11E396D", "session": {"restore_on_startup": "9C58FE4B55D77DADD515D5AE35DB52C2DD0C852E5E19DAEE5474D7B95917437F", "startup_urls": "B24D9A1ECE98D23CCDB486FED887EB85F2B4724B63A70D47FCEC32CA07BF0245"}, "settings_reset_prompt": {"last_triggered_for_default_search": "A748CD54F5D884B72DB209C03B602EF86E4319C77D007F36C23F7BDC332969F1", "last_triggered_for_homepage": "6FDCFC64E13AF5B74041F77E9C03991ABBF18277BB1F090CA43A5276F1BEC5A0", "last_triggered_for_startup_urls": "88CFFAC264E3DC0C8F6D8A3F7009279EC0842380702B3E5567820C21D6CD0325", "prompt_wave": "87C7190D6430449CC81824D974FBB6CDE5A4D97D15799AD2BB8D8A0717FB9F6F"}, "software_reporter": {"prompt_seed": "54ED4A480686178767FDF8FC7FB70E42B8075B14A4E1A61B43A8D3BAF7E01522", "prompt_version": "AD0B91824C5D0A15DEB3784B174FC5FDF212EA4A726619DC1DC43A8CA6F7CCA1", "reporting": "02805C27970AEE4C61D168F285513411CA83F74C045B137E0B782FE47855EDF2"}}, "super_mac": "909408EF284A97AD0F7902A5D7C3AC6000A0C9ED511AB003FA74554075662B69"}, "settings_reset_prompt": {"prompt_wave": 20170101}}