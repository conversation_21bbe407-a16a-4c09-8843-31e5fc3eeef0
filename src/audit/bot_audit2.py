import hmac
import base64
import os
import re as ree
import pandas
import requests
import time
import json
from urllib.parse import quote
# API接入方的appid
appid = '4gcblRia'
# API接入方的密钥
secret_key = 'ed35ce6ea44c0640a3507a7751ec6bab33a2a84f'
# 接口host
url = 'https://pgc.botsmart.cn/api'


def encrypt_data(secret_key, data):
    # 将密钥和字符串转换为字节串
    secret_key_bytes = secret_key.encode()
    data_bytes = data.encode()

    # 使用密钥进行加密
    hmac_sha1 = hmac.new(secret_key_bytes, data_bytes, digestmod='sha1')
    digest = hmac_sha1.digest()

    # 将加密结果转换为 Base64 编码的字符串
    encrypted_data = base64.b64encode(digest).decode()

    return encrypted_data

def send_request(url, api_path, request_body, appid, secret_key):
    timestamp = str(int(time.time() * 1000))
    # nonce = timestamp[:10]
    nonce = timestamp[-10:]
    # 拼装加密前的明文字符串，拼装规则：uri(接口地址) + json_body(请求体报文) + timestamp(毫秒) + nonce(请求流水号)
    data = api_path + json.dumps(request_body) + timestamp + nonce
    # 调用加密方法获取加密后的密文，并做url_encode处理
    encrypted_data = encrypt_data(secret_key, data)
    encode_sign = quote(encrypted_data, safe="")
    # 发送 HTTP 请求并设置请求头
    headers = {'Content-Type': 'application/json',
               'Accept': 'application/json',
               'apiSign-appId': appid,
               'apiSign-timestamp':  timestamp,
               'apiSign-nonce': nonce,
               'apiSign-sign': encode_sign}
    try:
        response = requests.post(url + api_path, headers=headers, json=request_body, timeout=30)
    except:
        print('审核失败!')
        return None
    # 处理响应
    print(response.text)
    return response

def audit(schemeId, content):
    request_body = {
        "schemeId": schemeId,
        "content": content
    }
    # 发送接口请求
    resp = send_request(url, '/open/v2/contentAuditRichText', request_body, appid, secret_key)
    return resp

def intercept(str1):
    str_list = []
    i = 1
    while len(str1) > 9000:

        if str1[9000-i] in '\n|\t|\r|，|。|？':
            str_list.append(str1[0:9000-i])
            str1 = str1[9000-i:-1]
            if len(str1) < 9000:
                str_list.append(str1)
            i = 0
        i += 1
    return str_list


if __name__ == '__main__':
    # 审核
    # 方案id
    keyword = ['判决书', '裁定书']
    files_names = ['专业文章']
    for files_name in files_names:
    # files_name = 'case1'
        files_path = 'C:/Users/<USER>/Desktop/'+files_name
        schemeId = '1701842816419807234'
        files = os.listdir(files_path)
        datas = []
        if os.path.exists(files_path + '_statistics.csv'):
            data = pandas.read_csv(files_path + '_statistics.csv', encoding='ANSI', header=None).values.tolist()
        else:
            f2 = open(files_path + '_statistics.csv', mode='a+', encoding='ANSI')
            f2.write('文件名,字数\n')
            f2.close()
            data = []
        if os.path.exists(files_path + '_audit.csv'):
            print(files_path + '_audit.csv 已存在')
        else:
            f2 = open(files_path + '_audit.csv', mode='a+', encoding='utf-8-sig')
            f2.write('文件名,问题分类,问题词,问题段落\n')
            f2.close()
        for da in data:
            datas.append(da[0])
        for file in files:
            if file in datas:
                continue
            with open(files_path+'/'+file, encoding='utf-8') as r:
                pattern = ree.compile(r'<[^>]+>')
                tt = r.read()       # 匹配尖括号内的所有内容
                content = ree.sub(pattern, '', tt)
                r.close()
            cur = ''
            firstLabelName = ''
            sentence = ''
            # flag = False
            # for key in keyword:
            #     if key in ree.sub('\n|\t|\r|，|。|？| |　', '', content):
            #         f2 = open(files_path + '_statistics.csv', mode='a+', encoding='ANSI')
            #         f2.write(file + ',' + str(len(content)) + '\n')
            #         f2.close()
            #         flag = True
            #         break
            print(file+':  '+str(len(content)))
            # if flag:
            #     continue
            if len(content) < 10000:
                # if len(content) < 600:
                #     time.sleep(.3)
                # start_time = time.time()
                rr = audit(schemeId, content)
                # end_time = time.time()
                # print(end_time - start_time)
                if rr is None:
                    continue
                res = json.loads(rr.text)
                if 'code' in res:
                    code = res['code']
                else:
                    code = 1
                while code != 0:
                    time.sleep(2)
                    rr = audit(schemeId, content)
                    if rr is None:
                        break
                    res = json.loads(rr.text)
                    code = res['code']
                    if 'msg' in res:
                        if res['msg'] == '要解析的富文本为空' or res['msg'] == '富文本有效内容为空':
                            break
                if rr is None:
                    continue
                if 'msg' in res:
                    if res['msg'] == '要解析的富文本为空' or res['msg'] == '富文本有效内容为空':
                        time.sleep(1)
                        continue
                for re in res['data']['adviceList']:
                    # if cur == '':
                    #     if re['cur'].isdigit():
                    #         cur = re['cur'] + '\t'
                    #     else:
                    #         cur = re['cur']
                    #         cur = ree.sub('\n|\t|\r|', '', cur)
                    # else:
                    #     cur = cur + '|' + re['cur']
                    # if firstLabelName == '':
                    #     firstLabelName = re['firstLabelName']
                    # else:
                    #     firstLabelName = firstLabelName + '|' + re['firstLabelName']
                    # if sentence == '':
                    #     sentence = re['sentence']
                    # else:
                    #     sentence = sentence + '|' + re['sentence']
                    if re['cur'].isdigit():
                        f1 = open(files_path + '_audit.csv', mode='a+', encoding='utf-8-sig')
                        f1.write(file + ',' + re['firstLabelName'] + ',' + re['cur'] + '\t,' + ree.sub(',', '，', re['sentence']) + '\n')
                        f1.close()
                    else:
                        f1 = open(files_path + '_audit.csv', mode='a+', encoding='utf-8-sig')
                        f1.write(file + ',' + re['firstLabelName'] + ',' + re['cur'] + ',' + ree.sub(',', '，', re['sentence']) + '\n')
                        f1.close()
            else:
                contents = intercept(content)
                for con in contents:
                    if con == '':
                        continue
                    # if len(content) < 600:
                    #     time.sleep(.3)
                    rr = audit(schemeId, con)
                    if rr is None:
                        break
                    res = json.loads(rr.text)
                    if 'code' in res:
                        code = res['code']
                    else:
                        code = 1
                    while code != 0:
                        time.sleep(5)
                        rr = audit(schemeId, con)
                        if rr is None:
                            break
                        res = json.loads(rr.text)
                        code = res['code']
                        if 'msg' in res:
                            if res['msg'] == '要解析的富文本为空' or res['msg'] == '富文本有效内容为空':
                                break
                    if rr is None:
                        break
                    if 'msg' in res:
                        if res['msg'] == '要解析的富文本为空' or res['msg'] == '富文本有效内容为空':
                            time.sleep(1)
                            continue
                    for re in res['data']['adviceList']:
                        # if cur == '':
                        #     if re['cur'].isdigit():
                        #         cur = re['cur'] + '\t'
                        #     else:
                        #         cur = re['cur']
                        #         cur = ree.sub('\n|\t|\r|', '', cur)
                        # else:
                        #     cur = cur + '|' + re['cur']
                        # if firstLabelName == '':
                        #     firstLabelName = re['firstLabelName']
                        # else:
                        #     firstLabelName = firstLabelName + '|' + re['firstLabelName']
                        # if sentence == '':
                        #     sentence = re['sentence']
                        # else:
                        #     sentence = sentence + '|' + re['sentence']
                        if re['cur'].isdigit():
                            f1 = open(files_path + '_audit.csv', mode='a+', encoding='utf-8-sig')
                            f1.write(file + ',' + re['firstLabelName'] + ',' + re['cur'] + '\t,' + ree.sub(',', '，', re['sentence']) + '\n')
                            f1.close()
                        else:
                            f1 = open(files_path + '_audit.csv', mode='a+', encoding='utf-8-sig')
                            f1.write(file + ',' + re['firstLabelName'] + ',' + re['cur'] + ',' + ree.sub(',', '，', re['sentence']) + '\n')
                            f1.close()
                    time.sleep(4)
            # if sentence != '':
            #     f1 = open(files_path + '_audit.csv', mode='a+', encoding='utf-8-sig')
            #     f1.write(file + ',' + firstLabelName + ',' + cur + ',' + sentence + '\n')
            #     f1.close()
            if not rr is None:
                f2 = open(files_path + '_statistics.csv', mode='a+', encoding='ANSI')
                f2.write(file + ',' + str(len(content)) + '\n')
                f2.close()
            print(len(files)-int(files.index(file)))
            # time.sleep(.1)




