#!/usr/bin/env python
# coding=utf-8
import time
import traceback
import uiautomator2 as u2
import hmac
import base64
import os
import re
import pandas
import requests
import time
import json
from urllib.parse import quote
# API接入方的appid
from src.utils.MinioUtils import Helper

appid = 'BtFZZ85p'
# API接入方的密钥
secret_key = 'a795b70ba290cc3c688b41dc842a8d93dbbb0736'
# 接口host
url = 'https://pgc.botsmart.cn/api'


def encrypt_data(secret_key, data):
    # 将密钥和字符串转换为字节串
    secret_key_bytes = secret_key.encode()
    data_bytes = data.encode()

    # 使用密钥进行加密
    hmac_sha1 = hmac.new(secret_key_bytes, data_bytes, digestmod='sha1')
    digest = hmac_sha1.digest()

    # 将加密结果转换为 Base64 编码的字符串
    encrypted_data = base64.b64encode(digest).decode()

    return encrypted_data

def send_request(url, api_path, request_body, appid, secret_key):
    timestamp = str(int(time.time() * 1000))
    nonce = timestamp[:10]
    # 拼装加密前的明文字符串，拼装规则：uri(接口地址) + json_body(请求体报文) + timestamp(毫秒) + nonce(请求流水号)
    data = api_path + json.dumps(request_body) + timestamp + nonce
    # 调用加密方法获取加密后的密文，并做url_encode处理
    encrypted_data = encrypt_data(secret_key, data)
    encode_sign = quote(encrypted_data, safe="")
    # 发送 HTTP 请求并设置请求头
    headers = {'Content-Type': 'application/json',
               'Accept': 'application/json',
               'apiSign-appId': appid,
               'apiSign-timestamp':  timestamp,
               'apiSign-nonce': nonce,
               'apiSign-sign': encode_sign}
    response = requests.post(url + api_path, headers=headers, json=request_body)
    # 处理响应
    print(response.text)
    return response

def audit(content, callBack):
    request_body = {
        "content": content,
        "callBack": callBack
    }
    # 发送接口请求
    resp = send_request(url, '/open/v1/counterfeitAsync', request_body, appid, secret_key)
    return resp

def counterResult(openId):
    request_body = {
        "openId": openId
    }
    # 发送接口请求
    resp = send_request(url, '/open/v1/counterfeitDetResult', request_body, appid, secret_key)
    return resp

def intercept(str1):
    str_list = []
    i = 1
    while len(str1) > 10000:

        if str1[10000-i] in '\n|\t|\r|，|。|？':
            str_list.append(str1[0:10000-i])
            str1 = str1[10000-i:-1]
            if len(str1) < 10000:
                str_list.append(str1)
            i = 0
        i += 1
    return str_list


if __name__ == '__main__':
    # 审核
    id = "5286ddbf"
    d = u2.connect(id)
    flag = True
    d.watcher.when(xpath='//*[@resource-id="shareNav"]').when(xpath='//*[@resource-id="shareNav"]/android.widget.TextView[1]').click()
    d.watcher.when(xpath='//*[@text="亲爱的用户，感谢您使用美团！"]').when(xpath='//*[@resource-id="com.sankuai.meituan:id/permission_agree_btn"]').click()
    d.watcher.when(xpath='//*[@text="全场1分起 新人专享"]').when(xpath='//*[@resource-id="com.sankuai.meituan:id/back_arrow"]').click()
    d.watcher.when(xpath='//*[@text="现金秒到账"]').when(xpath='//*[@resource-id="com.sankuai.meituan:id/top_cover_fragment_container"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]').click()
    d.watcher.when(xpath='//*[@text="连续签到"]').when(xpath='//*[@resource-id="com.sankuai.meituan:id/top_cover_fragment_container"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[2]/android.view.ViewGroup[2]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.widget.ImageView[1]').click()
    d.watcher.when(xpath='//*[@resource-id="com.sankuai.meituan:id/iv_head_img" and @content-desc="天降红包已到账"]').when(xpath='//*[@resource-id="com.sankuai.meituan:id/bottom_center_close_button"]').click()
    d.watcher.when(xpath='//*[@text="立即关注"]').when(xpath='//*[@resource-id="com.sankuai.meituan:id/frag_son_container"]/android.widget.FrameLayout[2]/android.widget.FrameLayout[1]/android.view.ViewGroup[10]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.widget.ImageView[1]').press("back")
    d.watcher.start()
    data = []
    data1 = []
    pattern = re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')
    n = 0
    while flag:
        try:
            # 打开美团app
            d.app_start('com.sankuai.meituan', stop=True)
            # 等待加载
            d.sleep(10)
            d.xpath('//*[@resource-id="com.sankuai.meituan:id/search_edit_flipper_container"]').click()
            d.sleep(1)
            d.send_keys("直播", clear=True)
            d.sleep(1)
            d.send_action("search")
            d.sleep(2)
            d.xpath('//*[@resource-id="live-animation"]').click()
            d.sleep(2)
            while flag:
                # 获取直播链接
                d.xpath('//*[@content-desc="返回"]/parent::android.view.ViewGroup/parent::android.view.ViewGroup/preceding-sibling::android.view.ViewGroup[1]/android.widget.ImageView').click()
                d.sleep(1)
                d.xpath('//*[@text="复制链接"]').click()
                d.sleep(1)
                Positioning = re.findall(pattern, d.clipboard)[0]
                # # 开启录屏
                d.app_start('com.miui.screenrecorder')
                d.xpath('//*[@resource-id="com.miui.screenrecorder:id/screen_record_switch"]').click()
                d.sleep(15)
                # 关闭录屏
                d.app_start('com.miui.screenrecorder')
                d.xpath('//*[@resource-id="com.miui.screenrecorder:id/screen_record_switch"]').click()
                # # 录屏15秒
                # save_name = time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.mp4'
                # d.shell('screenrecord --size 1080x2340 --time-limit 15 /sdcard/DCIM/ScreenRecorder/' + save_name)
                d.sleep(2)
                save_name = d.shell('cd /sdcard/DCIM/ScreenRecorder/ \n ls', timeout=60).output.split('\n')[-2]
                d.pull('/sdcard/DCIM/ScreenRecorder/' + save_name, 'C:/xtc/' + save_name)
                mp4_url = Helper.save_file_by_path(save_name='/AI/' + save_name, file_name=r'C:/xtc/' + save_name)
                f2 = open(r'C:\Users\<USER>\Desktop\虚假信息检测.csv', mode='a+', encoding='ANSI')
                f2.write('\"' + mp4_url + '\",\"' + Positioning + '\"\n')
                f2.close()
                # aud_json = json.loads(audit(mp4_url, Positioning).text)
                # while not 'msg' in aud_json:
                #     d.sleep(2)
                #     while aud_json['msg'] != '成功':
                #         aud_json = json.loads(audit(mp4_url, Positioning).text)
                # openId = aud_json['data']['openId']
                # data.append([Positioning, openId, '', '', mp4_url])
                # n = n + 1
                # if len(data) >= 55:
                #     for i in range(0, 50):
                #         if data[i][3] != '':
                #             continue
                #         ai_result = json.loads(counterResult(data[i][1]).text)
                #         while ai_result['code'] == 500000:
                #             d.sleep(2)
                #             ai_result = json.loads(counterResult(data[i][1]).text)
                #         if ai_result['data']['status'] == 2:
                #             if ai_result['data']['result'] == 1:
                #                 data[i][2] = '真实'
                #             else:
                #                 data[i][2] = '不真实'
                #             data[i][3] = str(ai_result['data']['probability'])
                #         d.sleep(1)
                #     data1 = []
                #     for da in data:
                #         if da[3] == '':
                #             data1.append(da)
                #         else:
                #             f2 = open(r'C:\Users\<USER>\Desktop\result.csv', mode='a+', encoding='ANSI')
                #             f2.write('\"'+da[0]+'\",\"'+da[1]+'\",\"'+da[2]+'\",\"'+da[3]+'\",\"'+da[4]+'\"\n')
                #             f2.close()
                #             # Helper.delete(da[4])
                #     data = []
                #     for da1 in data1:
                #         data.append(da1)
                d.swipe_ext("up")
                d.sleep(2)
                if n >= 100:
                    flag = False
        except Exception as e:
            traceback.print_exc()
            # d.app_stop('com.miui.screenrecorder')
            d.sleep(2)
    d.app_stop('com.sankuai.meituan')
    d.sleep(120)
    # while len(data) > 0:
    #     for i in range(0, len(data)):
    #         ai_result = json.loads(counterResult(data[i][1]).text)
    #         while ai_result['code'] == 500000:
    #             d.sleep(2)
    #             ai_result = json.loads(counterResult(data[i][1]).text)
    #         if ai_result['data']['status'] == 2:
    #             if ai_result['data']['result'] == 1:
    #                 data[i][2] = '真实'
    #             else:
    #                 data[i][2] = '不真实'
    #             data[i][3] = str(ai_result['data']['probability'])
    #         d.sleep(1)
    #     data1 = []
    #     for da in data:
    #         if da[3] == '':
    #             data1.append(da)
    #         else:
    #             f2 = open(r'C:\Users\<USER>\Desktop\result.csv', mode='a+', encoding='ANSI')
    #             f2.write('\"' + da[0] + '\",\"' + da[1] + '\",\"' + da[2] + '\",\"' + da[3] + '\",\"' + da[4] + '\"\n')
    #             f2.close()
    #             # Helper.delete(da[4])
    #     data = []
    #     for da1 in data1:
    #         data.append(da1)
    #     d.sleep(60)