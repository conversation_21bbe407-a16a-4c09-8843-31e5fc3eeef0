
import pandas
import xlwings
import pythoncom
import hashlib

def encryption(signature_data):
    signature_md5 = hashlib.md5()
    signature_md5.update(signature_data.encode("utf-8"))
    signature = signature_md5.hexdigest()
    return signature
def data_write(file_path, data):
    pythoncom.CoInitialize()
    app = xlwings.App(visible=False, add_book=False)
    # Excel工作簿显示警告,不显示
    app.display_alerts = False
    # 工作簿屏幕更新,不更新
    app.screen_updating = False
    wb = app.books.add()
    sht = wb.sheets('sheet1')
    # 将数据写入第 i 行，第 j 列
    for da in data:
        if len(str(da[7])) > 30000:
            txt_name = encryption(da[3]+da[4]+da[7])+'.txt'
            t = open(path+txt_name, mode='a+', encoding='utf-8-sig')
            t.write(da[7])
            t.close()
            da[7] = txt_name
            sht.range('A' + str(data.index(da)+1)).value = da
            sht.range('H' + str(data.index(da)+1)).add_hyperlink(address='./'+txt_name, text_to_display=txt_name)
        else:
            sht.range('A' + str(data.index(da)+1)).value = da
    wb.save(file_path)  # 保存文件
    wb.close()
    app.kill()
    pythoncom.CoUninitialize()


if __name__ == '__main__':
    path = r'C:/Users/<USER>/Desktop/学习园地/国家政策_国务院文件库_国务院政策文件库_国务院文件_国务院部门文件/'
    data = [['分类一', '分类二', '标题', '链接', '发布时间', '数据库', '信息来源', '正文', '索引号', '发文机关', '发文字号', '主题分类', '公文种类', '成文日期', '主题词', '作者', '编译', '分类', '标签']]
    data1 = pandas.read_csv(path + '国家政策_国务院文件库_国务院政策文件库_国务院文件_国务院部门文件.csv').values.tolist()
    data2 = pandas.read_csv(path + '国家政策_国务院文件库_国务院政策文件库_国务院文件_国务院部门文件(1).csv').values.tolist()
    for daa1 in data1:
        if '分类一' == daa1[0]:
            continue
        data.append(daa1)
    for daa2 in data2:
        if '分类一' == daa2[0]:
            continue
        data.append(daa2)
    for daa in data:
        if '分类一' == daa[0]:
            continue
        if isinstance(daa[4], str):
            daa[4] = daa[4]+'\t'
        if isinstance(daa[13], str):
            daa[13] = daa[13]+'\t'

    data_write(file_path=path+'国家政策_国务院文件库_国务院政策文件库_国务院文件_国务院部门文件.xlsx', data=data)
