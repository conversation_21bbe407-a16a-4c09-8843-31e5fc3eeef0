# -*- coding: utf-8 -*-
# !/usr/bin/env python
# coding=utf-8
import hmac
import base64
import os
import re
import traceback

import pandas
import requests
import time
import json
from urllib.parse import quote
# API接入方的appid
appid = '4gcblRia'
# API接入方的密钥
secret_key = 'ed35ce6ea44c0640a3507a7751ec6bab33a2a84f'
# 接口host
url = 'https://pgc.botsmart.cn/api'


def encrypt_data(secret_key, data):
    # 将密钥和字符串转换为字节串
    secret_key_bytes = secret_key.encode()
    data_bytes = data.encode()

    # 使用密钥进行加密
    hmac_sha1 = hmac.new(secret_key_bytes, data_bytes, digestmod='sha1')
    digest = hmac_sha1.digest()

    # 将加密结果转换为 Base64 编码的字符串
    encrypted_data = base64.b64encode(digest).decode()

    return encrypted_data

def send_request(url, api_path, request_body, appid, secret_key):
    timestamp = str(int(time.time() * 1000))
    # nonce = timestamp[:10]
    nonce = timestamp[-10:]
    # 拼装加密前的明文字符串，拼装规则：uri(接口地址) + json_body(请求体报文) + timestamp(毫秒) + nonce(请求流水号)
    data = api_path + json.dumps(request_body) + timestamp + nonce
    # 调用加密方法获取加密后的密文，并做url_encode处理
    encrypted_data = encrypt_data(secret_key, data)
    encode_sign = quote(encrypted_data, safe="")
    # 发送 HTTP 请求并设置请求头
    headers = {'Content-Type': 'application/json',
               'Accept': 'application/json',
               'apiSign-appId': appid,
               'apiSign-timestamp':  timestamp,
               'apiSign-nonce': nonce,
               'apiSign-sign': encode_sign}
    try:
        response = requests.post(url + api_path, headers=headers, json=request_body, timeout=30)
    except:
        print('审核失败!')
        return None
    # 处理响应
    print(response.text)
    return response

def audit(content):
    request_body = {
        "url": content,
        "rules": [
            104002, 104003, 104004, 104005, 104006, 104007, 104008, 104009, 104010, 104013, 104016, 104047, 104038, 104042, 104021, 104015, 104017,
            104018, 104019, 104020, 104040, 104043, 104022, 104023, 104025, 104030, 104029, 104026, 104027, 104033, 104014, 104044, 104048, 104041
        ]
    }
    # 发送接口请求
    resp = send_request(url, '/open/v2/imageAudit', request_body, appid, secret_key)
    return resp

def intercept(str1):
    str_list = []
    i = 1
    while len(str1) > 9000:

        if str1[9000-i] in '\n|\t|\r|，|。|？':
            str_list.append(str1[0:9000-i])
            str1 = str1[9000-i:-1]
            if len(str1) < 9000:
                str_list.append(str1)
            i = 0
        i += 1
    return str_list


if __name__ == '__main__':
    # 审核
    # 方案id
    headerss = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102'
    }
    sss = audit('https://wespypic2015.afunapp.com/FnjlTKYxCpNM1mVF7HCcYGkA9tBM?w=640')
    files_names = ['包图网广告设计']
    for files_name in files_names:
        files_path = 'C:/Users/<USER>/Desktop/'+files_name
        files = os.listdir(files_path)
        datas = [['图片链接', '图片标题链接', '审核结果']]
        for file in files:
            audit_datas = pandas.read_csv(files_path+'/'+file, encoding='utf-8', header=None).values.tolist()
            for audit_data in audit_datas:
                try:
                    if '图片链接' in audit_data[0]:
                        audit_data.append('audit')
                        print('audit')
                        continue
                    if '.gif' in audit_data[0]:
                        audit_data.append('跳过')
                        datas.append(audit_data)
                        print('跳过')
                        continue
                    base_data = base64.b64encode(requests.get(re.sub('﻿', '', audit_data[0]), headers=headerss).content).decode()
                    re_text = audit(content=base_data)
                    res = json.loads(re_text.text)
                    if 'code' in res:
                        code = res['code']
                    else:
                        code = 1
                    result = ''
                    for r in res['data']['result']:
                        if result == '':
                            result = r['labelName']
                        else:
                            result = result + ';' + r['labelName']
                    audit_data.append(result)
                    datas.append(audit_data)
                    print(result)
                except Exception as e:
                    traceback.print_exc()
                    audit_data.append('异常')
                    datas.append(audit_data)
                    print('异常')
        df = pandas.DataFrame(datas[1:], columns=datas[0])
        df.to_csv(files_path + '.csv', index=False, encoding='utf-8-sig')




