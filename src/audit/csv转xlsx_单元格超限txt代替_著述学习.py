import re

import pandas
import xlwings
import pythoncom
import hashlib

def encryption(signature_data):
    signature_md5 = hashlib.md5()
    signature_md5.update(signature_data.encode("utf-8"))
    signature = signature_md5.hexdigest()
    return signature
def data_write(file_path, data):
    pythoncom.CoInitialize()
    app = xlwings.App(visible=False, add_book=False)
    # Excel工作簿显示警告,不显示
    app.display_alerts = False
    # 工作簿屏幕更新,不更新
    app.screen_updating = False
    wb = app.books.add()
    sht = wb.sheets('sheet1')
    # 将数据写入第 i 行，第 j 列
    for da in data:
        if len(str(da[7])) > 30000:
            txt_name = encryption(da[3]+da[4]+da[7])+'.txt'
            t = open(path+txt_name, mode='a+', encoding='utf-8-sig')
            t.write(da[7])
            t.close()
            da[7] = txt_name
            sht.range('C' + str(data.index(da)+1)).number_format = '@'
            sht.range('F' + str(data.index(da)+1)).number_format = '@'
            sht.range('A' + str(data.index(da)+1)).value = da
            sht.range('I' + str(data.index(da)+1)).add_hyperlink(address='./'+txt_name, text_to_display=txt_name)
        else:
            sht.range('C' + str(data.index(da)+1)).number_format = '@'
            sht.range('F' + str(data.index(da)+1)).number_format = '@'
            sht.range('A' + str(data.index(da)+1)).value = da
    wb.save(file_path)  # 保存文件
    wb.close()
    app.kill()
    pythoncom.CoUninitialize()


if __name__ == '__main__':
    path = r'C:/Users/<USER>/Desktop/jt/'
    # data = [['标题', '链接', '发布时间', '数据库', '信息来源', '标签1', '标签2', '标签3', '正文', '作者', '编译', '分类', '标签']]
    data = pandas.read_csv(path + '思修学习_人民论坛.csv').values.tolist()
    for daa in data:
        if '标题' == daa[0]:
            continue
        # re.sub('-00', '-08', daa[2])
        print(daa[2])

    data_write(file_path=path+'思修学习_人民论坛.xlsx', data=data)
