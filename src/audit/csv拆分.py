import csv

import pandas
ss = ''
data = []
# data = pandas.read_csv('C:/Users/<USER>/Desktop/case1-1_audit.csv')
with open('C:/Users/<USER>/Desktop/case1_audit.csv', 'r', encoding='utf-8') as csvfile:
    reader = csv.reader(csvfile)
    for row in reader:
        if len(row) != 4:
            print(len(row))
            print(row)
            for i in range(3, len(row)):
                if ss == '':
                    ss = row[i]
                else:
                    ss = ss + '，' + row[i]
            row = [row[0], row[1], row[2], ss]
        data.append(row)


for d in data:
    das = d[1].split('|')
    if das[0] == '问题分类':
        f1 = open('C:/Users/<USER>/Desktop/case1_audit_拆分.csv', mode='a+', encoding='utf-8-sig')
        f1.write(d[0] + ',' + d[1] + ',' + d[2] + ',' + d[3] + '\n')
        f1.close()
        continue
    for da in das:
        f1 = open('C:/Users/<USER>/Desktop/case1_audit_拆分.csv', mode='a+', encoding='utf-8-sig')
        if d[2].split('|')[das.index(da)].isdigit():
            f1.write(d[0] + ',' + da + ',' + d[2].split('|')[das.index(da)] + '\t,' + d[3].split('|')[das.index(da)] + '\n')
        else:
            f1.write(d[0] + ',' + da + ',' + d[2].split('|')[das.index(da)] + ',' + d[3].split('|')[das.index(da)] + '\n')
        f1.close()

print()