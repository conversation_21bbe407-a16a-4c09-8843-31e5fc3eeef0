import csv

import pandas
ss = ''
data = []
# data = pandas.read_csv('C:/Users/<USER>/Desktop/case1-1_audit.csv')
with open('C:/Users/<USER>/Desktop/case1_audit_拆分.csv', 'r', encoding='utf-8') as csvfile:
    reader = csv.reader(csvfile)
    for row in reader:
        if row[2].isdigit():
            f1 = open('C:/Users/<USER>/Desktop/案例1_audit_拆分.csv', mode='a+', encoding='utf-8-sig')
            f1.write(row[0] + ',' + row[1] + ',' + row[2] + '\t,' + row[3] + '\n')
            f1.close()
        else:
            f1 = open('C:/Users/<USER>/Desktop/案例1_audit_拆分.csv', mode='a+', encoding='utf-8-sig')
            f1.write(row[0] + ',' + row[1] + ',' + row[2] + ',' + row[3] + '\n')
            f1.close()
print()