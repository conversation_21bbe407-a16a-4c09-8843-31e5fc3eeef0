
import uiautomator2 as u2

import xlwings
import pythoncom

def data_write(file_path, data):
    pythoncom.CoInitialize()
    app = xlwings.App(visible=False, add_book=False)
    # Excel工作簿显示警告,不显示
    app.display_alerts = False
    # 工作簿屏幕更新,不更新
    app.screen_updating = False
    wb = app.books.add()
    sht = wb.sheets('sheet1')
    # 将数据写入第 i 行，第 j 列
    sht.range('A1').value = data
    wb.save(file_path)  # 保存文件
    wb.close()
    app.kill()
    pythoncom.CoUninitialize()



if __name__ == '__main__':
    d = u2.connect('4TS8PRSG9DZPGYD6')
    # d.watcher.when(xpath='//*[@text="欢迎登录"]').when(xpath='//*[@resource-id="com.wdit.shrmtyp:id/iv_close"]').click()
    # d.watcher.when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/tv_top_vod_name"]').when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/confirm_button"]').click()
    # d.watcher.when(xpath='//*[@text="埋堆堆青少年模式"]').when(xpath='//*[@resource-id="com.tvbc.maiduidui:id/tv_ok"]').click()
    data = [['APP名称', '公司']]
    flag = True
    while flag:
        elements = d.xpath('//*[@resource-id="com.heytap.market:id/tv_name"]').all()
        for element in elements:
            bt = element.text
            element.click()
            d.sleep(2)
            d.swipe(0.4, 0.8, 0.4, 0.15, 0.4)
            d.sleep(2)
            gs = d.xpath('//*[@resource-id="com.heytap.market:id/company"]').text
            print(bt+','+gs)
            data.append([bt, gs])
            d.press('back')
            d.sleep(2)
        if len(d.xpath('//*[@resource-id="com.heytap.market:id/no_more_text"]').all()) > 0:
            flag = False
            break
        d.swipe(0.4, 0.85, 0.4, 0.15, 0.3)
        d.sleep(2)
    data_write(file_path=r'C:\Users\<USER>\Desktop\oppo短视频APP采集.xlsx', data=data)
