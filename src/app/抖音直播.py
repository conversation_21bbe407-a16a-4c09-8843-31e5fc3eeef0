#!/usr/bin/env python
# coding=utf-8
import time
import traceback
import uiautomator2 as u2

if __name__ == '__main__':
    id = "d4f87bf7"
    d = u2.connect(id)
    flag = True
    # 开启录屏
    d.app_start('com.miui.screenrecorder')
    d.xpath('//*[@resource-id="com.miui.screenrecorder:id/screen_record_switch"]').click()
    while flag:
        try:
            # 运行脚本
            d.app_start('com.ss.android.ugc.aweme', '.splash.SplashActivity', stop=True)
            d.watcher.when(xpath='//*[@text="个人信息保护指引"]').when(xpath='//*[@text="同意"]').click()
            d.watcher.when(xpath='//*[@text="抖音 想访问你的通讯录"]').when(xpath='//*[@text="拒绝"]').click()
            d.watcher.start()
            time.sleep(5)
            d.xpath('//*[@text="我"]').click()
            time.sleep(2)
            d.xpath('//*[@text="关注"]').click()
            time.sleep(2)
            d.xpath('//*[@resource-id="com.ss.android.ugc.aweme:id/ayp"]').click()
            time.sleep(2)
            while len(d.xpath('//*[@resource-id="com.ss.android.ugc.aweme:id/v7_" or @resource-id="com.ss.android.ugc.aweme:id/c74"]').all()) > 0:
                # 截图
                jt_image = d.screenshot()
                jt_image.save('C:/xtc/' + time.strftime("%Y%m%d_%H%M%S", time.localtime()) + '.jpg')
                time.sleep(2)
                if len(d.xpath('//*[@resource-id="com.ss.android.ugc.aweme:id/l_y"]').all()) == 1:
                    d.press('back')
                    time.sleep(2)
                    zb_flag = False
                    while zb_flag == False:
                        d.press('back')
                        time.sleep(10)
                        d.xpath('//*[@text="关注"]').click()
                        time.sleep(2)
                        if len(d.xpath('//*[@resource-id="com.ss.android.ugc.aweme:id/ayp"]').all()) >= 1:
                            d.xpath('//*[@resource-id="com.ss.android.ugc.aweme:id/ayp"]').click()
                            time.sleep(2)
                            zb_flag = True
        except Exception as e:
            traceback.print_exc()
            # 关闭录屏并导出
            d.app_start('com.miui.screenrecorder')
            d.xpath('//*[@resource-id="com.miui.screenrecorder:id/screen_record_switch"]').click()
            d.sleep(10)
            save_name = d.shell('cd /sdcard/DCIM/ScreenRecorder/ \n ls', timeout=60).output.split('\n')[-2]
            paths = d.pull('/sdcard/DCIM/ScreenRecorder/' + save_name, 'C:/xtc/' + save_name)
            d.sleep(10)
            # d.shell('cd /sdcard/DCIM/ScreenRecorder/ \n rm ' + save_name, timeout=60)
            d.sleep(40)
    # 关闭录屏并导出
    d.app_start('com.miui.screenrecorder')
    d.xpath('//*[@resource-id="com.miui.screenrecorder:id/screen_record_switch"]').click()
    d.sleep(10)
    save_name = d.shell('cd /sdcard/DCIM/ScreenRecorder/ \n ls', timeout=60).output.split('\n')[-2]
    paths = d.pull('/sdcard/DCIM/ScreenRecorder/' + save_name, 'C:/xtc/' + save_name)
    time.sleep(3)
    d.app_stop('com.ss.android.ugc.aweme')
    # pr = os.popen("adb -s 30a3fea3 shell screenrecord /sdcard/testsssss.mp4")
    # pr.readline()
    # time.sleep(5)
    # os.popen('^C').readline()
