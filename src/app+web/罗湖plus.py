import json
import os
import time
import traceback
from typing import Optional

import requests
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium import webdriver
import uiautomator2 as u2
import xlwings
import pythoncom

from src.utils import DriverUtils


def _findElement(driver: Optional[WebDriver],
                 selctor: Optional[str],
                 index: Optional[int] = 1,
                 timeout: float = None):
    timeout_catch = driver.capabilities['timeouts']['implicit']
    if timeout:
        driver.implicitly_wait(timeout)
    else:
        driver.implicitly_wait(timeout_catch)

    element = driver.find_elements(By.XPATH, selctor)[index - 1]

    driver.implicitly_wait(timeout_catch)
    return element


def _findElements(driver: Optional[WebDriver],
                  selctor: Optional[str],
                  timeout: float = None):
    timeout_catch = driver.capabilities['timeouts']['implicit']
    if timeout:
        driver.implicitly_wait(timeout)
    else:
        driver.implicitly_wait(timeout_catch)

    elements = driver.find_elements(By.XPATH, selctor)

    driver.implicitly_wait(timeout_catch)
    return elements

def data_write3(file_path, data):
    pythoncom.CoInitialize()
    app = xlwings.App(visible=False, add_book=False)
    # Excel工作簿显示警告,不显示
    app.display_alerts = False
    # 工作簿屏幕更新,不更新
    app.screen_updating = False
    wb = app.books.add()
    sht = wb.sheets('sheet1')
    # 将数据写入第 i 行，第 j 列
    sht.range('A1').value = data
    wb.save(file_path)  # 保存文件
    wb.close()
    app.kill()
    pythoncom.CoUninitialize()


def data_read2(file_path, table_name):
    pythoncom.CoInitialize()
    app = xlwings.App(visible=False, add_book=False)
    # Excel工作簿显示警告,不显示
    app.display_alerts = False
    # 工作簿屏幕更新,不更新
    app.screen_updating = False
    wb = app.books.open(file_path)
    data = wb.sheets[table_name]
    # 获取已编辑的矩形区域,最底部且最右侧的单元格
    last_cell = data.used_range.last_cell
    # 最大行数
    last_row = last_cell.row
    # 最大列数
    last_col = last_cell.column
    table = data.range((1, 1), (last_row, last_col)).value
    # 关闭工作簿
    wb.close()
    # 退出Excel
    app.quit()
    pythoncom.CoUninitialize()
    return table

def request(rwh, bt, url, sj, wb):
    s = {"taskNo": rwh, "modelName": "测试",
         "jsonArray": [{"标题": bt, "标题链接": url, "时间": sj, "内容": wb}]}
    payload = json.dumps(s)
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
    }
    re = requests.post("http://prod-patrol-info-dispatch.botsmart.cn:8106/task-info/pushData", payload,
                       headers=headers)
    a = re.text
    data = json.loads(a)
    print(data)

if __name__ == '__main__':
    id = '5286ddbf'
    rwh = 'XH-171409762304979'
    page = DriverUtils.Utils.get_page(port=9011, headless=False, user_data_name='app巡查')
    d = u2.connect(id)
    tittle_list = []
    n = 0
    try:
        while True:
            elements = d.xpath('//*[@resource-id="com.luohunews:id/newsitem_title"]').all()
            for e in elements:
                bt = e.text
                sj = ''
                wb = ''
                if bt in tittle_list:
                    print(bt + ':   已存在')
                else:
                    e.click()
                    d.sleep(2)
                    d.xpath('//*[@resource-id="com.luohunews:id/newdetail_share"]').click()
                    d.sleep(1)
                    d.xpath('//*[@text="复制链接"]').click()
                    d.sleep(1)
                    url = d.clipboard
                    page.get(url)
                    # page.wait.load_start()
                    # page.wait.load_complete()
                    sj_element = page.ele(locator='xpath://span[@id="updatetime"]', timeout=3)
                    if not sj_element:
                        d.press("back")
                        continue
                    sj = sj_element.text
                    wb_element = page.ele(locator='xpath://div[@id="bottomContent"]', timeout=3)
                    if not wb_element:
                        d.press("back")
                        continue
                    wb = wb_element.text
                    tittle_list.append(bt)
                    d.press("back")
                    request(rwh, bt, url, sj, wb)
                    print(url)
                    d.sleep(1)
                    n = n + 1
            if n > 500:
                break
            d.swipe(0.4, 0.9, 0.4, 0.15, 0.4)
            d.sleep(1)
        message = {
            "msgtype": "text",
            "text": {
                "content": '任务号[XH-168059832410111],地址[app：今日六合] 采集已完成,请查收'
            }
        }
        message_json = json.dumps(message)
        send_message = requests.post(url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=23e9ea92-c18d-4752-a2c9-620f15df9aba', data=message_json, headers={"Content-Type": "application/json"})
        print(send_message.text)
        page.quit()
    except Exception as e:
        page.quit()