# coding: utf-8
#
import datetime

import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class amblr_collect:

    @staticmethod
    def sal():
        seed = "123456789abcdefghijklmnopqrstuvwxyz"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal1():
        seed = "1234567890"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def login(src):
        d.set_fastinput_ime(True)
        src = amblr_collect.sal()
        src1 = amblr_collect.sal1()
        d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_register"]').click()
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/et_register_username"]').click()
        d.clear_text()
        d.send_keys(src+'1')
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/et_register_password"]').click()
        d.press("back")
        d.sleep(1)
        d.clear_text()
        d.send_keys(src+'3')
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/et_register_telephone_number"]').click()
        d.clear_text()
        d.send_keys('185' + src1)
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/et_register_wx"]').click()
        d.clear_text()
        d.send_keys('185' + src1)
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_register_nextstep"]').click()

        while len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').all()) == 0:
            d.sleep(1)

    @staticmethod
    def collect(d):
        d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rl_account_deposit"]/android.widget.TextView[1]').click()
        d.sleep(1)
        for i in range(1, 5001):
            if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').all()) == 1:
                d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
                d.sleep(1)
                if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_register"]').all()) == 1:
                    amblr_collect.login(d)
                    d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
                    d.sleep(1)
                d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rl_account_deposit"]/android.widget.TextView[1]').click()
                d.sleep(1)
            if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_register"]').all()) == 1:
                amblr_collect.login(d)
                d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
                d.sleep(1)
                d.xpath(
                    '//*[@resource-id="com.tx.app.three.txyc:id/rl_account_deposit"]/android.widget.TextView[1]').click()
                d.sleep(1)
            d.xpath('//*[@text="云闪付"]').click()
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/et_pay_third_party_balance"]').click()
            d.clear_text()
            d.send_keys(str(200+i))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_pay_third_party_nextstep"]').click()
            amblr_collect.page(d)

            if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').all()) == 1:
                d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
                d.sleep(1)
                if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_register"]').all()) == 1:
                    amblr_collect.login(d)
                    d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
                    d.sleep(1)
                d.xpath(
                    '//*[@resource-id="com.tx.app.three.txyc:id/rl_account_deposit"]/android.widget.TextView[1]').click()
                d.sleep(1)
            d.xpath('//*[@text="快捷支付"]').click()
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/et_pay_third_party_balance"]').click()
            d.clear_text()
            d.send_keys(str(200 + i))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_pay_third_party_nextstep"]').click()
            amblr_collect.page(d)

            if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').all()) == 1:
                d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
                d.sleep(1)
                if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_register"]').all()) == 1:
                    amblr_collect.login(d)
                    d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
                    d.sleep(1)
                d.xpath(
                    '//*[@resource-id="com.tx.app.three.txyc:id/rl_account_deposit"]/android.widget.TextView[1]').click()
                d.sleep(1)
            d.xpath('//*[@text="银联支付"]').click()
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/et_pay_third_party_balance"]').click()
            d.clear_text()
            d.send_keys(str(200 + i))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_pay_third_party_nextstep"]').click()
            amblr_collect.page(d)
        return 1

    @staticmethod
    def page(d):
        t = 0
        while (
                len(d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[7]/android.view.View[2]/android.view.View[1]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]').all()) == 0):
            d.sleep(1)
            t += 1
            if (len(d.xpath('//android.webkit.WebView/android.view.View[1]/android.view.View[1]').all()) == 1) and (t >= 5):
                if '您在1分钟内限制提交2笔' in d.xpath('//android.webkit.WebView/android.view.View[1]/android.view.View[1]').get_text():
                    d.press("back")
                    d.sleep(10)
                    break
            if len(d.xpath('//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').all()) == 1:
                if '参数异常：页面跳转地址' in d.xpath('//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').get_text()  :
                    d.press("back")
                    d.sleep(10)
                    break
            if t >= 30:
                d.press("back")
                d.sleep(10)
                break
            if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_register"]').all()) == 1:
                amblr_collect.login(d)
                d.sleep(2)
                d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
                d.sleep(1)
                d.xpath(
                    '//*[@resource-id="com.tx.app.three.txyc:id/rl_account_deposit"]/android.widget.TextView[1]').click()
                d.sleep(1)
                break
            # # if len(d.xpath('//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').all()) == 1:
            # #     d.xpath('//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
            if len(d.xpath('//*[@resource-id="userForm"]/android.widget.EditText[1]').all()) == 1:
                d.xpath('//*[@resource-id="userForm"]/android.widget.EditText[1]').click()
                d.clear_text()
                d.send_keys("詹明德")
                d.xpath('//*[@text="提交"]').click()
            # # if len(d.xpath('//*[@text="请点击下方按钮安装"]').all()) == 1:
            # #     d.press("back")

        if len(d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]').all()) != 0:
            d.sleep(2)
            if len(d.xpath('//*[@resource-id="userForm"]/android.widget.EditText[1]').all()) == 1:
                d.xpath('//*[@resource-id="userForm"]/android.widget.EditText[1]').click()
                d.clear_text()
                d.send_keys("詹明德")
                d.xpath('//*[@text="提交"]').click()
            d.sleep(2)
            if len(d.xpath(
                    '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').all()) == 1:
                if '参数异常：页面跳转地址' in d.xpath(
                        '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').get_text():
                    d.press("back")
                    d.sleep(10)
                    return 0
            names = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]')
            d.sleep(2)
            if len(d.xpath(
                    '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').all()) == 1:
                if '参数异常：页面跳转地址' in d.xpath(
                        '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').get_text():
                    d.press("back")
                    d.sleep(10)
                    return 0
            name = names.text.replace(' ', '').replace('姓名：', '')
            d.sleep(2)
            if len(d.xpath(
                    '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').all()) == 1:
                if '参数异常：页面跳转地址' in d.xpath(
                        '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').get_text():
                    d.press("back")
                    d.sleep(10)
                    return 0
            print(name)
            ids = d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="app"]/android.view.View[9]/android.view.View[2]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210324178786252631', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square='无', address='https://blr88b.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(20)
            return 1

        if len(d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]').all()) != 0:
            d.sleep(10)
            names = d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="content"]/android.view.View[6]/android.view.View[3]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="content"]/android.view.View[5]/android.view.View[3]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210324178786252631', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square=square, address='https://blr88b.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1

        if len(d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[7]/android.view.View[2]/android.view.View[1]').all()) != 0:
            d.sleep(10)
            names = d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[7]/android.view.View[2]/android.view.View[1]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[5]/android.view.View[2]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[6]/android.view.View[2]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210324178786252631', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square=square, address='https://blr88b.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1
        return 1


    @staticmethod
    def sign_out():
        d.xpath('//*[@text="我"]').click()
        d.sleep(1)
        d.xpath('//*[@text="安全退出"]').click()

    @staticmethod
    def run(d):
        d.watcher.when(xpath='//*[@resource-id="com.tg.chess.alibaba.a459qpx:id/iv_close"]').click()
        # d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.start()
        # d.app_start('com.tg.chess.alibaba.a459qpx', stop=True)
        # d.sleep(10)
        amblr_collect.collect(d)
        # amdc_collect.sign_out()
        d.watcher.remove()

if __name__ == '__main__':
    # id = "CQSCLFMBQGQK6HIR"
    id = "3a699a47"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="com.uk.edi.c213:id/title_tex"]').when(xpath='//*[@resource-id="com.uk.edi.c213:id/iv_close"]').click()
    d.watcher.when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[1]/android.view.View[1]').when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[4]/android.view.View[1]/android.view.View[1]').click()
    d.watcher.start()
    # d.app_start('com.tx.app.three.txyc', stop=True)
    # d.sleep(10)
    d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/rb_login"]').click()
    d.sleep(3)
    if len(d.xpath('//*[@resource-id="com.tx.app.three.txyc:id/btn_register"]').all()) == 1:
        amblr_collect.login(d)
    amblr_collect.collect(d)
    # amdc_collect.sign_out()
    d.watcher.remove()
