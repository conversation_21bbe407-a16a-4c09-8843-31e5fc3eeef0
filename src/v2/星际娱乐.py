# coding: utf-8
#
import datetime

import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
from src.v2.IP import switchover_IP
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class xjyl_collect:
    def __init__(self, d):
        self.d = d

    @staticmethod
    def sal():
        seed = "123456789abcdefghijklmnopqrstuvwxyz"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal1():
        seed = "1234567890"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def login(d):
        d.set_fastinput_ime(True)
        src = xjyl_collect.sal()
        src1 = xjyl_collect.sal1()
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').click()
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_register_username"]').click()
        d.clear_text()
        d.send_keys(src+'1')
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_register_password"]').click()
        d.press("back")
        d.sleep(1)
        d.clear_text()
        d.send_keys(src+'3')
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_register_telephone_number"]').click()
        d.clear_text()
        d.send_keys('185' + src1)
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_register_wx"]').click()
        d.clear_text()
        d.send_keys('185' + src1)
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register_nextstep"]').click()

        while len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').all()) == 0:
            d.sleep(1)

    @staticmethod
    def collect(d):
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
        d.sleep(1)
        for i in range(1, 50):
            # if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').all()) == 1:
            #     d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #     d.sleep(1)
            #     if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
            #         xjyl_collect.login(d)
            #         d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #         d.sleep(1)
            #     d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
            #     d.sleep(1)
            # if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
            #     xjyl_collect.login(d)
            #     d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #     d.sleep(1)
            #     d.xpath(
            #         '//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
            #     d.sleep(1)
            # d.xpath('//*[@text="云闪付"]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            # d.clear_text()
            # d.send_keys(str(100+i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            # xjyl_collect.page(d)
            # d.xpath('//*[@text="云闪付"]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rlv_pay_third_party_channel"]/android.widget.RelativeLayout[2]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            # xjyl_collect.page(d)
            # if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').all()) == 1:
            #     d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #     d.sleep(1)
            #     if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
            #         xjyl_collect.login(d)
            #         d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #         d.sleep(1)
            #     d.xpath(
            #         '//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
            #     d.sleep(1)
            # d.xpath('//*[@text="支付宝支付"]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            # d.clear_text()
            # d.send_keys(str(1000 + i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            # xjyl_collect.page(d)

            # if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').all()) == 1:
            #     d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #     d.sleep(1)
            #     if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
            #         xjyl_collect.login(d)
            #         d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #         d.sleep(1)
            #     d.xpath(
            #         '//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
            #     d.sleep(1)
            # d.xpath('//*[@text="快捷支付"]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            # xjyl_collect.page(d)
            # if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').all()) == 1:
            #     d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #     d.sleep(1)
            #     if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
            #         xjyl_collect.login(d)
            #         d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #         d.sleep(1)
            #     d.xpath(
            #         '//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
            #     d.sleep(1)
            # d.xpath('//*[@text="快捷支付"]').click()
            # d.sleep(2)
            # d.xpath(
            #     '//*[@resource-id="com.tx.app.three.xjcb:id/rlv_pay_third_party_channel"]/android.widget.RelativeLayout[2]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            # xjyl_collect.page(d)

            # if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').all()) == 1:
            #     d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #     d.sleep(1)
            #     if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
            #         xjyl_collect.login(d)
            #         d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #         d.sleep(1)
            #     d.xpath(
            #         '//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
            #     d.sleep(1)
            # d.xpath('//*[@text="银联支付"]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            # xjyl_collect.page(d)
            if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').all()) == 1:
                d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
                d.sleep(1)
                if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
                    xjyl_collect.login(d)
                    d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
                    d.sleep(1)
                d.xpath(
                    '//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
                d.sleep(1)
            d.xpath('//*[@text="银联支付"]').click()
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rlv_pay_third_party_channel"]/android.widget.RelativeLayout[2]').click()
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            xjyl_collect.page(d)
            # if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').all()) == 1:
            #     d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #     d.sleep(1)
            #     if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
            #         xjyl_collect.login(d)
            #         d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
            #         d.sleep(1)
            #     d.xpath(
            #         '//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
            #     d.sleep(1)
            # d.xpath('//*[@text="银联支付"]').click()
            # d.sleep(2)
            # d.xpath(
            #     '//*[@resource-id="com.tx.app.three.xjcb:id/rlv_pay_third_party_channel"]/android.widget.RelativeLayout[3]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            # xjyl_collect.page(d)
            # d.xpath('//*[@text="银联支付"]').click()
            # d.sleep(2)
            # d.xpath(
            #     '//*[@resource-id="com.tx.app.three.xjcb:id/rlv_pay_third_party_channel"]/android.widget.RelativeLayout[4]').click()
            # d.sleep(2)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/et_pay_third_party_balance"]').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_pay_third_party_nextstep"]').click()
            # xjyl_collect.page(d)
        return 1

    @staticmethod
    def page(d):
        t = 0
        while (
                len(d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[7]/android.view.View[2]/android.view.View[1]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="show_name"]').all()) == 0) and (
                len(d.xpath('//android.widget.ListView/android.view.View[1]/android.view.View[2]').all()) == 0) and (
                len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) == 0) and (
                len(d.xpath(
                    '//*[@resource-id="app"]/android.view.View[7]/android.widget.ListView[1]/android.view.View[2]/android.view.View[2]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]').all()) == 0):
            d.sleep(1)
            t += 1
            if (len(d.xpath('//android.webkit.WebView/android.view.View[1]/android.view.View[1]').all()) == 1) and (t >= 5):
                if '您在1分钟内限制提交2笔' in d.xpath('//android.webkit.WebView/android.view.View[1]/android.view.View[1]').get_text():
                    d.press("back")
                    d.sleep(10)
                    break
            if len(d.xpath('//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').all()) == 1:
                if '参数异常：页面跳转地址' in d.xpath('//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').get_text()  :
                    d.press("back")
                    d.sleep(10)
                    break
            if t >= 30:
                d.press("back")
                d.sleep(10)
                if len(d.xpath('//*[@text="快捷支付"]').all()) == 0:
                    d.press("back")
                    d.sleep(10)
                break
            if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
                xjyl_collect.login(d)
                d.sleep(2)
                d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
                d.sleep(1)
                d.xpath(
                    '//*[@resource-id="com.tx.app.three.xjcb:id/rl_account_deposit"]/android.widget.TextView[1]').click()
                d.sleep(1)
                break
            # # if len(d.xpath('//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').all()) == 1:
            # #     d.xpath('//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
            if len(d.xpath('//*[@resource-id="userForm"]/android.widget.EditText[1]').all()) == 1:
                d.xpath('//*[@resource-id="userForm"]/android.widget.EditText[1]').click()
                d.clear_text()
                d.send_keys("詹明德")
                d.xpath('//*[@text="提交"]').click()
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.clear_text()
                d.send_keys("詹明德")
                d.xpath('//*[@text="提交付款"]').click()
            # # if len(d.xpath('//*[@text="请点击下方按钮安装"]').all()) == 1:
            # #     d.press("back")

        if len(d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]').all()) != 0:
            d.sleep(2)
            if len(d.xpath('//*[@resource-id="userForm"]/android.widget.EditText[1]').all()) == 1:
                d.xpath('//*[@resource-id="userForm"]/android.widget.EditText[1]').click()
                d.clear_text()
                d.send_keys("詹明德")
                d.xpath('//*[@text="提交"]').click()
            d.sleep(2)
            if len(d.xpath(
                    '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').all()) == 1:
                if '参数异常：页面跳转地址' in d.xpath(
                        '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').get_text():
                    d.press("back")
                    d.sleep(10)
                    return 0
            names = d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]')
            d.sleep(2)
            if len(d.xpath(
                    '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').all()) == 1:
                if '参数异常：页面跳转地址' in d.xpath(
                        '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').get_text():
                    d.press("back")
                    d.sleep(10)
                    return 0
            name = names.text.replace(' ', '').replace('姓名：', '')
            d.sleep(2)
            if len(d.xpath(
                    '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').all()) == 1:
                if '参数异常：页面跳转地址' in d.xpath(
                        '//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]/android.widget.FrameLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]').get_text():
                    d.press("back")
                    d.sleep(10)
                    return 0
            print(name)
            ids = d.xpath('//*[@resource-id="app"]/android.view.View[3]/android.view.View[2]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\星际娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210322155844499760', dname=name,
                             brandname='星际娱乐', newest_at=newest_at,
                             square='', address='https://88w6z.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(20)
            return 1

        if len(d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[7]/android.view.View[2]/android.view.View[1]').all()) != 0:
            d.sleep(10)
            names = d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[7]/android.view.View[2]/android.view.View[1]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[5]/android.view.View[2]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="app"]/android.view.View[1]/android.view.View[6]/android.view.View[2]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\星际娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210322155844499760', dname=name,
                             brandname='星际娱乐', newest_at=newest_at,
                             square=square, address='https://88w6z.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1

        # if len(d.xpath('//android.widget.ListView/android.view.View[1]/android.view.View[2]').all()) != 0:
        #     d.sleep(4)
        #     names = d.xpath('//android.widget.ListView/android.view.View[1]/android.view.View[2]')
        #     name = names.text.replace(' ', '').replace('姓名：', '')
        #     print(name)
        #     ids = d.xpath('//android.widget.ListView/android.view.View[3]/android.view.View[2]')
        #     id_1 = ids.text.replace(' ', '').replace('卡号：', '')
        #     print(id_1)
        #     banks = d.xpath('//android.widget.ListView/android.view.View[7]/android.view.View[2]')
        #     bank = banks.text.replace(' ', '').replace('银行：', '')
        #     print(bank)
        #     image = d.screenshot()
        #     image_path = "D:\\石溪科技银行卡\\采集卡号清单\\星际娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
        #     image.save(image_path)
        #     d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
        #     d.sleep(1)
        #     d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
        #     square = d.clipboard
        #     newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        #     payment.post_web(card_number=id_1, serial_number='20210322155844499760', dname=name,
        #                      brandname='星际娱乐', newest_at=newest_at,
        #                      square=square, address='https://88w6z.com/', bankname=bank,
        #                      image_path=image_path)
        #     # lis.append([bank, name, id_1, image_path])
        #     print("保存成功")
        #     d.press("back")
        #     d.sleep(1)
        #     d.press("back")
        #     d.sleep(1)
        #     d.press("back")
        #     d.sleep(20)
        #     return 1

        if len(d.xpath('//*[@resource-id="show_name"]').all()) != 0:
            d.sleep(10)
            names = d.xpath('//*[@resource-id="show_name"]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="show_pone"]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="show_bankname"]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\星际娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210322155844499760', dname=name,
                             brandname='星际娱乐', newest_at=newest_at,
                             square='', address='https://88w6z.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(20)
            return 1

        if len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) != 0:
            d.sleep(10)
            names = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[5]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\星际娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210322155844499760', dname=name,
                             brandname='星际娱乐', newest_at=newest_at,
                             square=square, address='https://88w6z.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1

        if len(d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]').all()) != 0:
            d.sleep(10)
            names = d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="content"]/android.view.View[6]/android.view.View[3]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="content"]/android.view.View[5]/android.view.View[3]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\星际娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210322155844499760', dname=name,
                             brandname='星际娱乐', newest_at=newest_at,
                             square=square, address='https://88w6z.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1
        if len(d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.widget.ListView[1]/android.view.View[2]/android.view.View[2]').all()) != 0:
            d.sleep(4)
            names = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.widget.ListView[1]/android.view.View[2]/android.view.View[2]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.widget.ListView[1]/android.view.View[1]/android.view.View[2]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.widget.ListView[1]/android.view.View[3]/android.view.View[2]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\太阳城\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210322155844499760', dname=name,
                             brandname='星际娱乐', newest_at=newest_at,
                             square=square, address='https://88w6z.com/', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1
        return 1


    @staticmethod
    def sign_out():
        d.xpath('//*[@text="我"]').click()
        d.sleep(1)
        d.xpath('//*[@text="安全退出"]').click()

    def run(self):
        d = self.d
        d.watcher.when(xpath='//*[@resource-id="com.uk.edi.c213:id/title_tex"]').when(
            xpath='//*[@resource-id="com.uk.edi.c213:id/iv_close"]').click()
        d.watcher.when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[1]/android.view.View[1]').when(
            xpath='//*[@resource-id="layui-layer1"]/android.view.View[4]/android.view.View[1]/android.view.View[1]').click()
        d.watcher.when(xpath='//*[@text="重要提示："]').when(xpath='//*[@text="我知道了"]').click()
        d.watcher.start()
        d.app_start('com.tx.app.three.xjcb', stop=True)
        d.sleep(10)
        d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
        d.sleep(3)
        if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
            xjyl_collect.login(d)
        xjyl_collect.collect(d)
        # amdc_collect.sign_out()
        d.watcher.remove()
        ip = switchover_IP(d)
        ip.run()

if __name__ == '__main__':
    # id = "CQSCLFMBQGQK6HIR"
    id = "3a699a47"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="com.uk.edi.c213:id/title_tex"]').when(xpath='//*[@resource-id="com.uk.edi.c213:id/iv_close"]').click()
    d.watcher.when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[1]/android.view.View[1]').when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[4]/android.view.View[1]/android.view.View[1]').click()
    d.watcher.when(xpath='//*[@text="重要提示："]').when(xpath='//*[@text="我知道了"]').click()
    d.watcher.start()
    d.app_start('com.tx.app.three.xjcb', stop=True)
    d.sleep(10)
    d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/rb_login"]').click()
    d.sleep(3)
    if len(d.xpath('//*[@resource-id="com.tx.app.three.xjcb:id/btn_register"]').all()) == 1:
        xjyl_collect.login(d)
    xjyl_collect.collect(d)
    # amdc_collect.sign_out()
    d.watcher.remove()
    ip = switchover_IP(d)
    ip.run()
