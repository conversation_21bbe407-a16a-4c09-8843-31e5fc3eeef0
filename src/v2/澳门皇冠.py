# coding: utf-8
#
import datetime

import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class amhg_collect:
    def __init__(self, d):
        self.d = d

    @staticmethod
    def sal():
        seed = "123456789abcdefghijklmnopqrstuvwxyz"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal2():
        seed = '1234567890'
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def login(src):
        d.set_fastinput_ime(True)
        d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/et_register_username"]').click()
        d.send_keys(amhg_collect.sal())
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/et_register_password"]').click()
        d.send_keys(amhg_collect.sal())
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/et_register_telephone_number"]').click()
        d.send_keys('188' + amhg_collect.sal2())
        d.sleep(1)
        d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/btn_register_nextstep"]').click()

        # 输入登录名,密码
        # d.set_fastinput_ime(True)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[1]/android.view.View[2]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[1]/android.view.View[2]/android.view.View[2]/android.widget.EditText[1]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@text="立即登陆"]').click()
        # while len(d.xpath('//*[@text="充值提现"]').all()) == 0:
        #     d.sleep(1)

    @staticmethod
    def collect(d):
        d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/rb_login"]').click()
        d.sleep(2)
        if len(d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/btn_register_nextstep"]').all()) == 1:
            amhg_collect.login('hhhh')
            d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/tv_register"]').click()
        for i in range(1, 5001):
            d.xpath('//*[@text="银联支付"]').click()
            d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/rlv_pay_third_party_channel"]/android.widget.RelativeLayout[1]').click()
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/et_pay_third_party_balance"]').click()
            # d.sleep(1)
            # d.clear_text()
            # d.send_keys(str(500+i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/btn_pay_third_party_nextstep"]').click()
            # amhg_collect.page(d)

            d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/rlv_pay_third_party_channel"]/android.widget.RelativeLayout[2]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/et_pay_third_party_balance"]').click()
            d.sleep(1)
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tx.app.three.amhc:id/btn_pay_third_party_nextstep"]').click()
            amhg_collect.page(d)
        return 1

    @staticmethod
    def page(d):
        while (
                len(d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="cardno-text"]/android.view.View[1]').all()) == 0) and (
                len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) == 0):
            d.sleep(1)
            if len(d.xpath('//android.widget.EditText').all()) == 1:
                d.xpath('//android.widget.EditText').click()
                d.sleep(1)
                d.clear_text()
                d.sleep(1)
                d.send_keys('李天莲')
                d.xpath('//*[@text="提交"]').click()
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.sleep(1)
                d.clear_text()
                d.sleep(1)
                d.send_keys('李天莲')
                d.xpath('//*[@text="提交"]').click()
            if len(d.xpath('//*[@text="下单IP限制"]').all()) == 1:
                d.press("back")
                break

        if len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]')
            name = names.text.replace(' ', '').replace(':', '')
            print(name)
            ids = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[5]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace(':', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace(':', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门皇冠\\脚本采集\\去重\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210316175908116341', dname=name,
                              brandname='澳门皇冠', newest_at=newest_at,
                              square=square, address='www.87hg.com', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            return 1

        if len(d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]')
            name = names.text.replace(' ', '').replace(':', '')
            print(name)
            ids = d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]')
            id_1 = ids.text.replace(' ', '').replace(':', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="app"]/android.view.View[9]/android.view.View[2]')
            bank = banks.text.replace(' ', '').replace(':', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门皇冠\\脚本采集\\去重\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210316175908116341', dname=name,
                              brandname='澳门皇冠', newest_at=newest_at,
                              square='无', address='www.87hg.com', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            return 1
        return 1


    @staticmethod
    def sign_out():
        d.xpath('//*[@text="我"]').click()
        d.sleep(1)
        d.xpath('//*[@text="安全退出"]').click()


    def run(self):
        d = self.d
        d.watcher.when(xpath='//*[@resource-id="com.tg.chess.alibaba.a459qpx:id/iv_close"]').click()
        d.watcher.when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_normal_title_layout"]').when(
            xpath='//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
        d.watcher.start()
        d.app_start('com.tx.app.three.amhc', stop=True)
        d.sleep(10)
        amhg_collect.collect(d)
        # amdc_collect.sign_out()
        d.watcher.remove()

if __name__ == '__main__':
    id = "c265b801"
    # id = "96e2aec0"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="com.tg.chess.alibaba.a459qpx:id/iv_close"]').click()
    d.watcher.when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_normal_title_layout"]').when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
    d.watcher.start()
    # d.app_start('com.tg.chess.alibaba.a459qpx', stop=True)
    # d.sleep(10)
    amhg_collect.collect(d)
    # amdc_collect.sign_out()
    d.watcher.remove()
