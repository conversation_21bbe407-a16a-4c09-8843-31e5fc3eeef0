# coding: utf-8
#
import datetime

import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class amxpj_collect:

    @staticmethod
    def sal():
        seed = "123456789abcdefghijklmnopqrstuvwxyz"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def login(src):
        d.set_fastinput_ime(True)
        src = amxpj_collect.sal()
        d.xpath('//*[@text="注册"]').click()
        d.xpath('//*[@text="用户名"]').click()
        d.send_keys(src)
        d.press("back")
        d.sleep(2)
        d.xpath('//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[2]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.EditText[2]').click()
        d.send_keys(src)
        d.press("back")
        d.sleep(2)
        d.xpath('//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[2]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.EditText[3]').click()
        d.send_keys(src)
        d.press("back")
        d.sleep(2)
        d.xpath('//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[2]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.EditText[4]').click()
        d.send_keys("黄博")
        d.press("back")
        d.sleep(2)
        d.xpath('//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[2]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.EditText[5]').click()
        d.send_keys('456321')
        d.press("back")
        d.sleep(2)
        d.xpath('//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[2]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.widget.EditText[6]').click()
        d.send_keys('18892981232')
        d.press("back")
        d.sleep(2)
        d.xpath('//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.LinearLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[2]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[9]').click()

        # 输入登录名,密码
        # d.set_fastinput_ime(True)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[1]/android.view.View[2]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[1]/android.view.View[2]/android.view.View[2]/android.widget.EditText[1]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@text="立即登陆"]').click()
        while len(d.xpath('//*[@text="充值提现"]').all()) == 0:
            d.sleep(1)

    @staticmethod
    def collect(d):
        d.xpath('//*[@resource-id="theme-qpbwh-home"]/android.view.View[5]').click()
        d.sleep(5)
        for i in range(1, 5001):
            d.xpath('//*[@content-desc="支付宝支付"]').click()
            d.sleep(3)
            # d.swipe(0.98, 0.23, 0.24, 0.23, 0.5)
            # d.sleep(2)
            d.xpath('//*[@content-desc="支付宝卡转卡：10-5000元"]').click()
            d.sleep(2)
            elements = d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[2]/android.view.View').all()
            d.sleep(2)
            n = 0
            for element in elements:
                n += 1
                if n >= 13:
                    break
                element.click()
                d.sleep(1)
                d.xpath('//*[@content-desc="下一步 "]').click()
                amxpj_collect.page(d)

            # d.xpath('//*[@content-desc="支付宝宝转卡：10-5000元"]').click()
            # d.sleep(3)
            # n = 0
            # elements = d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[2]/android.view.View').all()
            # for element in elements:
            #     n += 1
            #     if n >= 13:
            #         break
            #     element.click()
            #     d.sleep(1)
            #     d.xpath('//*[@content-desc="下一步 "]').click()
            #     n += 1
            #     amxpj_collect.page(d)

            d.xpath('//*[@content-desc="银联快捷"]').click()
            d.sleep(1)
            # d.xpath('//*[@content-desc="银联500-10000"]').click()
            # d.sleep(2)
            # elements = d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[2]/android.view.View').all()
            # d.sleep(2)
            # for element in elements:
            #     element.click()
            #     d.sleep(1)
            #     d.xpath('//*[@content-desc="下一步 "]').click()
            #     amxpj_collect.page(d)
            d.xpath('//*[@content-desc="银联310-2999元"]').click()
            d.sleep(2)
            elements = d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[2]/android.view.View').all()
            d.sleep(2)
            for element in elements:
                element.click()
                d.sleep(1)
                d.xpath('//*[@content-desc="下一步 "]').click()
                amxpj_collect.page(d)
            # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[1]/android.view.View[1]/android.view.View[3]/android.view.View[1]/android.view.View[1]').click()
            # d.sleep(2)
            # elements = d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[2]/android.view.View').all()
            # d.sleep(2)
            # for element in elements:
            #     element.click()
            #     d.sleep(1)
            #     d.xpath('//*[@content-desc="下一步 "]').click()
            #     amxpj_collect.page(d)



        return 1

    @staticmethod
    def page(d):
        t = 0
        while (
                len(d.xpath('//*[@resource-id="show_name"]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="realname-text"]/android.view.View[1]').all()) == 0) and (
                len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="realName"]/android.view.View[1]').all()) == 0):
            d.sleep(1)
            t += 1
            if (len(d.xpath('//*[@content-desc="下一步 "]').all()) == 1) and (t >= 5):
                if t >= 35:
                    break
                d.xpath('//*[@content-desc="下一步 "]').click()
                d.sleep(3)
            if (len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1) and (t >= 5):
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.clear_text()
                d.send_keys("刘长胜")
                d.xpath('//*[@text="提交"]').click()
            if len(d.xpath('//android.webkit.WebView/android.view.View[1]/android.view.View[1]').all()) == 1:
                if ('当前没有符合条件的收款账户' in d.xpath('//android.webkit.WebView/android.view.View[1]/android.view.View[1]').get_text()) or ('您下单过于频繁' in d.xpath('//android.webkit.WebView/android.view.View[1]/android.view.View[1]').get_text()):
                    d.press("back")
                    d.sleep(10)
                    break
            if t >= 30:
                if len(d.xpath('//*[@content-desc="银联快捷"]').all()) == 0:
                    d.press("back")
                    d.sleep(2)
                d.sleep(10)
                break


        if len(d.xpath('//*[@resource-id="show_name"]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//*[@resource-id="show_name"]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="show_pone"]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="show_bankname"]')
            bank = banks.text.replace(' ', '').replace('开户行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门新葡京\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210318159240770282', dname=name,
                              brandname='澳门新葡京', newest_at=newest_at,
                              square=square, address='www.6577.com', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1

        if len(d.xpath('//*[@resource-id="realname-text"]/android.view.View[1]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//*[@resource-id="realname-text"]/android.view.View[1]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="cardno-text"]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="bankname-text"]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('开户行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门新葡京\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210318159240770282', dname=name,
                              brandname='澳门新葡京', newest_at=newest_at,
                              square=square, address='www.6577.com', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1

        if len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[5]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('开户行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门新葡京\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210318159240770282', dname=name,
                              brandname='澳门新葡京', newest_at=newest_at,
                              square=square, address='www.6577.com', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1

        if len(d.xpath('//*[@resource-id="realName"]/android.view.View[1]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//*[@resource-id="realName"]/android.view.View[1]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="card"]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="bank"]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('开户行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门新葡京\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210318159240770282', dname=name,
                              brandname='澳门新葡京', newest_at=newest_at,
                              square=square, address='www.6577.com', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(20)
            return 1
        return 1


    @staticmethod
    def sign_out():
        d.xpath('//*[@text="我"]').click()
        d.sleep(1)
        d.xpath('//*[@text="安全退出"]').click()

    @staticmethod
    def run(d):
        d.watcher.when(xpath='//*[@resource-id="com.tg.chess.alibaba.a459qpx:id/iv_close"]').click()
        # d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.start()
        # d.app_start('com.tg.chess.alibaba.a459qpx', stop=True)
        # d.sleep(10)
        amxpj_collect.collect(d)
        # amdc_collect.sign_out()
        d.watcher.remove()

if __name__ == '__main__':
    id = "CQSCLFMBQGQK6HIR"
    # id = "96e2aec0"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_normal_title_layout"]').when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
    d.watcher.when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[1]/android.view.View[1]').when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[4]/android.view.View[1]/android.view.View[1]').click()
    d.watcher.start()
    # d.app_start('com.tg.chess.alibaba.a459qpx', stop=True)
    # d.sleep(10)
    amxpj_collect.collect(d)
    # amdc_collect.sign_out()
    d.watcher.remove()
