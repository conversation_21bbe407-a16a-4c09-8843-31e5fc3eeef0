# coding: utf-8
#
import datetime

import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class hrcp_collect:

    @staticmethod
    def sal():
        seed = "123456789abcdefghijklmnopqrstuvwxyz"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal1():
        seed = "1234567890"
        sa = []
        for i in range(10):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal2():
        seed = "1234567890"
        sa = []
        for i in range(6):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def login(d):
        d.set_fastinput_ime(True)
        src = hrcp_collect.sal()
        d.xpath('//*[@text="注册"]').click()
        d.xpath('//*[@text="用户名"]').click()
        d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(src)
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2])//2
        # y = (bounds[1] + bounds[3])//2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="确认密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(src)
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="真实姓名"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys('李国军')
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys("黄博")
        d.sleep(2)
        element = d.xpath('//*[@text="取款密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(str(hrcp_collect.sal2()))
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] + bounds[1]
        # d.click(x, y)
        # d.send_keys("456321")
        d.sleep(2)
        element = d.xpath('//*[@text="手机号码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys('1' + str(hrcp_collect.sal1()))
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys("456321")
        d.sleep(2)
        element = d.xpath('//*[@text="识别码ID"]/following::android.view.View[1]').all()[0]
        element.click()
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        d.sleep(5)
        # 输入登录名,密码
        # d.set_fastinput_ime(True)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[1]/android.view.View[2]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[1]/android.view.View[2]/android.view.View[2]/android.widget.EditText[1]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@text="立即登陆"]').click()
        # while len(d.xpath('//*[@text="充值提现"]').all()) == 0:
        #     d.sleep(1)

    @staticmethod
    def collect(d):
        d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/firstpage_recharge_withdraw"]').click()
        for i in range(1, 5001):
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/recycler_view"]/android.widget.LinearLayout[1]/android.widget.TextView[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/rv_payment_channel"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/et_amount"]').click()
            d.sleep(1)
            d.clear_text()
            d.send_keys(str(500+i))
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/btn_recharge"]').click()
            hrcp_collect.page(d)

            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/recycler_view"]/android.widget.LinearLayout[1]/android.widget.TextView[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/rv_payment_channel"]/android.widget.LinearLayout[2]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/et_amount"]').click()
            d.sleep(1)
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/btn_recharge"]').click()
            hrcp_collect.page(d)

            d.xpath('//*[@text="支付宝"]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/rv_payment_channel"]/android.widget.LinearLayout[3]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/et_amount"]').click()
            d.sleep(1)
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/btn_recharge"]').click()
            hrcp_collect.page(d)

            d.xpath('//*[@text="支付宝"]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/rv_payment_channel"]/android.widget.LinearLayout[4]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/et_amount"]').click()
            d.sleep(1)
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(2)
            d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/btn_recharge"]').click()
            hrcp_collect.page(d)

            d.xpath('//*[@text="云闪付"]').click()
            d.sleep(1)
            elements = d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/rv_payment_channel"]/android.widget.LinearLayout').all()
            for element in elements:
                element.click()
                d.sleep(1)
                d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/et_amount"]').click()
                d.sleep(1)
                d.clear_text()
                d.send_keys(str(500 + i))
                d.sleep(2)
                d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/btn_recharge"]').click()
                hrcp_collect.page(d)

        return 1

    @staticmethod
    def page(d):
        t = 0
        while (
                len(d.xpath('//android.webkit.WebView/android.view.View[2]/android.view.View[4]/android.view.View[2]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]').all()) == 0) and (
                len(d.xpath('//android.webkit.WebView/android.view.View[10]/android.view.View[4]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="payInfo"]/android.view.View[13]/android.view.View[2]').all()) == 0):
            d.sleep(1)
            t += 1
            # d.click(0.5, 0.45)
            # if len(d.xpath('//*[@resource-id="txtPayee"]').all()) == 1:
            #     d.sleep(1)
            #     d.xpath('//*[@resource-id="txtPayee"]').click()
            #     d.sleep(1)
            #     d.clear_text()
            #     d.send_keys('黄伟强')
            #     d.sleep(1)
            #     d.xpath('//*[@resource-id="btnPayeeSubmit"]').click()
            # if t == 20:
            #     d.press("back")
            #     d.sleep(20)
            #     break
            # if len(d.xpath('//*[@resource-id="tpay1"]').all()) == 1:
            #     if '点击开始支付宝转银行卡' in d.xpath('//*[@resource-id="tpay1"]').get_text():
            #         d.press("back")
            #         d.sleep(20)
            #         break
            # if len(d.xpath('//*[@text="success"]').all()) == 1:
            #     if 'success' in d.xpath('//*[@text="success"]').get_text():
            #         d.press("back")
            #         d.sleep(20)
            #         break
            if (len(d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/btn_recharge"]').all()) == 1) and (t % 10 == 0):
                if t >= 20:
                    d.sleep(20)
                d.xpath('//*[@resource-id="com.tech.amjx.cash2:id/btn_recharge"]').click()


        if len(d.xpath('//android.webkit.WebView/android.view.View[2]/android.view.View[4]/android.view.View[2]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]').all()) != 0:
            d.sleep(3)
            names = d.xpath('//android.webkit.WebView/android.view.View[2]/android.view.View[4]/android.view.View[2]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]')
            name = names.text.replace(' ', '')
            print(name)
            ids = d.xpath('//android.webkit.WebView/android.view.View[2]/android.view.View[3]/android.view.View[2]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[2]/android.view.View[5]/android.view.View[2]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]')
            bank = banks.text.replace(' ', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门金沙\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210325171416358331', dname=name,
                              brandname='澳门金沙', newest_at=newest_at,
                              square=square, address='https://sha4066.com/#/phone', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(40)
            return 1

        if len(d.xpath('//android.webkit.WebView/android.view.View[10]/android.view.View[4]').all()) != 0:
            d.sleep(3)
            names = d.xpath('//android.webkit.WebView/android.view.View[10]/android.view.View[4]')
            name = names.text.replace(' ', '')
            print(name)
            ids = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[4]')
            id_1 = ids.text.replace(' ', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[8]/android.view.View[4]')
            bank = banks.text.replace(' ', '')
            print(bank)
            image = d.screenshot()
            image_path = "E:\\石溪科技银行卡\\采集卡号清单\\澳门金沙\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210325171416358331', dname=name,
                             brandname='澳门金沙', newest_at=newest_at,
                             square=square, address='https://sha4066.com/#/phone', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(40)
            return 1
        return 1


    @staticmethod
    def sign_out():
        d.xpath('//*[@text="我"]').click()
        d.sleep(1)
        d.xpath('//*[@text="安全退出"]').click()

    @staticmethod
    def run(d):
        d.watcher.when(xpath='//*[@text="系统提示"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text="Close"]').click()
        d.watcher.start()
        # d.app_start('com.baas.tg168', stop=False)
        if len(d.xpath('//*[@text="注册"]').all()) == 1:
            hrcp_collect.login(d)
        hrcp_collect.collect(d)
        d.watcher.remove()

if __name__ == '__main__':
    # id = "c265b801"
    id = "CQSCLFMBQGQK6HIR"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@text="请详细阅读如下文字："]').when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[2]/android.view.View[1]/android.view.View[1]').click()
    d.watcher.when(xpath='//*[@resource-id="android:id/content"]/android.widget.LinearLayout[1]').when(xpath='//*[@resource-id="com.tech.amjx.cash2:id/dialog_twobtn_cancel"]').click()
    d.watcher.when(xpath='//*[@text="充值须知"]').when(xpath='//*[@resource-id="layui-m-layer0"]/android.view.View[2]/android.view.View[1]/android.view.View[1]/android.view.View[2]/android.view.View[1]/android.view.View[1]').click()
    # d.watcher.when(xpath='//*[@text="Close"]').click()
    d.watcher.start()
    # d.app_start('com.baas.tg168', stop=True)
    hrcp_collect.collect(d)
    # amdc_collect.sign_out()
    d.watcher.remove()
    # amdc_collect.data_write('E:\\石溪科技银行卡\\采集卡号清单\\大奖直播\\脚本采集\\银行卡信息_c265b801.xlsx', lis)