# coding: utf-8
#
import datetime

import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class yhgj_collect:

    @staticmethod
    def sal():
        seed = "123456789abcdefghijklmnopqrstuvwxyz"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal1():
        seed = "1234567890"
        sa = []
        for i in range(10):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal2():
        seed = "1234567890"
        sa = []
        for i in range(6):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def login(d):
        d.set_fastinput_ime(True)
        src = yhgj_collect.sal()
        d.xpath('//*[@text="注册"]').click()
        d.xpath('//*[@text="用户名"]').click()
        d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(src)
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2])//2
        # y = (bounds[1] + bounds[3])//2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="确认密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(src)
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="真实姓名"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys('李国军')
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys("黄博")
        d.sleep(2)
        element = d.xpath('//*[@text="取款密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(str(yhgj_collect.sal2()))
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] + bounds[1]
        # d.click(x, y)
        # d.send_keys("456321")
        d.sleep(2)
        element = d.xpath('//*[@text="手机号码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys('1' + str(yhgj_collect.sal1()))
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys("456321")
        d.sleep(2)
        element = d.xpath('//*[@text="识别码ID"]/following::android.view.View[1]').all()[0]
        element.click()
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        d.sleep(5)
        # 输入登录名,密码
        # d.set_fastinput_ime(True)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[1]/android.view.View[2]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[1]/android.view.View[2]/android.view.View[2]/android.widget.EditText[1]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@text="立即登陆"]').click()
        # while len(d.xpath('//*[@text="充值提现"]').all()) == 0:
        #     d.sleep(1)

    @staticmethod
    def collect(d):
        d.sleep(3)
        d.xpath('//*[@resource-id="com.games.yy:id/tv_deposit"]').click()
        d.sleep(2)
        d.xpath('//*[@resource-id="com.games.yy:id/iv_close"]').click()
        for i in range(1, 5001):
            d.xpath('//*[@resource-id="com.games.yy:id/tv_pay_type"]').click()
            d.sleep(1)
            d.xpath('//*[@text="支付宝"]').click()
            d.sleep(1)
            # d.xpath('//*[@text="银联转银行卡"]').click()
            d.xpath('//*[@text="128"]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/edit_save_amount"]').click()
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/ll_activity"]/android.widget.ImageView[1]').click()
            d.sleep(2)
            d.xpath('//*[@resource-id="com.games.yy:id/tv_type_name"]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/tv_next_step_btn"]').click()
            yhgj_collect.page(d)

            d.xpath('//*[@resource-id="com.games.yy:id/tv_pay_type"]').click()
            d.sleep(1)
            d.xpath('//*[@text="云闪付"]').click()
            d.sleep(1)
            # d.xpath('//*[@resource-id="com.games.yy:id/tv_type_name"]').click()
            d.xpath('//*[@text="128"]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/edit_save_amount"]').click()
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/ll_activity"]/android.widget.ImageView[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/tv_type_name"]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/tv_next_step_btn"]').click()
            yhgj_collect.page(d)

            # d.xpath('//*[@resource-id="com.games.yy:id/tv_pay_type"]').click()
            # d.sleep(1)
            # d.xpath('//*[@text="银联"]').click()
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.games.yy:id/tv_type_name"]').click()
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.games.yy:id/edit_save_amount"]').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.games.yy:id/ll_activity"]/android.widget.ImageView[1]').click()
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.games.yy:id/tv_type_name"]').click()
            # d.sleep(1)
            # d.xpath('//*[@resource-id="com.games.yy:id/tv_next_step_btn"]').click()
            # yhgj_collect.page(d)

            d.xpath('//*[@resource-id="com.games.yy:id/tv_pay_type"]').click()
            d.sleep(1)
            d.xpath('//*[@text="银联"]').click()
            d.sleep(1)
            # d.xpath('//*[@text="银联转银行卡"]').click()
            d.xpath('//*[@text="128"]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/edit_save_amount"]').click()
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/ll_activity"]/android.widget.ImageView[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/tv_type_name"]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.games.yy:id/tv_next_step_btn"]').click()
            yhgj_collect.page(d)


        return 1

    @staticmethod
    def page(d):
        t = 0
        while (
                len(d.xpath('//*[@resource-id="content"]/android.view.View[6]/android.view.View[3]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="acc"]/android.view.View[1]').all()) == 0) and (
                len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="card_account"]').all()) == 0):
            d.sleep(1)
            t += 1
            if len(d.xpath('//*[@resource-id="com.vivo.browser:id/message"]').all()) == 1:
                if ('您所在地域不允许访问' in d.xpath('//*[@resource-id="com.vivo.browser:id/message"]').get_text()) or ('未匹配到在线的收款码' in d.xpath('//*[@resource-id="com.vivo.browser:id/message"]').get_text()):
                    d.sleep(2)
                    d.xpath('//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
                    d.sleep(2)
                    while len(d.xpath('//*[@resource-id="com.games.yy:id/tv_next_step_btn"]').all()) == 0:
                        d.press("back")
                        d.sleep(4)
                    d.sleep(10)
                    return 0
                d.xpath('//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
                d.sleep(2)
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.clear_text()
                d.send_keys("凌天翔")
                d.xpath('//*[@text="提交"]').click()
                if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                    while len(d.xpath('//*[@text="下一步"]').all()) == 0:
                        d.press("back")
                        d.sleep(4)
                    d.sleep(10)
                    return 0
            if len(d.xpath('//*[@text="支付请求超时，请重新下单！"]').all()) == 1:
                d.press("back")
                d.sleep(10)
                break
            if t >= 30:
                if len(d.xpath('//*[@resource-id="com.games.yy:id/tv_next_step_btn"]').all()) == 1:
                    d.sleep(10)
                    break
                d.press("back")
                d.sleep(10)
                break

        if len(d.xpath('//*[@resource-id="content"]/android.view.View[6]/android.view.View[3]').all()) != 0:
            d.sleep(1)
            if '网银' in d.xpath('//*[@resource-id="content"]/android.view.View[1]').get_text():
                names = d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]')
                name = names.text.replace(' ', '')
                print(name)
                ids = d.xpath('//*[@resource-id="content"]/android.view.View[6]/android.view.View[3]')
                id_1 = ids.text.replace(' ', '').replace('I', '')
                print(id_1)
                banks = d.xpath('//*[@resource-id="content"]/android.view.View[5]/android.view.View[3]')
                bank = banks.text.replace(' ', '')
                print(bank)
            else:
                names = d.xpath('//*[@resource-id="content"]/android.view.View[6]/android.view.View[3]')
                name = names.text.replace(' ', '')
                print(name)
                ids = d.xpath('//*[@resource-id="content"]/android.view.View[5]/android.view.View[3]')
                id_1 = ids.text.replace(' ', '').replace('I', '')
                print(id_1)
                banks = d.xpath('//*[@resource-id="content"]/android.view.View[4]/android.view.View[3]')
                bank = banks.text.replace(' ', '')
                print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\银河国际\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210325173743911637', dname=name,
                              brandname='银河国际', newest_at=newest_at,
                              square=square, address='https://762yh.com/', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1

        if len(d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]').all()) != 0:
            d.sleep(3)
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.clear_text()
                d.send_keys("凌天翔")
                d.xpath('//*[@text="确定"]').click()
            d.sleep(1)
            names = d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]')
            name = names.text.replace(' ', '')
            print(name)
            ids = d.xpath('//*[@resource-id="app"]/android.view.View[3]/android.view.View[2]')
            id_1 = ids.text.replace(' ', '').replace('I', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]')
            bank = banks.text.replace(' ', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\银河国际\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210325173743911637', dname=name,
                              brandname='银河国际', newest_at=newest_at,
                              square=square, address='https://762yh.com/', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1

        if len(d.xpath('//*[@resource-id="acc"]/android.view.View[1]').all()) != 0:
            d.sleep(3)
            names = d.xpath('//*[@resource-id="acc"]/android.view.View[1]')
            name = names.text.replace(' ', '')
            print(name)
            ids = d.xpath('//*[@resource-id="card"]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('I', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="bk"]/android.view.View[1]')
            bank = banks.text.replace(' ', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\银河国际\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210325173743911637', dname=name,
                              brandname='银河国际', newest_at=newest_at,
                              square=square, address='https://762yh.com/', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1
        if len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) != 0:
            d.sleep(3)
            names = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]')
            name = names.text.replace(' ', '')
            print(name)
            ids = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[5]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('I', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[1]')
            bank = banks.text.replace(' ', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\银河国际\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210325173743911637', dname=name,
                              brandname='银河国际', newest_at=newest_at,
                              square=square, address='https://762yh.com/', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1
        if len(d.xpath('//*[@resource-id="card_account"]').all()) != 0:
            d.sleep(3)
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.clear_text()
                d.send_keys("凌天翔")
                d.xpath('//*[@text="确定"]').click()
                d.sleep(3)
                if len(d.xpath('//*[@resource-id="com.vivo.browser:id/message"]').all()) == 1:
                    if ('您所在地域不允许访问' in d.xpath('//*[@resource-id="com.vivo.browser:id/message"]').get_text()) or (
                            '未匹配到在线的收款码' in d.xpath('//*[@resource-id="com.vivo.browser:id/message"]').get_text()):
                        d.sleep(2)
                        d.xpath('//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
                        d.sleep(2)
                        while len(d.xpath('//*[@resource-id="com.games.yy:id/tv_next_step_btn"]').all()) == 0:
                            d.press("back")
                            d.sleep(4)
                        d.sleep(10)
                        return 0
                    d.xpath('//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
                    d.sleep(2)
            names = d.xpath('//*[@resource-id="card_account"]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="card_no"]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[4]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\银河国际\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210325173743911637', dname=name,
                              brandname='银河国际', newest_at=newest_at,
                              square=square, address='https://762yh.com/', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1
        return 1


    @staticmethod
    def sign_out():
        d.xpath('//*[@text="我"]').click()
        d.sleep(1)
        d.xpath('//*[@text="安全退出"]').click()

    @staticmethod
    def run(d):
        d.watcher.when(xpath='//*[@text="系统提示"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text="Close"]').click()
        d.watcher.start()
        # d.app_start('com.baas.tg168', stop=False)
        if len(d.xpath('//*[@text="注册"]').all()) == 1:
            yhgj_collect.login(d)
        yhgj_collect.collect(d)
        d.watcher.remove()

if __name__ == '__main__':
    id = "96e2aec0"
    # id = "CQSCLFMBQGQK6HIR"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@text="系统提示"]').when(xpath='//*[@text="确定"]').click()
    d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
    d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
    d.watcher.when(xpath='//*[@resource-id="exampleModalLabel2"]/android.view.View[1]').when(xpath='//*[@resource-id="alt"]/android.view.View[1]/android.view.View[2]/android.widget.Button[1]').click()
    # d.watcher.when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_normal_title_layout"]').when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
    d.watcher.when(xpath='//*[@text="Close"]').click()
    d.watcher.start()
    # d.app_start('com.baas.tg168', stop=True)
    yhgj_collect.collect(d)
    # amdc_collect.sign_out()
    d.watcher.remove()
    # amdc_collect.data_write('D:\\石溪科技银行卡\\采集卡号清单\\大奖直播\\脚本采集\\银行卡信息_c265b801.xlsx', lis)