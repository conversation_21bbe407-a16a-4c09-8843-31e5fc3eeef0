# coding: utf-8
#
import datetime

import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class ffyl_collect:

    @staticmethod
    def sal():
        seed = "123456789abcdefghijklmnopqrstuvwxyz"
        sa = []
        for i in range(9):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal1():
        seed = "1234567890"
        sa = []
        for i in range(10):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal2():
        seed = "1234567890"
        sa = []
        for i in range(6):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def login(d):
        d.set_fastinput_ime(True)
        src = ffyl_collect.sal()
        d.xpath('//*[@text="注册"]').click()
        d.xpath('//*[@text="用户名"]').click()
        d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(src)
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2])//2
        # y = (bounds[1] + bounds[3])//2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="确认密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(src)
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys(src)
        d.sleep(2)
        element = d.xpath('//*[@text="真实姓名"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys('王一凡')
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys("黄博")
        d.sleep(2)
        element = d.xpath('//*[@text="取款密码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys(str(ffyl_collect.sal2()))
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] + bounds[1]
        # d.click(x, y)
        # d.send_keys("456321")
        d.sleep(2)
        element = d.xpath('//*[@text="手机号码"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys('1' + str(ffyl_collect.sal1()))
        # bounds = element.bounds
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        # d.send_keys("456321")
        d.sleep(2)
        element = d.xpath('//*[@text="识别码ID"]/following::android.widget.EditText[1]').all()[0]
        element.click()
        d.send_keys('228333')
        # x = (bounds[0] + bounds[2]) // 2
        # y = (bounds[1] + bounds[3]) // 2 + bounds[3] - bounds[1]
        # d.click(x, y)
        d.sleep(5)
        # 输入登录名,密码
        # d.set_fastinput_ime(True)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[1]/android.view.View[2]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[1]/android.view.View[2]/android.view.View[2]/android.widget.EditText[1]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@text="立即登陆"]').click()
        # while len(d.xpath('//*[@text="充值提现"]').all()) == 0:
        #     d.sleep(1)

    @staticmethod
    def collect(d):
        d.sleep(3)
        d.xpath('//*[@text="充值"]').click()
        d.sleep(2)
        d.xpath('//*[@text="线上入款"]').click()
        d.sleep(2)
        d.xpath('//*[@text="网银转账"]').click()
        d.sleep(1)
        for i in range(1, 5001):
            d.sleep(1)
            d.xpath('//*[@text="卡转卡 153-3000"]').click()
            d.sleep(5)
            d.xpath('//*[@resource-id="ffbc23.bets8888.sport:id/et_recharge_money"]').click()
            d.clear_text()
            d.send_keys(str(160 + i))
            d.sleep(1)
            d.xpath('//*[@text="下一步"]').click()
            ffyl_collect.page(d)

            d.xpath('//*[@text="卡转卡小额 30-500"]').click()
            d.sleep(5)
            d.xpath('//*[@resource-id="ffbc23.bets8888.sport:id/et_recharge_money"]').click()
            d.clear_text()
            d.send_keys(str(100 + i))
            d.sleep(1)
            d.xpath('//*[@text="下一步"]').click()
            ffyl_collect.page(d)

            # d.xpath('//*[@text="卡转卡小额 30-300"]').click()
            # d.sleep(1)
            # d.xpath('//*[@resource-id="ffbc23.bets8888.sport:id/et_recharge_money"]').click()
            # d.clear_text()
            # d.send_keys(str(30 + i))
            # d.sleep(1)
            # d.xpath('//*[@text="下一步"]').click()
            # ffyl_collect.page(d)


        return 1

    @staticmethod
    def page(d):
        t = 0
        while (
                len(d.xpath('//*[@resource-id="t4"]').all()) == 0) and (
                len(d.xpath('//*[@text="姓名："]/following::android.view.View[1]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="show_name"]').all()) == 0
        ):
            d.sleep(1)
            t += 1
            # if (len(d.xpath('//*[@text="请使用 手机银行 进行转帐"]').all()) == 1) or (len(d.xpath('//*[@text="网络不稳定，请稍后再试！"]').all()) == 1):
            #     d.press("back")
            #     d.sleep(10)
            #     d.xpath('//android.widget.ListView/android.view.View[2]').click()
            #     while len(d.xpath(
            #             '//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[2]/android.widget.LinearLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[4]').all()) == 0:
            #         d.sleep(1)
            #     d.sleep(3)
            #     d.xpath('//*[@resource-id="android:id/content"]/android.widget.FrameLayout[1]/android.widget.FrameLayout[1]/android.view.ViewGroup[2]/android.widget.LinearLayout[1]/android.webkit.WebView[1]/android.webkit.WebView[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[1]/android.view.View[5]').click()
            #     d.sleep(1)
            #     d.xpath('//*[@resource-id="mdl-0_amount"]').click()
            #     d.send_keys('899')
            #     d.xpath('//*[@text="提交"]').click()
            # if len(d.xpath('//*[@text="转账姓名:"]').all()) == 1:
            #     d.sleep(10)
            #     if len(d.xpath('//*[@text="收银台"]/android.view.View[1]/android.view.View[6]/android.view.View[2]').all()) == 0:
            #         d.press("back")
            #         break
            # if len(d.xpath('//*[@text="注册"]').all()) == 1:
            #     d.app_start('com.baas.tg168', stop=True)
            #     d.sleep(10)
            #     ffyl_collect.login(d)
            #     d.app_start('com.baas.tg168', stop=True)
            #     d.sleep(3)
            #     d.xpath('//*[@text="充值提现"]').click()
            #     d.sleep(1)
            #     d.xpath('//*[@text="在线存款"]').click()
            #     d.sleep(1)
            #     return 1
            # if t == 40:
            #     d.press("back")
            #     break
        if len(d.xpath('//*[@resource-id="t4"]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//*[@resource-id="t4"]')
            name = names.text.replace(' ', '')
            print(name)
            ids = d.xpath('//*[@resource-id="t6"]')
            id_1 = ids.text.replace(' ', '').replace('I', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="t5"]')
            bank = banks.text.replace(' ', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\非凡娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210329194392453210', dname=name,
                              brandname='非凡娱乐', newest_at=newest_at,
                              square='无', address='https://x4933qq.com/home', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(30)
            return 1

        if len(d.xpath('//*[@text="姓名："]/following::android.view.View[1]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//*[@text="姓名："]/following::android.view.View[1]')
            name = names.text.replace(' ', '')
            print(name)
            ids = d.xpath('//*[@text="账号："]/following::android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('I', '')
            print(id_1)
            banks = d.xpath('//*[@text="信息："]/following::android.view.View[1]')
            bank = banks.text.replace(' ', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\非凡娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210329194392453210', dname=name,
                             brandname='非凡娱乐', newest_at=newest_at,
                             square='无', address='https://x4933qq.com/home', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(30)
            return 1

        if len(d.xpath('//*[@resource-id="show_name"]').all()) != 0:
            d.sleep(1)
            names = d.xpath('//*[@resource-id="show_name"]')
            name = names.text.replace(' ', '')
            print(name)
            ids = d.xpath('//*[@resource-id="show_pone"]')
            id_1 = ids.text.replace(' ', '').replace('I', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="show_bankname"]')
            bank = banks.text.replace(' ', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\非凡娱乐\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210329194392453210', dname=name,
                             brandname='非凡娱乐', newest_at=newest_at,
                             square='无', address='https://x4933qq.com/home', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(30)
            return 1
        return 1


    @staticmethod
    def sign_out():
        d.xpath('//*[@text="我"]').click()
        d.sleep(1)
        d.xpath('//*[@text="安全退出"]').click()

    @staticmethod
    def run(d):
        d.watcher.when(xpath='//*[@text="系统提示"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text=" 系统公告"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
        d.watcher.when(xpath='//*[@text="Close"]').click()
        d.watcher.start()
        # d.app_start('com.baas.tg168', stop=False)
        if len(d.xpath('//*[@text="注册"]').all()) == 1:
            ffyl_collect.login(d)
        ffyl_collect.collect(d)
        d.watcher.remove()

if __name__ == '__main__':
    # id = "c265b801"
    id = "96e2aec0"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="ffbc23.bets8888.sport:id/dialogtitle"]').when(xpath='//*[@resource-id="ffbc23.bets8888.sport:id/dialogsure"]').click()
    d.watcher.when(xpath='//*[@text="重要提示："]').when(xpath='//*[@text="我知道了"]').click()
    # d.watcher.when(xpath='//*[@text=" 系统消息"]').when(xpath='//*[@text="确定"]').click()
    # d.watcher.when(xpath='//*[@text="Close"]').click()
    d.watcher.start()
    # d.app_start('com.baas.tg168', stop=True)
    ffyl_collect.collect(d)
    # amdc_collect.sign_out()
    d.watcher.remove()
    # amdc_collect.data_write('D:\\石溪科技银行卡\\采集卡号清单\\大奖直播\\脚本采集\\银行卡信息_c265b801.xlsx', lis)