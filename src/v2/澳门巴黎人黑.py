# coding: utf-8
#
import datetime

import uiautomator2 as u2
import random
import xlwt
from src.payment import payment
from src.v2.IP import switchover_IP
# 登录账号1-操作至信息界面-采集文本信息写入+截图-退出；
# 登录账号2-操作至信息界面-采集文本信息写入+截图-退出；
# 循环20次，再从头来；

class amblrh_collect:
    def __init__(self, d):
        self.d = d

    @staticmethod
    def reset(d):
        d.app_start('com.android.settings', stop=True)
        d.sleep(2)
        d.swipe_ext("up", scale=1)
        d.sleep(1)
        d.swipe_ext("up", scale=1)
        d.sleep(1)
        d.swipe_ext("up", scale=1)
        d.sleep(1)
        if len(d.xpath('//*[@text="更多设置"]').all()) == 1:
            d.xpath('//*[@text="更多设置"]').click()
        else:
            d.xpath('//*[@text="应用与权限"]').click()
        d.xpath('//*[@text="应用管理"]').click()
        while len(d.xpath('//*[@text="澳门巴黎人"]').all()) == 0:
            d.swipe_ext("up", scale=1)
        d.xpath('//*[@text="澳门巴黎人"]').click()
        d.sleep(1)
        d.xpath('//*[@text="存储"]').click()
        d.sleep(1)
        d.xpath('//*[@text="清除数据"]').click()
        d.sleep(1)
        d.xpath('//*[@resource-id="android:id/button1"]').click()

    @staticmethod
    def sal():
        seed = "123456789abcdefghijklmnopqrstuvwxyz"
        sa = []
        for i in range(7):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal1():
        seed = "1234567890"
        sa = []
        for i in range(8):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def sal2():
        seed = "1234567890"
        sa = []
        for i in range(6):
            sa.append(random.choice(seed))
        salt = ''.join(sa)
        print(salt)
        return salt

    @staticmethod
    def data_write(file_path, lis):
        f = xlwt.Workbook()
        sheet1 = f.add_sheet(u'sheet1', cell_overwrite_ok=True)  # 创建sheet
        # 将数据写入第 i 行，第 j 列
        i = 0
        for data in lis:
            for j in range(len(data)):
                sheet1.write(i, j, data[j])
            i = i + 1
        f.save(file_path)

    @staticmethod
    def login(d):
        d.set_fastinput_ime(True)
        src = amblrh_collect.sal()
        d.xpath('//*[@text="快速注册"]').click()
        d.sleep(1)
        d.xpath('//android.support.v4.view.ViewPager/android.view.ViewGroup[1]/android.view.ViewGroup[1]').click()
        d.send_keys('a'+src+'1')
        d.sleep(2)
        d.xpath('//android.support.v4.view.ViewPager/android.view.ViewGroup[1]/android.view.ViewGroup[2]').click()
        d.send_keys('王一凡')
        d.sleep(2)
        d.xpath('//android.support.v4.view.ViewPager/android.view.ViewGroup[1]/android.view.ViewGroup[3]').click()
        d.send_keys(src + '1')
        d.sleep(2)
        d.xpath('//android.support.v4.view.ViewPager/android.view.ViewGroup[1]/android.view.ViewGroup[4]').click()
        d.send_keys('188'+amblrh_collect.sal1())
        d.sleep(2)
        d.xpath('//*[@text="立即注册"]').click()
        d.sleep(2)
        d.xpath('//*[@text="立即注册"]').click()
        d.sleep(2)
        # 输入登录名,密码
        # d.set_fastinput_ime(True)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[2]/android.view.View[1]/android.view.View[2]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@resource-id="app"]/android.view.View[2]/android.view.View[1]/android.view.View[2]/android.view.View[2]/android.widget.EditText[1]').click()
        # d.clear_text()
        # d.send_keys(src)
        # d.xpath('//*[@text="立即登陆"]').click()
        while len(d.xpath('//*[@text="我的"]').all()) == 0:
            d.sleep(1)

    @staticmethod
    def collect(d):
        d.sleep(2)
        d.xpath('//*[@text="我的"]').click()
        d.sleep(1)
        for i in range(1, 50):
            d.xpath('//*[@text="存款"]').click()
            d.sleep(3)
            d.swipe(0.9, 0.2, 0.1, 0.2, 0.5)
            d.sleep(1)
            d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup[3]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.ImageView[1]').click()
            d.sleep(2)
            # elements = d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup').all()
            # for element in elements:
            #     element.click()
            #     d.sleep(1)
            #     d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.widget.EditText').click()
            #     d.clear_text()
            #     d.send_keys(str(500 + i))
            #     d.sleep(1)
            #     d.xpath('//*[@text="下一步"]').click()
            #     d.sleep(1)
            #     d.xpath('//*[@text="下一步"]').click()
            #     amblrh_collect.page(d)
            # d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup[4]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.ImageView[1]').click()
            # elements = d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup').all()
            # d.sleep(2)
            # for element in elements:
            #     element.click()
            #     d.sleep(1)
            #     d.xpath(
            #         '//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.widget.EditText').click()
            #     d.clear_text()
            #     d.send_keys(str(500 + i))
            #     d.sleep(1)
            #     d.xpath('//*[@text="下一步"]').click()
            #     d.sleep(1)
            #     d.xpath('//*[@text="下一步"]').click()
            #     amblrh_collect.page(d)

            # d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup[3]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.ImageView[1]').click()
            # d.sleep(2)
            # d.xpath('//*[@text="支付1"]').click()
            # d.sleep(1)
            # d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.widget.EditText').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@text="下一步"]').click()
            # d.sleep(1)
            # d.xpath('//*[@text="下一步"]').click()
            # amblrh_collect.page(d)
            d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup[3]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.ImageView[1]').click()
            d.sleep(2)
            d.xpath('//*[@text="支付3"]').click()
            d.sleep(1)
            d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.widget.EditText').click()
            d.clear_text()
            d.send_keys(str(500 + i))
            d.sleep(1)
            d.xpath('//*[@text="下一步"]').click()
            d.sleep(1)
            d.xpath('//*[@text="下一步"]').click()
            amblrh_collect.page(d)

            # d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup[4]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.ImageView[1]').click()
            # d.sleep(2)
            # d.xpath('//*[@text="支付1"]').click()
            # d.sleep(1)
            # d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.widget.EditText').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@text="下一步"]').click()
            # d.sleep(1)
            # d.xpath('//*[@text="下一步"]').click()
            # amblrh_collect.page(d)
            # d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup[4]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.ImageView[1]').click()
            # d.sleep(2)
            # d.xpath('//*[@text="支付3"]').click()
            # d.sleep(1)
            # d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.widget.EditText').click()
            # d.clear_text()
            # d.send_keys(str(500 + i))
            # d.sleep(1)
            # d.xpath('//*[@text="下一步"]').click()
            # d.sleep(1)
            # d.xpath('//*[@text="下一步"]').click()
            # amblrh_collect.page(d)
            while len(d.xpath('//*[@text="我的"]').all()) == 0:
                d.press("back")
                d.sleep(3)


        return 1

    @staticmethod
    def page(d):
        t = 0
        while (
                len(d.xpath('//*[@resource-id="card_account"]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]').all()) == 0) and (
                len(d.xpath('//android.widget.ListView/android.view.View[1]/android.view.View[2]').all()) == 0) and (
                len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]').all()) == 0) and (
                len(d.xpath('//*[@resource-id="show2"]/android.view.View[3]/android.view.View[1]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]').all()) == 0):
            d.sleep(1)
            t += 1
            if len(d.xpath('//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').all()) == 1:
                if '付款' in d.xpath('//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').get_text():
                    d.xpath('//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').click()
                    d.sleep(4)
                    break
            if len(d.xpath('//*[@resource-id="payer-name"]').all()) == 1:
                if len(d.xpath(
                        '//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').all()) == 1:
                    if '付款' in d.xpath('//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').get_text():
                        d.xpath('//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').click()
                        d.sleep(4)
                        break
                d.xpath('//*[@resource-id="payer-name"]').click()
                d.clear_text()
                d.send_keys("詹明德")
                d.xpath('//*[@text="确定"]').click()
                d.sleep(2)
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                if len(d.xpath(
                        '//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').all()) == 1:
                    if '付款' in d.xpath(
                            '//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').get_text():
                        d.xpath(
                            '//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').click()
                        d.sleep(4)
                        break
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.clear_text()
                d.send_keys("詹明德")
                d.sleep(1)
                if len(d.xpath('//*[@text="提交付款"]').all()) == 1:
                    d.xpath('//*[@text="提交付款"]').click()
                elif len(d.xpath('//*[@text="提交"]').all()) == 1:
                    d.xpath('//*[@text="提交"]').click()
                d.sleep(4)
                if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                    while len(d.xpath('//*[@text="下一步"]').all()) == 0:
                        d.press("back")
                        d.sleep(4)
                    d.sleep(10)
                    break
            if len(d.xpath('//android.widget.EditText').all()) == 1:
                if len(d.xpath(
                        '//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').all()) == 1:
                    if '付款' in d.xpath(
                            '//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').get_text():
                        d.xpath(
                            '//android.widget.ListView/android.view.View[3]/android.view.View[1]/android.view.View[1]').click()
                        d.sleep(4)
                        break
                d.xpath('//android.widget.EditText').click()
                d.clear_text()
                d.send_keys("詹明德")
                if len(d.xpath('//*[@text="提交付款"]').all()) == 1:
                    d.xpath('//*[@text="提交付款"]').click()
                elif len(d.xpath('//*[@text="提交"]').all()) == 1:
                    d.xpath('//*[@text="提交"]').click()
                d.sleep(4)
                if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                    while len(d.xpath('//*[@text="下一步"]').all()) == 0:
                        d.press("back")
                        d.sleep(4)
                    d.sleep(10)
                    break
            if t >= 30:
                while len(d.xpath('//*[@text="下一步"]').all()) == 0:
                    d.press("back")
                    d.sleep(4)
                d.sleep(10)
                break
            if len(d.xpath('//*[@text="立即登录"]').all()) == 1:
                amblrh_collect.login(d)
                d.xpath('//*[@text="我的"]').click()
                d.sleep(1)
                d.xpath('//*[@text="存款"]').click()
                d.sleep(1)
                d.swipe(0.9, 0.2, 0.1, 0.2, 0.5)
                d.sleep(1)
                d.xpath('//android.widget.ScrollView/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.HorizontalScrollView[1]/android.view.ViewGroup[1]/android.view.ViewGroup[3]/android.view.ViewGroup[1]/android.view.ViewGroup[1]/android.widget.ImageView[1]').click()


        if len(d.xpath('//*[@resource-id="card_account"]').all()) != 0:
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.clear_text()
                d.send_keys("詹明德")
                if len(d.xpath('//*[@text="提交"]').all()) == 1:
                    d.xpath('//*[@text="提交"]').click()
                elif len(d.xpath('//*[@text="确定"]').all()) == 1:
                    d.xpath('//*[@text="确定"]').click()
            if len(d.xpath('//*[@resource-id="payer-name"]').all()) == 1:
                d.xpath('//*[@resource-id="payer-name"]').click()
                d.clear_text()
                d.send_keys("詹明德")
                if len(d.xpath('//*[@text="提交"]').all()) == 1:
                    d.xpath('//*[@text="提交"]').click()
                elif len(d.xpath('//*[@text="确定"]').all()) == 1:
                    d.xpath('//*[@text="确定"]').click()
            d.sleep(4)
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                while len(d.xpath('//*[@text="下一步"]').all()) == 0:
                    d.press("back")
                    d.sleep(4)
                d.sleep(10)
                return 0
            names = d.xpath('//*[@resource-id="card_account"]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            if '请输入付款姓名' in name:
                d.press("back")
                d.sleep(1)
                d.press("back")
                d.sleep(1)
                return 1
            print(name)
            ids = d.xpath('//*[@resource-id="card_no"]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[4]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人黑\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210406202023635538', dname=name,
                              brandname='澳门巴黎人', newest_at=newest_at,
                              square=square, address='https://sdzyczswxggh.com/home/<USER>', bankname=bank,
                              image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1

        if len(d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]').all()) != 0:
            d.sleep(4)
            if len(d.xpath('//android.widget.EditText').all()) == 1:
                d.xpath('//android.widget.EditText').click()
                d.clear_text()
                d.send_keys("詹明德")
                if len(d.xpath('//*[@text="提交付款"]').all()) == 1:
                    d.xpath('//*[@text="提交付款"]').click()
                elif len(d.xpath('//*[@text="提交"]').all()) == 1:
                    d.xpath('//*[@text="提交"]').click()
            d.sleep(4)
            if len(d.xpath('//*[@text="union-pay"]').all()) == 1:
                names = d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]')
                name = names.text.replace(' ', '').replace('姓名：', '')
                print(name)
                ids = d.xpath('//*[@resource-id="app"]/android.view.View[3]/android.view.View[2]')
                id_1 = ids.text.replace(' ', '').replace('卡号：', '')
                print(id_1)
                banks = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]')
                bank = banks.text.replace(' ', '').replace('银行：', '')
                print(bank)
            else:
                names = d.xpath('//*[@resource-id="app"]/android.view.View[7]/android.view.View[2]')
                name = names.text.replace(' ', '').replace('姓名：', '')
                print(name)
                ids = d.xpath('//*[@resource-id="app"]/android.view.View[5]/android.view.View[2]')
                id_1 = ids.text.replace(' ', '').replace('卡号：', '')
                print(id_1)
                banks = d.xpath('//*[@resource-id="app"]/android.view.View[9]/android.view.View[2]')
                bank = banks.text.replace(' ', '').replace('银行：', '')
                print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人黑\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210406202023635538', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square='', address='https://sdzyczswxggh.com/home/<USER>', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(30)
            return 1
        if len(d.xpath('//android.widget.ListView/android.view.View[1]/android.view.View[2]').all()) != 0:
            d.sleep(4)
            names = d.xpath('//android.widget.ListView/android.view.View[1]/android.view.View[2]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//android.widget.ListView/android.view.View[3]/android.view.View[2]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//android.widget.ListView/android.view.View[7]/android.view.View[2]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人黑\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210406202023635538', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square=square, address='https://sdzyczswxggh.com/home/<USER>', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1
        if len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) != 0:
            d.sleep(4)
            names = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[5]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人黑\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210406202023635538', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square=square, address='https://sdzyczswxggh.com/home/<USER>', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1
        if len(d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]').all()) != 0:
            d.sleep(4)
            names = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[7]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[5]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//android.webkit.WebView/android.view.View[9]/android.view.View[1]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人黑\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210406202023635538', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square=square, address='https://sdzyczswxggh.com/home/<USER>', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1
        if len(d.xpath('//*[@resource-id="show2"]/android.view.View[3]/android.view.View[1]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]').all()) != 0:
            d.sleep(4)
            if len(d.xpath('//*[@resource-id="placeholder"]').all()) == 1:
                d.xpath('//*[@resource-id="placeholder"]').click()
                d.sleep(1)
                d.clear_text()
                d.send_keys("詹明德")
                d.sleep(1)
                d.xpath('//*[@resource-id="popup-dialog"]/android.view.View[4]/android.view.View[1]').click()
            d.sleep(4)
            names = d.xpath('//*[@resource-id="show2"]/android.view.View[3]/android.view.View[1]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="#number"]/android.view.View[1]/android.view.View[1]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="show2"]/android.view.View[4]/android.view.View[1]/android.widget.EditText[1]/android.view.View[1]/android.view.View[1]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\澳门巴黎人黑\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210406202023635538', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square=square, address='https://sdzyczswxggh.com/home/<USER>', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1
        if len(d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]').all()) != 0:
            d.sleep(4)
            names = d.xpath('//*[@resource-id="content"]/android.view.View[7]/android.view.View[3]')
            name = names.text.replace(' ', '').replace('姓名：', '')
            print(name)
            ids = d.xpath('//*[@resource-id="content"]/android.view.View[6]/android.view.View[3]')
            id_1 = ids.text.replace(' ', '').replace('卡号：', '')
            print(id_1)
            banks = d.xpath('//*[@resource-id="content"]/android.view.View[5]/android.view.View[3]')
            bank = banks.text.replace(' ', '').replace('银行：', '')
            print(bank)
            image = d.screenshot()
            image_path = "D:\\石溪科技银行卡\\采集卡号清单\\太阳城\\" + bank + ',' + name + ',' + id_1 + ".jpg"
            image.save(image_path)
            d.xpath('//*[@resource-id="com.vivo.browser:id/rl_container_view"]/android.widget.LinearLayout[1]').click()
            d.sleep(1)
            d.xpath('//*[@resource-id="com.vivo.browser:id/copy_icon"]').click()
            square = d.clipboard
            newest_at = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            payment.post_web(card_number=id_1, serial_number='20210406202023635538', dname=name,
                             brandname='澳门巴黎人', newest_at=newest_at,
                             square=square, address='https://sdzyczswxggh.com/home/<USER>', bankname=bank,
                             image_path=image_path)
            # lis.append([bank, name, id_1, image_path])
            print("保存成功")
            d.press("back")
            d.sleep(1)
            d.press("back")
            d.sleep(30)
            return 1
        while len(d.xpath('//*[@text="下一步"]').all()) == 0:
            d.press("back")
            d.sleep(4)
        return 1


    @staticmethod
    def sign_out():
        d.xpath('//*[@text="我"]').click()
        d.sleep(1)
        d.xpath('//*[@text="安全退出"]').click()

    def run(self):
        d = self.d
        amblrh_collect.reset(d)
        ip = switchover_IP(d)
        ip.run()
        d.watcher.when(xpath='//*[@resource-id="android:id/alertTitle"]').when(
            xpath='//*[@resource-id="android:id/button1"]').click()
        d.watcher.when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_normal_title_layout"]').when(
            xpath='//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
        d.watcher.when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[1]/android.view.View[1]').when(
            xpath='//*[@resource-id="layui-layer1"]/android.view.View[4]/android.view.View[1]/android.view.View[1]').click()
        d.watcher.when(xpath='//*[@resource-id="com.tx.app.threetxn.tyce:id/iv_red_packet"]').when(
            xpath='//*[@resource-id="com.tx.app.threetxn.tyce:id/iv_red_cancel"]').click()
        # d.watcher.when(xpath='//*[@text="Close"]').click()
        d.watcher.start()
        d.app_start('com.aweproject.app.ylbb', stop=True)

        d.sleep(5)
        if len(d.xpath('//*[@text="登录"]').all()) == 1:
            d.xpath('//*[@text="登录"]').click()
            d.sleep(3)
            amblrh_collect.login(d)
        amblrh_collect.collect(d)
        # amdc_collect.sign_out()
        d.watcher.remove()


if __name__ == '__main__':
    id = "c265b801"
    # id = "CQSCLFMBQGQK6HIR"
    d = u2.connect(id)
    d.watcher.when(xpath='//*[@resource-id="android:id/alertTitle"]').when(xpath='//*[@resource-id="android:id/button1"]').click()
    d.watcher.when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_normal_title_layout"]').when(xpath='//*[@resource-id="com.vivo.browser:id/dialog_single_button"]').click()
    d.watcher.when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[1]/android.view.View[1]').when(xpath='//*[@resource-id="layui-layer1"]/android.view.View[4]/android.view.View[1]/android.view.View[1]').click()
    d.watcher.when(xpath='//*[@resource-id="com.tx.app.threetxn.tyce:id/iv_red_packet"]').when(
        xpath='//*[@resource-id="com.tx.app.threetxn.tyce:id/iv_red_cancel"]').click()
    d.watcher.when(xpath='//*[@text="立即更新"]').click()
    d.watcher.start()
    # d.app_start('com.aweproject.app.ylbb', stop=True)
    #
    # d.sleep(5)
    if len(d.xpath('//*[@text="登录"]').all()) == 1:
        d.xpath('//*[@text="登录"]').click()
        d.sleep(3)
        amblrh_collect.login(d)
    amblrh_collect.collect(d)
    # amdc_collect.sign_out()
    d.watcher.remove()
    ip = switchover_IP(d)
    ip.run()
    # amdc_collect.data_write('D:\\石溪科技银行卡\\采集卡号清单\\大奖直播\\脚本采集\\银行卡信息_c265b801.xlsx', lis)