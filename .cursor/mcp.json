{"mcpServers": {"context7-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "4f26bc15-2afc-4631-8579-a1a21e7f7928"], "env": {}}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@xinzhongyouhai/mcp-sequentialthinking-tools", "--key", "4f26bc15-2afc-4631-8579-a1a21e7f7928"], "env": {}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}}}